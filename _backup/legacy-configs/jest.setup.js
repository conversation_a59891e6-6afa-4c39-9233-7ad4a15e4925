// Jest 全局设置文件

// 扩展 Jest 匹配器
import '@testing-library/jest-dom';

// 模拟浏览器 API
Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: jest.fn().mockImplementation(query => ({
        matches: false,
        media: query,
        onchange: null,
        addListener: jest.fn(), // deprecated
        removeListener: jest.fn(), // deprecated
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn(),
    })),
});

// 模拟 ResizeObserver
global.ResizeObserver = jest.fn().mockImplementation(() => ({
    observe: jest.fn(),
    unobserve: jest.fn(),
    disconnect: jest.fn(),
}));

// 模拟 IntersectionObserver
global.IntersectionObserver = jest.fn().mockImplementation(() => ({
    observe: jest.fn(),
    unobserve: jest.fn(),
    disconnect: jest.fn(),
}));

// 模拟 MutationObserver
global.MutationObserver = jest.fn().mockImplementation(() => ({
    observe: jest.fn(),
    disconnect: jest.fn(),
    takeRecords: jest.fn(),
}));

// 模拟 fetch API
global.fetch = jest.fn();

// 模拟 localStorage
const localStorageMock = {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    clear: jest.fn(),
};
global.localStorage = localStorageMock;

// 模拟 sessionStorage
const sessionStorageMock = {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    clear: jest.fn(),
};
global.sessionStorage = sessionStorageMock;

// 模拟 URL 构造函数
global.URL.createObjectURL = jest.fn();
global.URL.revokeObjectURL = jest.fn();

// 模拟 CustomEvent
global.CustomEvent = class CustomEvent extends Event {
    constructor(event, params) {
        super(event, params);
        this.detail = params && params.detail || {};
    }
};

// 设置测试超时
jest.setTimeout(10000);

// 全局错误处理
process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// 控制台警告过滤
const originalWarn = console.warn;
console.warn = (...args) => {
    // 过滤掉一些已知的无害警告
    const message = args[0];
    if (
        typeof message === 'string' &&
        (
            message.includes('React.createFactory') ||
            message.includes('componentWillReceiveProps') ||
            message.includes('componentWillMount')
        )
    ) {
        return;
    }
    originalWarn.apply(console, args);
};

// 测试环境标识
process.env.NODE_ENV = 'test';