#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * 递归查找指定目录下的所有文件
 */
function findFiles(dir, pattern, results = []) {
    if (!fs.existsSync(dir)) return results;

    const files = fs.readdirSync(dir);

    for (const file of files) {
        const filePath = path.join(dir, file);

        // 跳过 node_modules 和 dist 目录
        if (file === 'node_modules' || file === 'dist' || file === '.git') {
            continue;
        }

        try {
            const stat = fs.statSync(filePath);

            if (stat.isDirectory()) {
                findFiles(filePath, pattern, results);
            } else if (pattern.test(file)) {
                results.push(filePath);
            }
        } catch (error) {
            // 忽略无法访问的文件（如损坏的符号链接）
            continue;
        }
    }

    return results;
}

/**
 * 检查单个包的测试覆盖率
 */
function checkPackageCoverage(packagePath) {
    const packageName = path.basename(packagePath);

    // 查找所有 TypeScript 源文件（排除测试文件和类型定义文件）
    const sourceFiles = findFiles(packagePath, /\.ts$/)
        .filter(file =>
            !file.includes('test') &&
            !file.includes('spec') &&
            !file.endsWith('.d.ts') &&
            !file.includes('node_modules') &&
            !file.includes('dist') &&
            file.includes('/src/')
        );

    // 查找所有测试文件
    const testFiles = findFiles(packagePath, /\.(test|spec)\.ts$/)
        .filter(file => !file.includes('node_modules'));

    const sourceCount = sourceFiles.length;
    const testCount = testFiles.length;

    console.log(`📦 检查包: ${packageName}`);

    if (sourceCount === 0) {
        console.log('   ⚠️  没有源文件，跳过');
        return null;
    }

    console.log(`   📄 源文件: ${sourceCount} 个`);
    console.log(`   🧪 测试文件: ${testCount} 个`);

    if (testCount === 0) {
        console.log('   ❌ 没有测试文件');
        return { name: packageName, coverage: 0, hasTests: false };
    }

    // 简单估算覆盖率（测试文件数 / 源文件数 * 100，最大100%）
    const estimatedCoverage = Math.min(Math.round((testCount / sourceCount) * 100), 100);
    console.log(`   📊 估算覆盖率: ${estimatedCoverage}%`);

    // 显示详细的测试文件信息
    if (testCount > 0) {
        console.log('   📋 测试文件列表:');
        testFiles.slice(0, 5).forEach(file => {
            const relativePath = path.relative(packagePath, file);
            console.log(`      - ${relativePath}`);
        });
        if (testFiles.length > 5) {
            console.log(`      ... 还有 ${testFiles.length - 5} 个测试文件`);
        }
    }

    return {
        name: packageName,
        coverage: estimatedCoverage,
        hasTests: testCount > 0,
        sourceCount,
        testCount
    };
}

/**
 * 主函数
 */
function main() {
    console.log('🧪 开始运行改进的测试覆盖率检查...\n');

    const packagesDir = './packages';

    if (!fs.existsSync(packagesDir)) {
        console.error('❌ packages 目录不存在');
        process.exit(1);
    }

    const packages = fs.readdirSync(packagesDir)
        .filter(name => {
            const packagePath = path.join(packagesDir, name);
            return fs.statSync(packagePath).isDirectory();
        });

    const results = [];

    for (const packageName of packages) {
        const packagePath = path.join(packagesDir, packageName);
        const result = checkPackageCoverage(packagePath);

        if (result) {
            results.push(result);
        }

        console.log(''); // 空行分隔
    }

    // 生成汇总报告
    console.log('\n📋 测试覆盖率汇总报告');
    console.log('==================================================');

    let totalCoverage = 0;
    let packagesWithTests = 0;
    let totalSourceFiles = 0;
    let totalTestFiles = 0;

    for (const result of results) {
        const status = result.hasTests ? '✅' : '❌';
        console.log(`${status} ${result.name.padEnd(20)} ${result.coverage}%`);

        if (result.hasTests) {
            totalCoverage += result.coverage;
            packagesWithTests++;
        }

        totalSourceFiles += result.sourceCount;
        totalTestFiles += result.testCount;
    }

    console.log('==================================================');
    console.log(`📊 总计: ${results.length} 个包`);
    console.log(`✅ 有测试: ${packagesWithTests} 个包`);
    console.log(`❌ 无测试: ${results.length - packagesWithTests} 个包`);
    console.log(`📄 总源文件: ${totalSourceFiles} 个`);
    console.log(`🧪 总测试文件: ${totalTestFiles} 个`);

    if (packagesWithTests > 0) {
        const avgCoverage = Math.round(totalCoverage / packagesWithTests);
        console.log(`📈 平均覆盖率: ${avgCoverage}%`);
    }

    // 显示需要改进的包
    const needsImprovement = results.filter(r => !r.hasTests || r.coverage < 100);

    if (needsImprovement.length > 0) {
        console.log('\n🎯 需要补充测试的包:');
        for (const result of needsImprovement) {
            if (!result.hasTests) {
                console.log(`   ❌ ${result.name} - 缺少测试文件`);
            } else if (result.coverage < 100) {
                console.log(`   📈 ${result.name} - 覆盖率 ${result.coverage}% (需要达到 100%)`);
            }
        }
    } else {
        console.log('\n🎉 所有包都达到了100%测试覆盖率！');
    }

    // 返回退出码
    const allPackagesHave100Coverage = results.every(r => r.coverage === 100);
    process.exit(allPackagesHave100Coverage ? 0 : 1);
}

if (require.main === module) {
    main();
}