# Micro-Core 项目优化设计文档

## 概述

本设计文档基于需求文档，为 Micro-Core 项目的全面重构优化提供详细的技术设计方案。设计遵循微内核架构原则，通过模块化拆分、代码去重、性能优化等手段，实现项目的技术债务清零和开发体验提升。

## 架构设计

### 整体架构原则

1. **单一职责原则**: 每个文件和模块都有明确的单一职责
2. **依赖倒置原则**: 高层模块不依赖低层模块，都依赖抽象
3. **开闭原则**: 对扩展开放，对修改封闭
4. **接口隔离原则**: 使用多个专门的接口，而不是单一的总接口
5. **最小知识原则**: 模块间的耦合度最小化

### 分层架构设计

```
Level 1: shared/core (基础设施层)
├── errors/           # 统一错误处理
├── logging/          # 日志系统
└── validation/       # 数据验证

Level 2: shared/utils, shared/types, shared/constants (工具层)
├── utils/            # 工具函数库
├── types/            # 类型定义
└── constants/        # 常量定义

Level 3: shared/helpers, shared/adapters, shared/testing (辅助层)
├── helpers/          # 辅助工具
├── adapters/         # 适配器基础设施
└── testing/          # 测试工具

Level 4: core, plugins, builders, sidecar (核心层)
├── core/             # 微前端内核
├── plugins/          # 插件系统
├── builders/         # 构建工具
└── sidecar/          # Sidecar模式

Level 5: adapters (适配器层)
└── adapter-*/        # 各框架适配器

Level 6: applications (应用层)
└── apps/             # 示例应用
```

## 组件和接口设计

### 1. 核心模块重构设计

#### 1.1 MicroCore 主类重构

**当前问题**: `packages/core/src/index.ts` 职责混合，151行代码包含导出和类定义

**设计方案**:

```typescript
// packages/core/src/index.ts (纯导出文件)
export { MicroCore } from './micro-core';
export * from './exports';
export * from './legacy/types';
export * from './legacy/utils';

// packages/core/src/micro-core.ts (主类定义)
export class MicroCore {
    private readonly kernel: MicroCoreKernel;
    private readonly eventBus: EventBus;
    
    constructor(options: MicroCoreOptions = {}) {
        this.kernel = new MicroCoreKernel(options);
        this.eventBus = new EventBus();
    }
    
    // 委托方法到内核
    registerApplication = this.kernel.registerApplication.bind(this.kernel);
    unregisterApplication = this.kernel.unregisterApplication.bind(this.kernel);
    start = this.kernel.start.bind(this.kernel);
    stop = this.kernel.stop.bind(this.kernel);
    use = this.kernel.use.bind(this.kernel);
}

// packages/core/src/exports/index.ts (分类导出)
export * from './runtime';
export * from './communication';
export * from './types';
```

#### 1.2 内核文件拆分设计

**当前问题**: `packages/core/src/runtime/kernel.ts` 279行，职责过多

**设计方案**:

```typescript
// packages/core/src/runtime/kernel/index.ts (内核主类)
export class MicroCoreKernel {
    private readonly appManager: AppManager;
    private readonly pluginManager: PluginManager;
    private readonly lifecycleManager: LifecycleManager;
    private readonly routeListener: RouteListener;
    
    constructor(options: MicroCoreOptions = {}) {
        this.appManager = new AppManager(options);
        this.pluginManager = new PluginManager(options);
        this.lifecycleManager = new LifecycleManager(options);
        this.routeListener = new RouteListener(options);
    }
}

// packages/core/src/runtime/kernel/managers/app-manager.ts
export class AppManager {
    private apps = new Map<string, AppInstance>();
    
    register(config: AppConfig): void { /* 应用注册逻辑 */ }
    unregister(name: string): void { /* 应用注销逻辑 */ }
    getApp(name: string): AppInstance | undefined { /* 获取应用 */ }
}

// packages/core/src/runtime/kernel/managers/plugin-manager.ts
export class PluginManager {
    private plugins = new Map<string, Plugin>();
    
    use(plugin: Plugin): void { /* 插件注册逻辑 */ }
    remove(name: string): void { /* 插件移除逻辑 */ }
    getPlugin(name: string): Plugin | undefined { /* 获取插件 */ }
}

// packages/core/src/runtime/kernel/managers/lifecycle-manager.ts
export class LifecycleManager {
    private eventBus: EventBus;
    
    constructor(eventBus: EventBus) {
        this.eventBus = eventBus;
    }
    
    start(): Promise<void> { /* 启动逻辑 */ }
    stop(): Promise<void> { /* 停止逻辑 */ }
}
```

### 2. 共享模块重构设计

#### 2.1 工具函数模块化设计

**当前问题**: `packages/shared/utils/src/` 40+文件组织混乱

**设计方案**:

```typescript
// packages/shared/utils/src/index.ts (主导出)
export * as TypeCheck from './type-check';
export * as DataStructures from './data-structures';
export * as AsyncUtils from './async';
export * as DOMUtils from './dom';
export * as NetworkUtils from './networking';
export * as LoggerUtils from './logging';

// 向后兼容的直接导出
export {
    isObject, isFunction, isString, isNumber, isBoolean,
    isArray, isPromise, isEmpty
} from './type-check';

// packages/shared/utils/src/type-check/index.ts
export { isObject, isFunction, isString } from './primitives';
export { isArray, isPromise } from './collections';
export { isEmpty } from './objects';

// packages/shared/utils/src/type-check/primitives.ts
export function isObject(value: unknown): value is object {
    return value !== null && typeof value === 'object';
}

export function isFunction(value: unknown): value is Function {
    return typeof value === 'function';
}

export function isString(value: unknown): value is string {
    return typeof value === 'string';
}
```

#### 2.2 核心基础设施设计

**设计方案**:

```typescript
// packages/shared/core/errors/index.ts
export class MicroCoreError extends Error {
    constructor(
        public code: string,
        message: string,
        public context?: any,
        public cause?: Error
    ) {
        super(message);
        this.name = 'MicroCoreError';
    }
}

export const ERROR_CODES = {
    INVALID_ARGUMENT: 'INVALID_ARGUMENT',
    OPERATION_FAILED: 'OPERATION_FAILED',
    RESOURCE_NOT_FOUND: 'RESOURCE_NOT_FOUND',
    PERMISSION_DENIED: 'PERMISSION_DENIED'
} as const;

// packages/shared/core/logging/index.ts
export interface Logger {
    debug(message: string, ...args: any[]): void;
    info(message: string, ...args: any[]): void;
    warn(message: string, ...args: any[]): void;
    error(message: string, ...args: any[]): void;
}

export function createLogger(name: string): Logger {
    return {
        debug: (message, ...args) => console.debug(`[${name}] ${message}`, ...args),
        info: (message, ...args) => console.info(`[${name}] ${message}`, ...args),
        warn: (message, ...args) => console.warn(`[${name}] ${message}`, ...args),
        error: (message, ...args) => console.error(`[${name}] ${message}`, ...args)
    };
}
```

### 3. 适配器标准化设计

#### 3.1 基础适配器抽象类设计

```typescript
// packages/shared/adapters/base/adapter.ts
export abstract class BaseAdapter<TConfig = any, TInstance = any> {
    protected readonly name: string;
    protected readonly version: string;
    protected config: TConfig;
    protected instance: TInstance | null = null;
    protected isBootstrapped = false;
    protected isMounted = false;
    protected logger: Logger;

    constructor(name: string, version: string, config: TConfig) {
        this.name = name;
        this.version = version;
        this.config = this.validateConfig(config);
        this.logger = createLogger(`[${name}Adapter]`);
    }

    // 抽象方法 - 必须由子类实现
    abstract validateConfig(config: TConfig): TConfig;
    abstract bootstrap(props?: any): Promise<void>;
    abstract mount(container: Element, props?: any): Promise<void>;
    abstract unmount(props?: any): Promise<void>;

    // 通用方法 - 基类提供默认实现
    getName(): string { return this.name; }
    getVersion(): string { return this.version; }
    getConfig(): TConfig { return { ...this.config }; }
    isReady(): boolean { return this.isBootstrapped && this.isMounted; }

    protected handleError(error: Error, context: string): never {
        const microError = new MicroCoreError(
            `${this.name}:${context}`,
            error.message,
            { originalError: error, config: this.config }
        );
        this.logger.error(`Error in ${context}:`, microError);
        throw microError;
    }
}
```

#### 3.2 适配器工具函数共享设计

```typescript
// packages/shared/adapters/utils/common.ts
export abstract class AdapterUtils<TFramework = any> {
    protected framework: TFramework;
    protected logger: Logger;

    constructor(framework: TFramework, adapterName: string) {
        this.framework = framework;
        this.logger = createLogger(`[${adapterName}Utils]`);
    }

    // 通用方法 - 消除100%重复
    createContainer(selector: string | Element): Element {
        if (typeof selector === 'string') {
            const element = document.querySelector(selector);
            if (!element) {
                throw new MicroCoreError(
                    ERROR_CODES.RESOURCE_NOT_FOUND,
                    `Container not found: ${selector}`
                );
            }
            return element;
        }
        return selector;
    }

    cleanupContainer(container: Element): void {
        container.innerHTML = '';
        container.removeAttribute('data-micro-app');
        this.logger.debug('Container cleaned up');
    }

    getVersion(): string {
        return this.framework?.version || 'unknown';
    }

    // 抽象方法 - 各框架实现
    abstract extractComponent(entry: any): any;
    abstract isComponent(obj: any): boolean;
    abstract validateConfig(config: any): any;
}

// packages/adapters/adapter-react/src/utils.ts
export class ReactUtils extends AdapterUtils<typeof React> {
    constructor() {
        super(React, 'React');
    }

    extractComponent(entry: any): ComponentType {
        if (this.isComponent(entry)) {
            return entry;
        }
        if (entry && entry.default && this.isComponent(entry.default)) {
            return entry.default;
        }
        throw new MicroCoreError(
            ERROR_CODES.INVALID_ARGUMENT,
            'Invalid React component entry'
        );
    }

    isComponent(obj: any): obj is ComponentType {
        return typeof obj === 'function' || 
               (obj && typeof obj === 'object' && obj.$$typeof);
    }

    validateConfig(config: ReactAppConfig): ReactAppConfig {
        if (!config.name || typeof config.name !== 'string') {
            throw new MicroCoreError(
                ERROR_CODES.INVALID_ARGUMENT,
                'App name is required and must be a string'
            );
        }
        return config;
    }
}
```

#### 3.3 统一适配器目录结构

```
packages/adapters/adapter-[framework]/
├── src/
│   ├── index.ts              # 主导出文件
│   ├── adapter.ts            # 适配器主类
│   ├── lifecycle/            # 生命周期管理
│   │   ├── index.ts
│   │   ├── bootstrap.ts
│   │   ├── mount.ts
│   │   └── unmount.ts
│   ├── utils/                # 适配器工具
│   │   ├── index.ts
│   │   ├── component.ts
│   │   └── validation.ts
│   ├── types/                # 类型定义
│   │   ├── index.ts
│   │   ├── config.ts
│   │   └── lifecycle.ts
│   └── constants/            # 常量定义
│       └── index.ts
├── __tests__/                # 测试文件
│   ├── unit/
│   ├── integration/
│   └── fixtures/
├── examples/                 # 使用示例
├── package.json              # 包配置
├── tsconfig.json             # TypeScript配置
├── tsup.config.ts            # 构建配置（统一使用tsup）
├── vitest.config.ts          # 测试配置
└── README.md                 # 文档
```

### 4. 测试结构统一设计

#### 4.1 测试目录标准化

```
tests/                        # 项目级测试目录
├── unit/                     # 单元测试
├── integration/              # 集成测试
├── e2e/                      # 端到端测试
├── performance/              # 性能测试
├── fixtures/                 # 测试数据
├── helpers/                  # 测试辅助工具
└── setup/                    # 测试设置

packages/[package-name]/__tests__/  # 包级测试目录
├── unit/                     # 单元测试
├── integration/              # 集成测试
├── fixtures/                 # 测试数据
└── helpers/                  # 测试辅助
```

#### 4.2 测试配置统一

```typescript
// vitest.config.base.ts (共享测试配置)
import { defineConfig } from 'vitest/config';

export function createVitestConfig(options: any = {}) {
    return defineConfig({
        test: {
            environment: 'jsdom',
            globals: true,
            setupFiles: ['./tests/setup.ts'],
            coverage: {
                provider: 'v8',
                reporter: ['text', 'html', 'lcov', 'json'],
                reportsDirectory: './coverage',
                thresholds: {
                    global: {
                        branches: 90,
                        functions: 90,
                        lines: 90,
                        statements: 90
                    }
                },
                exclude: [
                    '**/__tests__/**',
                    '**/node_modules/**',
                    '**/dist/**',
                    '**/*.config.*',
                    '**/legacy/**'
                ]
            },
            ...options
        }
    });
}
```

## 数据模型设计

### 1. 类型定义统一设计

```typescript
// packages/shared/types/src/base.ts
export interface BaseAppConfig {
    name: string;
    version?: string;
    container: string | Element;
    activeWhen: string | ((location: Location) => boolean);
    customProps?: Record<string, any>;
}

export interface BaseLifecycleHooks {
    beforeBootstrap?: (props?: any) => Promise<void>;
    afterBootstrap?: (props?: any) => Promise<void>;
    beforeMount?: (props?: any) => Promise<void>;
    afterMount?: (props?: any) => Promise<void>;
    beforeUnmount?: (props?: any) => Promise<void>;
    afterUnmount?: (props?: any) => Promise<void>;
}

export interface BaseAdapterConfig extends BaseAppConfig {
    sandbox?: boolean | SandboxOptions;
    loader?: LoaderOptions;
    errorBoundary?: boolean;
}

// packages/adapters/adapter-react/src/types.ts
export interface ReactAppConfig extends BaseAdapterConfig {
    reactVersion?: string;
    strictMode?: boolean;
    errorBoundary?: boolean | ReactErrorBoundaryOptions;
}

export interface ReactLifecycleHooks extends BaseLifecycleHooks {
    onError?: (error: Error, errorInfo: ErrorInfo) => void;
    onRender?: (component: ComponentType) => void;
}
```

### 2. 配置管理设计

```typescript
// packages/shared/configs/base.ts
export interface BuildConfig {
    entry: string[];
    format: ('cjs' | 'esm')[];
    dts: boolean;
    clean: boolean;
    sourcemap: boolean;
    minify: boolean;
    external?: string[];
}

export function createTsupConfig(options: Partial<BuildConfig> = {}): BuildConfig {
    return {
        entry: ['src/index.ts'],
        format: ['cjs', 'esm'],
        dts: true,
        clean: true,
        sourcemap: true,
        minify: false,
        ...options
    };
}

// packages/adapters/adapter-react/tsup.config.ts
import { createTsupConfig } from '@micro-core/shared/configs';

export default createTsupConfig({
    external: ['react', 'react-dom']
});
```

## 错误处理设计

### 1. 统一错误处理系统

```typescript
// packages/shared/core/errors/index.ts
export class MicroCoreError extends Error {
    constructor(
        public code: string,
        message: string,
        public context?: any,
        public cause?: Error
    ) {
        super(message);
        this.name = 'MicroCoreError';
        
        // 保持错误堆栈
        if (Error.captureStackTrace) {
            Error.captureStackTrace(this, MicroCoreError);
        }
    }

    toJSON() {
        return {
            name: this.name,
            code: this.code,
            message: this.message,
            context: this.context,
            stack: this.stack
        };
    }
}

export const ERROR_CODES = {
    // 参数错误
    INVALID_ARGUMENT: 'INVALID_ARGUMENT',
    MISSING_REQUIRED_PARAM: 'MISSING_REQUIRED_PARAM',
    
    // 操作错误
    OPERATION_FAILED: 'OPERATION_FAILED',
    INITIALIZATION_FAILED: 'INITIALIZATION_FAILED',
    
    // 资源错误
    RESOURCE_NOT_FOUND: 'RESOURCE_NOT_FOUND',
    RESOURCE_LOAD_FAILED: 'RESOURCE_LOAD_FAILED',
    
    // 权限错误
    PERMISSION_DENIED: 'PERMISSION_DENIED',
    UNAUTHORIZED: 'UNAUTHORIZED'
} as const;

export type ErrorCode = typeof ERROR_CODES[keyof typeof ERROR_CODES];
```

### 2. 错误边界设计

```typescript
// packages/shared/core/errors/boundary.ts
export interface ErrorBoundaryOptions {
    fallback?: ComponentType<{ error: Error }>;
    onError?: (error: Error, errorInfo: any) => void;
    isolate?: boolean;
}

export class ErrorBoundary {
    private options: ErrorBoundaryOptions;
    private logger: Logger;

    constructor(options: ErrorBoundaryOptions = {}) {
        this.options = options;
        this.logger = createLogger('[ErrorBoundary]');
    }

    handleError(error: Error, context: string): void {
        this.logger.error(`Error caught in ${context}:`, error);
        
        if (this.options.onError) {
            try {
                this.options.onError(error, { context });
            } catch (handlerError) {
                this.logger.error('Error in error handler:', handlerError);
            }
        }
    }
}
```

## 测试策略设计

### 1. 测试覆盖率目标

- **单元测试覆盖率**: ≥95% (核心模块)
- **集成测试覆盖率**: ≥85% (适配器和插件)
- **端到端测试覆盖率**: ≥80% (关键用户流程)
- **整体测试覆盖率**: ≥90%

### 2. 测试分层策略

```typescript
// 单元测试模板
describe('ModuleUnderTest', () => {
    let instance: ModuleUnderTest;

    beforeEach(() => {
        instance = new ModuleUnderTest();
    });

    afterEach(() => {
        instance?.destroy?.();
        vi.restoreAllMocks();
    });

    describe('constructor', () => {
        it('should create instance with valid config', () => {
            expect(instance).toBeInstanceOf(ModuleUnderTest);
        });

        it('should throw error with invalid config', () => {
            expect(() => new ModuleUnderTest(null as any)).toThrow();
        });
    });

    describe('public methods', () => {
        it('should handle normal cases', () => {
            const result = instance.publicMethod('valid-input');
            expect(result).toBeDefined();
        });

        it('should handle edge cases', () => {
            expect(() => instance.publicMethod('')).toThrow();
            expect(instance.publicMethod(null as any)).toBeNull();
        });
    });
});
```

### 3. 性能测试设计

```typescript
// packages/shared/testing/performance.ts
export interface PerformanceMetrics {
    loadTime: number;
    memoryUsage: number;
    bundleSize: number;
    renderTime: number;
}

export class PerformanceMonitor {
    private metrics: PerformanceMetrics[] = [];

    startMeasurement(): string {
        const id = `perf-${Date.now()}`;
        performance.mark(`${id}-start`);
        return id;
    }

    endMeasurement(id: string): number {
        performance.mark(`${id}-end`);
        performance.measure(id, `${id}-start`, `${id}-end`);
        const measure = performance.getEntriesByName(id)[0];
        return measure.duration;
    }

    measureMemoryUsage(): number {
        if ('memory' in performance) {
            return (performance as any).memory.usedJSHeapSize;
        }
        return 0;
    }
}
```

## 性能优化设计

### 1. 构建性能优化

- **并行构建**: 使用 Turbo 优化 monorepo 构建
- **增量构建**: 只构建变更的包
- **缓存策略**: 充分利用构建缓存
- **依赖优化**: 减少重复依赖安装

### 2. 运行时性能优化

- **延迟加载**: 非关键模块延迟初始化
- **Tree Shaking**: 优化导入导出结构
- **代码分割**: 按需加载功能模块
- **内存管理**: 及时清理不用的资源

### 3. 包体积优化

- **共享依赖**: 提取公共依赖到 shared 包
- **压缩优化**: 使用高效的压缩算法
- **模块拆分**: 细粒度的模块划分
- **外部化依赖**: 将大型依赖外部化

## 质量保证设计

### 1. 代码质量监控

```typescript
// .jscpd.json (代码重复率监控)
{
  "threshold": 1,
  "reporters": ["html", "console", "json"],
  "ignore": ["**/__tests__/**", "**/node_modules/**", "**/dist/**"],
  "format": ["typescript", "javascript"],
  "output": "./reports/jscpd"
}
```

### 2. 自动化质量检查

```yaml
# CI/CD 质量门禁
quality-gates:
  - code-duplication: <1%
  - test-coverage: ≥90%
  - build-time: <3min
  - bundle-size: <target
  - eslint-errors: 0
  - typescript-errors: 0
```

### 3. 持续监控

- **性能监控**: 构建时间、包体积、运行时性能
- **质量监控**: 代码重复率、测试覆盖率、错误率
- **依赖监控**: 安全漏洞、版本更新、许可证合规

这个设计文档为 Micro-Core 项目的全面重构提供了详细的技术方案，确保重构过程的系统性和可执行性。