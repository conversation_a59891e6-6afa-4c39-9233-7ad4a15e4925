#!/usr/bin/env node

/**
 * 仅测试核心模块
 * 用于快速验证核心功能
 */

import { execSync } from 'child_process';
import fs from 'fs';

console.log('🧪 测试核心模块...\n');

try {
    // 检查核心测试文件
    const coreTestDir = 'packages/core/__tests__';
    if (!fs.existsSync(coreTestDir)) {
        console.log('❌ 核心测试目录不存在');
        process.exit(1);
    }

    const testFiles = fs.readdirSync(coreTestDir).filter(f => f.endsWith('.test.ts'));
    console.log(`发现 ${testFiles.length} 个核心测试文件:`);
    testFiles.forEach(file => console.log(`  - ${file}`));
    console.log('');

    // 逐个运行测试文件
    for (const testFile of testFiles) {
        const testPath = `${coreTestDir}/${testFile}`;
        console.log(`🔍 运行 ${testFile}...`);

        try {
            const result = execSync(`pnpm vitest run ${testPath} --reporter=basic --no-coverage`, {
                encoding: 'utf8',
                stdio: 'pipe',
                timeout: 30000
            });

            console.log(`✅ ${testFile} 通过`);
        } catch (error) {
            console.log(`❌ ${testFile} 失败:`);
            if (error.stdout) {
                console.log(error.stdout);
            }
            if (error.stderr) {
                console.log(error.stderr);
            }
        }
        console.log('');
    }

    console.log('🎉 核心模块测试完成！');

} catch (error) {
    console.log('❌ 测试运行失败:', error.message);
    process.exit(1);
}