#!/usr/bin/env node

/**
 * 覆盖率分析引擎
 * 
 * 此脚本用于分析各个子包的测试覆盖率，生成详细的覆盖率报告，
 * 并识别测试覆盖率的差距。
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 导入模块
const { analyzeCoverageReport } = require('./coverage/analyzer');
const { generateGapReport } = require('./coverage/gap-reporter');
const { generateTrendReport } = require('./coverage/trend-reporter');
const { generateHtmlReport } = require('./coverage/html-reporter');
const { identifyTestGaps } = require('./coverage/gap-identifier');

// 配置
const PACKAGES_DIR = path.resolve(__dirname, '../packages');
const OUTPUT_DIR = path.resolve(__dirname, '../reports/coverage-analysis');
const COVERAGE_DIR = path.resolve(__dirname, '../coverage');

// 确保输出目录存在
if (!fs.existsSync(OUTPUT_DIR)) {
    fs.mkdirSync(OUTPUT_DIR, { recursive: true });
}

/**
 * 运行测试并生成覆盖率报告
 */
function runTestsWithCoverage() {
    console.log('运行测试并生成覆盖率报告...');

    try {
        // 使用 Vitest 运行测试并生成覆盖率报告
        execSync('pnpm run test:coverage', { stdio: 'inherit' });
        console.log('测试完成，覆盖率报告已生成');
        return true;
    } catch (error) {
        console.error('运行测试时出错:', error.message);
        return false;
    }
}

/**
 * 主函数
 */
async function main() {
    console.log('开始覆盖率分析...');

    // 运行测试并生成覆盖率报告
    const testSuccess = runTestsWithCoverage();
    if (!testSuccess) {
        console.error('测试失败，无法继续分析覆盖率');
        process.exit(1);
    }

    // 分析覆盖率报告
    const packageCoverage = analyzeCoverageReport(COVERAGE_DIR);
    if (!packageCoverage) {
        console.error('无法分析覆盖率报告');
        process.exit(1);
    }

    // 生成差距报告
    const gapReport = generateGapReport(packageCoverage);

    // 识别测试差距
    const testGaps = identifyTestGaps(packageCoverage, PACKAGES_DIR, OUTPUT_DIR);

    // 生成趋势报告
    const trendReport = generateTrendReport(packageCoverage, OUTPUT_DIR);

    // 生成HTML报告
    generateHtmlReport(packageCoverage, gapReport, trendReport, OUTPUT_DIR, PACKAGES_DIR);

    console.log('覆盖率分析完成');
    console.log(`报告已生成到 ${OUTPUT_DIR}`);
}

// 执行主函数
main().catch(error => {
    console.error('覆盖率分析过程中出错:', error);
    process.exit(1);
});