#!/usr/bin/env node

/**
 * 简化的测试运行脚本
 * 逐个运行测试包以避免配置冲突
 */

import { execSync } from 'child_process';
import fs from 'fs';

const testPackages = [
    'packages/core',
    'packages/shared',
    'packages/adapters',
    'packages/plugins'
];

console.log('🧪 开始运行测试套件...\n');

let totalPassed = 0;
let totalFailed = 0;

for (const pkg of testPackages) {
    console.log(`📦 测试 ${pkg}...`);

    try {
        // 检查是否有测试文件
        const testDir = `${pkg}/__tests__`;
        if (!fs.existsSync(testDir)) {
            console.log(`⚠️  ${pkg} 没有测试文件，跳过\n`);
            continue;
        }

        const testFiles = fs.readdirSync(testDir).filter(f => f.endsWith('.test.ts'));
        if (testFiles.length === 0) {
            console.log(`⚠️  ${pkg} 没有测试文件，跳过\n`);
            continue;
        }

        console.log(`   发现 ${testFiles.length} 个测试文件`);

        // 运行测试
        const result = execSync(`pnpm vitest run ${pkg}/__tests__ --reporter=basic`, {
            encoding: 'utf8',
            stdio: 'pipe'
        });

        console.log(`✅ ${pkg} 测试通过`);
        totalPassed++;

    } catch (error) {
        console.log(`❌ ${pkg} 测试失败:`);
        console.log(error.stdout || error.message);
        totalFailed++;
    }

    console.log('');
}

console.log('📊 测试结果汇总:');
console.log(`✅ 通过: ${totalPassed} 个包`);
console.log(`❌ 失败: ${totalFailed} 个包`);

if (totalFailed === 0) {
    console.log('🎉 所有测试都通过了！');
    process.exit(0);
} else {
    console.log('💥 有测试失败，请检查上面的错误信息');
    process.exit(1);
}