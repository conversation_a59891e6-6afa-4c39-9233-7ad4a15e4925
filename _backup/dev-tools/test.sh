#!/bin/bash

# Micro-Core 测试脚本
# 运行所有子包的单元测试

set -e

echo "🧪 开始运行 Micro-Core 单元测试..."
echo "=================================="

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 测试结果统计
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 测试函数
run_test() {
    local package_name=$1
    local package_path=$2
    
    echo ""
    echo -e "${YELLOW}📦 测试 ${package_name}...${NC}"
    echo "-----------------------------------"
    
    if [ -d "$package_path" ] && [ -f "$package_path/package.json" ]; then
        cd "$package_path"
        
        # 检查是否有测试脚本
        if npm run | grep -q "test"; then
            if npm test; then
                echo -e "${GREEN}✅ ${package_name} 测试通过${NC}"
                PASSED_TESTS=$((PASSED_TESTS + 1))
            else
                echo -e "${RED}❌ ${package_name} 测试失败${NC}"
                FAILED_TESTS=$((FAILED_TESTS + 1))
            fi
        else
            echo -e "${YELLOW}⚠️  ${package_name} 没有测试脚本${NC}"
        fi
        
        cd - > /dev/null
    else
        echo -e "${RED}❌ ${package_name} 目录不存在或无效${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
}

# 运行各个包的测试
echo "🔍 开始测试各个子包..."

# 核心包测试
run_test "Core 核心包" "packages/core"

# 共享工具包测试
run_test "Shared 共享工具包" "packages/shared"

# Sidecar 边车模式测试
run_test "Sidecar 边车模式" "packages/sidecar"

# 插件系统测试
run_test "Plugins 插件系统" "packages/plugins"

# 适配器系统测试
run_test "Adapters 适配器系统" "packages/adapters"

# 构建工具适配测试
run_test "Builders 构建工具适配" "packages/builders"

# 输出测试结果
echo ""
echo "=================================="
echo "🏁 测试完成！"
echo ""
echo "📊 测试结果统计："
echo "   总计: $TOTAL_TESTS 个包"
echo -e "   通过: ${GREEN}$PASSED_TESTS${NC} 个包"
echo -e "   失败: ${RED}$FAILED_TESTS${NC} 个包"

if [ $FAILED_TESTS -eq 0 ]; then
    echo ""
    echo -e "${GREEN}🎉 所有测试都通过了！${NC}"
    exit 0
else
    echo ""
    echo -e "${RED}💥 有 $FAILED_TESTS 个包的测试失败${NC}"
    exit 1
fi