# Packages 重构实施手册

## 🎯 快速开始指南

### 前置条件检查
```bash
# 1. 检查 Node.js 版本
node --version  # 需要 >= 18.0.0

# 2. 检查 pnpm 版本  
pnpm --version  # 需要 >= 8.0.0

# 3. 检查当前分支状态
git status      # 确保工作区干净

# 4. 安装必要工具
pnpm add -D madge jscpd cloc dependency-cruiser
```

### 🚀 一键启动重构
```bash
# 执行完整重构流程
./scripts/start-refactor.sh

# 或者分步执行
./scripts/phase1-core-refactor.sh    # 第一阶段：核心重构
./scripts/phase2-structure-opt.sh    # 第二阶段：结构优化  
./scripts/phase3-final-polish.sh     # 第三阶段：完善优化
```

---

## 📋 详细实施检查清单

### 阶段一：核心重构 (5天)

#### Day 1: 环境准备和分析
- [ ] **09:00-10:30** 创建重构分支和安装工具
  ```bash
  git checkout -b feature/packages-deep-refactor
  pnpm add -D madge jscpd cloc dependency-cruiser
  ```

- [ ] **10:30-12:00** 生成当前状态基准报告
  ```bash
  # 依赖关系分析
  madge --image current-deps.png packages/
  
  # 代码重复分析
  jscpd packages/ --reporters html --output ./reports/duplication
  
  # 代码行数统计
  cloc packages/ --by-file --csv --out=./reports/lines-of-code.csv
  
  # 构建时间基准
  time pnpm run build > ./reports/build-time-before.txt 2>&1
  ```

- [ ] **13:00-15:00** 创建 shared 包工具目录结构
  ```bash
  mkdir -p packages/shared/utils/src/{type-check,url,logger,format,config,dom}
  ```

- [ ] **15:00-17:00** 迁移类型检查函数
  - 源文件: `packages/core/src/utils.ts` (125-193行)
  - 目标: `packages/shared/utils/src/type-check/core.ts`
  - 函数: `isObject, isFunction, isString, isNumber, isBoolean, isArray, isPromise, isEmpty`

- [ ] **17:00-18:00** 运行初步测试验证
  ```bash
  pnpm run test
  pnpm run type-check
  ```

#### Day 2: URL和日志工具迁移
- [ ] **09:00-11:00** 迁移 URL 验证函数
  - 源文件: `packages/core/src/utils.ts` (38-45行)
  - 目标: `packages/shared/utils/src/url/index.ts`
  - 删除 core 包中的重复实现

- [ ] **11:00-13:00** 迁移日志工具
  - 源文件: `packages/core/src/utils.ts` (74-115行)
  - 目标: `packages/shared/utils/src/logger/index.ts`
  - 保持 Logger 接口兼容性

- [ ] **14:00-16:00** 更新 core 包导入引用
  ```typescript
  // packages/core/src/utils.ts 更新为使用 shared 包实现
  import { isValidUrl, createLogger, /* ... */ } from '@micro-core/shared/utils';
  ```

- [ ] **16:00-18:00** 添加向后兼容层和废弃警告
  ```typescript
  if (process.env.NODE_ENV === 'development') {
      console.warn('[DEPRECATED] 请从 @micro-core/shared/utils 导入工具函数');
  }
  ```

#### Day 3: 适配器工具函数重构
- [ ] **09:00-11:00** 分析适配器重复代码
  - React: `packages/adapters/adapter-react/src/utils.ts`
  - Vue2/Vue3: 类似的工具函数
  - Angular/Svelte/Solid: 通用逻辑

- [ ] **11:00-13:00** 提取错误格式化函数
  ```bash
  # 创建通用错误格式化工具
  mkdir -p packages/shared/utils/src/format
  # 迁移 formatReactError 等函数到 format/error.ts
  ```

- [ ] **14:00-16:00** 提取配置合并工具
  ```bash
  # 创建通用配置合并工具
  mkdir -p packages/shared/utils/src/config
  # 迁移配置合并逻辑到 config/merge.ts
  ```

- [ ] **16:00-18:00** 提取容器管理函数
  ```bash
  # 创建 DOM 操作工具
  mkdir -p packages/shared/utils/src/dom
  # 迁移容器创建/清理函数到 dom/container.ts
  ```

#### Day 4: 构建器和测试更新
- [ ] **09:00-11:00** 提取构建器格式化工具
  - `formatBytes` → `packages/shared/utils/src/format/bytes.ts`
  - `formatTime` → `packages/shared/utils/src/format/time.ts`

- [ ] **11:00-13:00** 提取配置验证逻辑
  - 通用验证逻辑 → `packages/shared/utils/src/config/validate.ts`

- [ ] **14:00-16:00** 更新测试用例
  ```bash
  # 更新迁移后的测试
  # 添加缺失的测试覆盖
  pnpm run test:coverage
  ```

- [ ] **16:00-18:00** 运行集成测试
  ```bash
  pnpm run test
  pnpm run test:integration
  pnpm run build
  ```

#### Day 5: 验证和文档
- [ ] **09:00-11:00** 兼容性验证
  ```bash
  # 运行兼容性验证脚本
  node scripts/verify-utils-migration.js
  npx ts-node scripts/verify-types.ts
  ```

- [ ] **11:00-13:00** 性能测试
  ```bash
  # 测量包体积变化
  pnpm run size-check
  
  # 测量构建时间变化
  time pnpm run build > ./reports/build-time-after.txt 2>&1
  ```

- [ ] **14:00-16:00** 代码审查和优化
  ```bash
  pnpm run lint
  pnpm run type-check
  ```

- [ ] **16:00-18:00** 更新文档和发布准备
  - 更新 API 文档
  - 准备 CHANGELOG
  - 版本号更新

---

## 🔧 关键脚本和工具

### 自动化重构脚本

<details>
<summary>scripts/start-refactor.sh</summary>

```bash
#!/bin/bash
set -e

echo "🚀 开始 Packages 深度重构..."

# 检查前置条件
echo "📋 检查前置条件..."
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装"
    exit 1
fi

if ! command -v pnpm &> /dev/null; then
    echo "❌ pnpm 未安装"
    exit 1
fi

# 检查 Git 状态
if [ -n "$(git status --porcelain)" ]; then
    echo "❌ 工作区不干净，请先提交或暂存更改"
    exit 1
fi

# 创建重构分支
echo "🌿 创建重构分支..."
git checkout -b feature/packages-deep-refactor-$(date +%Y%m%d)

# 安装依赖工具
echo "🛠️ 安装分析工具..."
pnpm add -D madge jscpd cloc dependency-cruiser

# 生成基准报告
echo "📊 生成基准报告..."
mkdir -p reports
madge --image reports/deps-before.png packages/
jscpd packages/ --reporters html --output reports/duplication-before
cloc packages/ --by-file --csv --out=reports/loc-before.csv

# 记录构建时间基准
echo "⏱️ 记录构建时间基准..."
time pnpm run build > reports/build-time-before.txt 2>&1

echo "✅ 重构环境准备完成"
echo "📝 请按照实施手册继续执行后续步骤"
```
</details>

<details>
<summary>scripts/verify-refactor.js</summary>

```javascript
#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔍 验证重构结果...');

const verificationTasks = [
    {
        name: '函数导出验证',
        check: () => {
            const coreUtils = require('../packages/core/dist/utils.js');
            const sharedUtils = require('../packages/shared/dist/utils.js');
            
            const requiredFunctions = [
                'isValidUrl', 'createLogger', 'isObject', 'isFunction',
                'isString', 'isNumber', 'isBoolean', 'isArray', 'isPromise', 'isEmpty'
            ];
            
            for (const func of requiredFunctions) {
                if (typeof coreUtils[func] !== 'function') {
                    throw new Error(`Core包缺少函数: ${func}`);
                }
                if (typeof sharedUtils[func] !== 'function') {
                    throw new Error(`Shared包缺少函数: ${func}`);
                }
            }
        }
    },
    {
        name: '功能一致性验证',
        check: () => {
            const coreUtils = require('../packages/core/dist/utils.js');
            
            // 测试 URL 验证
            if (!coreUtils.isValidUrl('https://example.com')) {
                throw new Error('isValidUrl 功能异常');
            }
            
            // 测试日志创建
            const logger = coreUtils.createLogger('test');
            if (typeof logger.info !== 'function') {
                throw new Error('createLogger 功能异常');
            }
            
            // 测试类型检查
            if (!coreUtils.isString('test') || coreUtils.isString(123)) {
                throw new Error('类型检查函数异常');
            }
        }
    },
    {
        name: '构建验证',
        check: () => {
            try {
                execSync('pnpm run build', { stdio: 'pipe' });
            } catch (error) {
                throw new Error('构建失败: ' + error.message);
            }
        }
    },
    {
        name: '测试验证',
        check: () => {
            try {
                execSync('pnpm run test', { stdio: 'pipe' });
            } catch (error) {
                throw new Error('测试失败: ' + error.message);
            }
        }
    },
    {
        name: '类型检查验证',
        check: () => {
            try {
                execSync('pnpm run type-check', { stdio: 'pipe' });
            } catch (error) {
                throw new Error('类型检查失败: ' + error.message);
            }
        }
    }
];

let passedCount = 0;
let failedCount = 0;

for (const task of verificationTasks) {
    try {
        console.log(`🔄 执行: ${task.name}`);
        task.check();
        console.log(`✅ ${task.name} - 通过`);
        passedCount++;
    } catch (error) {
        console.error(`❌ ${task.name} - 失败: ${error.message}`);
        failedCount++;
    }
}

console.log('\n📊 验证结果汇总:');
console.log(`✅ 通过: ${passedCount}`);
console.log(`❌ 失败: ${failedCount}`);

if (failedCount > 0) {
    console.log('\n🚨 存在验证失败项，请检查并修复');
    process.exit(1);
} else {
    console.log('\n🎉 所有验证项通过，重构成功！');
}
```
</details>

### 性能监控脚本

<details>
<summary>scripts/monitor-performance.js</summary>

```javascript
#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');

console.log('📊 性能监控报告');

// 1. 包体积分析
function analyzeBundleSize() {
    console.log('\n📦 包体积分析:');
    
    const packages = ['core', 'shared', 'adapters', 'builders', 'plugins', 'sidecar'];
    let totalSize = 0;
    
    for (const pkg of packages) {
        const distPath = `packages/${pkg}/dist`;
        if (fs.existsSync(distPath)) {
            const size = execSync(`du -sh ${distPath}`).toString().split('\t')[0];
            console.log(`  ${pkg}: ${size}`);
        }
    }
}

// 2. 代码重复率分析
function analyzeDuplication() {
    console.log('\n🔄 代码重复率分析:');
    
    try {
        const result = execSync('jscpd packages/ --reporters json', { encoding: 'utf8' });
        const data = JSON.parse(result);
        
        console.log(`  总重复率: ${data.statistics.total.percentage}%`);
        console.log(`  重复行数: ${data.statistics.total.duplicatedLines}`);
        console.log(`  总行数: ${data.statistics.total.totalLines}`);
        
        if (data.statistics.total.percentage > 1) {
            console.log('  ⚠️  重复率超过目标值 1%');
        } else {
            console.log('  ✅ 重复率达标');
        }
    } catch (error) {
        console.log('  ❌ 无法分析代码重复率');
    }
}

// 3. 构建时间分析
function analyzeBuildTime() {
    console.log('\n⏱️  构建时间分析:');
    
    const startTime = Date.now();
    try {
        execSync('pnpm run build', { stdio: 'pipe' });
        const buildTime = (Date.now() - startTime) / 1000;
        console.log(`  构建时间: ${buildTime.toFixed(2)}s`);
        
        if (buildTime > 90) {
            console.log('  ⚠️  构建时间超过目标值 90s');
        } else {
            console.log('  ✅ 构建时间达标');
        }
    } catch (error) {
        console.log('  ❌ 构建失败');
    }
}

// 4. 测试覆盖率分析
function analyzeCoverage() {
    console.log('\n🧪 测试覆盖率分析:');
    
    try {
        const result = execSync('pnpm run test:coverage', { encoding: 'utf8' });
        
        // 解析覆盖率数据
        const lines = result.split('\n');
        const summaryLine = lines.find(line => line.includes('All files'));
        
        if (summaryLine) {
            const parts = summaryLine.split('|').map(s => s.trim());
            console.log(`  语句覆盖率: ${parts[1]}`);
            console.log(`  分支覆盖率: ${parts[2]}`);
            console.log(`  函数覆盖率: ${parts[3]}`);
            console.log(`  行覆盖率: ${parts[4]}`);
        }
    } catch (error) {
        console.log('  ❌ 无法获取测试覆盖率');
    }
}

// 执行所有分析
analyzeBundleSize();
analyzeDuplication();
analyzeBuildTime();
analyzeCoverage();

console.log('\n📈 监控完成');
```
</details>

---

## 🚨 故障排除指南

### 常见问题和解决方案

#### 1. 导入路径错误
**问题**: `Cannot resolve module '@micro-core/shared/utils'`

**解决方案**:
```bash
# 检查 shared 包是否正确构建
cd packages/shared && pnpm run build

# 检查 workspace 依赖配置
grep -r "@micro-core/shared" packages/*/package.json

# 重新安装依赖
pnpm install
```

#### 2. 类型定义冲突
**问题**: `Duplicate identifier` 或类型不兼容

**解决方案**:
```bash
# 清理类型缓存
rm -rf packages/*/dist
rm -rf node_modules/.cache

# 重新构建类型定义
pnpm run type-check
pnpm run build
```

#### 3. 测试失败
**问题**: 迁移后测试用例失败

**解决方案**:
```bash
# 更新测试中的导入路径
find packages/ -name "*.test.ts" -exec sed -i 's/@micro-core\/core/@micro-core\/shared\/utils/g' {} \;

# 重新运行测试
pnpm run test
```

#### 4. 构建性能下降
**问题**: 重构后构建时间增加

**解决方案**:
```bash
# 检查依赖关系
madge --circular packages/

# 优化 TypeScript 配置
# 在 tsconfig.json 中添加:
{
  "compilerOptions": {
    "incremental": true,
    "tsBuildInfoFile": ".tsbuildinfo"
  }
}
```

### 紧急回滚程序

如果重构过程中遇到严重问题，可以执行紧急回滚：

```bash
#!/bin/bash
# scripts/emergency-rollback.sh

echo "🚨 执行紧急回滚..."

# 1. 保存当前状态
git stash push -m "emergency-rollback-$(date +%Y%m%d-%H%M%S)"

# 2. 切换到主分支
git checkout main

# 3. 创建回滚分支
git checkout -b hotfix/emergency-rollback-$(date +%Y%m%d-%H%M%S)

# 4. 验证基本功能
if pnpm run build && pnpm run test; then
    echo "✅ 回滚成功，系统功能正常"
else
    echo "❌ 回滚后仍有问题，需要手动干预"
    exit 1
fi

echo "🎯 回滚完成，请检查系统状态"
```

---

## 📞 支持和反馈

### 技术支持渠道
- **项目负责人**: Echo <<EMAIL>>
- **技术讨论**: 项目技术群
- **问题报告**: [GitHub Issues](https://github.com/echo008/micro-core/issues)
- **文档反馈**: 通过 PR 提交文档改进建议

### 实施过程中的注意事项
1. **每个阶段完成后都要运行完整测试**
2. **保持频繁的代码提交，便于问题定位**
3. **遇到问题及时在技术群讨论**
4. **重要变更前先在测试环境验证**

---

*本实施手册配合《packages目录深度分析与重构清单.md》使用，提供具体的操作指导和问题解决方案。*

**版本**: v1.0.0  
**更新时间**: 2024年12月  
**适用范围**: micro-core packages 重构项目