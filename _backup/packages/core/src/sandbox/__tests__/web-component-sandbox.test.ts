/**
 * WebComponentSandbox tests
 */

import { afterEach, beforeEach, describe, expect, it } from 'vitest';
import { SANDBOX_TYPES } from '../../constants';
import { WebComponentSandbox } from '../web-component-sandbox';

describe('WebComponentSandbox', () => {
    let sandbox: WebComponentSandbox;

    beforeEach(() => {
        sandbox = new WebComponentSandbox('test-sandbox', {});
    });

    afterEach(() => {
        if (sandbox) {
            sandbox.destroy();
        }
    });

    describe('Basic functionality', () => {
        it('should create sandbox with correct name and type', () => {
            expect(sandbox.name).toBe('test-sandbox');
            expect(sandbox.type).toBe(SANDBOX_TYPES.WEB_COMPONENT);
        });

        it('should start inactive', () => {
            expect(sandbox.isActive()).toBe(false);
        });

        it('should activate and deactivate correctly', () => {
            sandbox.active();
            expect(sandbox.isActive()).toBe(true);

            sandbox.inactive();
            expect(sandbox.isActive()).toBe(false);
        });

        it('should generate unique tag name', () => {
            const stats = sandbox.getStats();
            expect(stats.tagName).toMatch(/^micro-core-test-sandbox-/);
        });
    });

    describe('Custom element management', () => {
        it('should create custom element when activated', () => {
            sandbox.active();

            const customElement = sandbox.getCustomElement();
            expect(customElement).toBeTruthy();
            expect(customElement?.tagName.toLowerCase()).toMatch(/^micro-core-test-sandbox-/);
        });

        it('should create shadow root when activated', () => {
            sandbox.active();

            const shadowRoot = sandbox.getShadowRoot();
            expect(shadowRoot).toBeTruthy();
            expect(shadowRoot?.mode).toBe('open');
        });

        it('should add custom element to DOM', () => {
            sandbox.active();

            const customElement = sandbox.getCustomElement();
            expect(customElement?.parentNode).toBeTruthy();
        });

        it('should remove custom element from DOM when deactivated', () => {
            sandbox.active();
            const customElement = sandbox.getCustomElement();

            sandbox.inactive();

            expect(customElement?.parentNode).toBeFalsy();
        });
    });

    describe('Shadow DOM functionality', () => {
        it('should support closed shadow mode', () => {
            const closedSandbox = new WebComponentSandbox('closed-test', {
                shadowMode: 'closed'
            });

            closedSandbox.active();

            const stats = closedSandbox.getStats();
            expect(stats.shadowMode).toBe('closed');

            closedSandbox.destroy();
        });

        it('should add CSS to shadow DOM', () => {
            sandbox.active();

            // Ensure shadow root is available
            const shadowRoot = sandbox.getShadowRoot();
            expect(shadowRoot).toBeTruthy();

            expect(() => {
                sandbox.addCSS('body { color: red; }');
            }).not.toThrow();

            const styleElements = shadowRoot?.querySelectorAll('style');
            expect(styleElements?.length).toBeGreaterThan(0);
        });

        it('should add HTML to shadow DOM', () => {
            sandbox.active();

            expect(() => {
                sandbox.addHTML('<div id="test">Hello World</div>');
            }).not.toThrow();

            const shadowRoot = sandbox.getShadowRoot();
            const testElement = shadowRoot?.querySelector('#test');
            expect(testElement?.textContent).toBe('Hello World');
        });
    });

    describe('Script execution', () => {
        it('should throw error when executing script in inactive sandbox', () => {
            expect(() => {
                sandbox.execScript('var test = 1;');
            }).toThrow('Cannot execute script: Sandbox is not active');
        });

        it('should execute script when active', () => {
            sandbox.active();

            expect(() => {
                sandbox.execScript('console.log("test");');
            }).not.toThrow();
        });
    });

    describe('Event handling', () => {
        it('should add event listeners', () => {
            sandbox.active();

            const listener = () => { };

            expect(() => {
                sandbox.addEventListener('click', listener);
            }).not.toThrow();

            const stats = sandbox.getStats();
            expect(stats.eventListeners).toBe(1);
        });

        it('should remove event listeners', () => {
            sandbox.active();

            const listener = () => { };
            sandbox.addEventListener('click', listener);

            expect(() => {
                sandbox.removeEventListener('click', listener);
            }).not.toThrow();

            const stats = sandbox.getStats();
            expect(stats.eventListeners).toBe(0);
        });

        it('should throw error when adding event listener to inactive sandbox', () => {
            const listener = () => { };

            expect(() => {
                sandbox.addEventListener('click', listener);
            }).toThrow('Cannot add event listener: Sandbox is not active');
        });
    });

    describe('Options handling', () => {
        it('should respect custom tag prefix', () => {
            const customSandbox = new WebComponentSandbox('custom-test', {
                tagPrefix: 'my-app'
            });

            const stats = customSandbox.getStats();
            expect(stats.tagName).toMatch(/^my-app-custom-test-/);

            customSandbox.destroy();
        });

        it('should respect custom container', () => {
            const container = document.createElement('div');
            document.body.appendChild(container);

            const containerSandbox = new WebComponentSandbox('container-test', {
                container: container
            });

            containerSandbox.active();

            const customElement = containerSandbox.getCustomElement();
            expect(customElement?.parentNode).toBe(container);

            containerSandbox.destroy();
            document.body.removeChild(container);
        });

        it('should handle style isolation option', () => {
            const styledSandbox = new WebComponentSandbox('styled-test', {
                enableStyleIsolation: true
            });

            styledSandbox.active();

            const shadowRoot = styledSandbox.getShadowRoot();
            const styleElements = shadowRoot?.querySelectorAll('style');
            expect(styleElements?.length).toBeGreaterThan(0);

            styledSandbox.destroy();
        });
    });

    describe('Statistics and management', () => {
        it('should provide sandbox statistics', () => {
            sandbox.active();

            const stats = sandbox.getStats();
            expect(stats).toHaveProperty('tagName');
            expect(stats).toHaveProperty('hasCustomElement');
            expect(stats).toHaveProperty('hasShadowRoot');
            expect(stats).toHaveProperty('scriptElements');
            expect(stats).toHaveProperty('eventListeners');
            expect(stats).toHaveProperty('shadowMode');

            expect(stats.hasCustomElement).toBe(true);
            expect(stats.hasShadowRoot).toBe(true);
        });

        it('should reset sandbox state', () => {
            sandbox.active();

            // Ensure shadow root is available before adding HTML
            const shadowRoot = sandbox.getShadowRoot();
            expect(shadowRoot).toBeTruthy();

            sandbox.addHTML('<div>test</div>');

            sandbox.reset();

            expect(sandbox.isActive()).toBe(false);
            const stats = sandbox.getStats();
            expect(stats.hasCustomElement).toBe(false);
            expect(stats.hasShadowRoot).toBe(false);
        });
    });

    describe('Error handling', () => {
        it('should handle CSS addition errors gracefully', () => {
            expect(() => {
                sandbox.addCSS('body { color: red; }');
            }).toThrow('Cannot add CSS: Sandbox is not active');
        });

        it('should handle HTML addition errors gracefully', () => {
            expect(() => {
                sandbox.addHTML('<div>test</div>');
            }).toThrow('Cannot add HTML: Sandbox is not active');
        });

        it('should handle container not found error', () => {
            const invalidSandbox = new WebComponentSandbox('invalid-test', {
                container: '#non-existent-container'
            });

            expect(() => {
                invalidSandbox.active();
            }).toThrow('Container element not found');

            invalidSandbox.destroy();
        });
    });
});