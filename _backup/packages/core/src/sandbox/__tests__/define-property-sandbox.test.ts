/**
 * DefinePropertySandbox tests
 */

import { afterEach, beforeEach, describe, expect, it } from 'vitest';
import { SANDBOX_TYPES } from '../../constants';
import { DefinePropertySandbox } from '../define-property-sandbox';

describe('DefinePropertySandbox', () => {
    let sandbox: DefinePropertySandbox;

    beforeEach(() => {
        sandbox = new DefinePropertySandbox('test-sandbox', {});
    });

    afterEach(() => {
        if (sandbox) {
            sandbox.destroy();
        }
    });

    describe('Basic functionality', () => {
        it('should create sandbox with correct name and type', () => {
            expect(sandbox.name).toBe('test-sandbox');
            expect(sandbox.type).toBe(SANDBOX_TYPES.DEFINE_PROPERTY);
        });

        it('should start inactive', () => {
            expect(sandbox.isActive()).toBe(false);
        });

        it('should activate and deactivate correctly', () => {
            sandbox.active();
            expect(sandbox.isActive()).toBe(true);

            sandbox.inactive();
            expect(sandbox.isActive()).toBe(false);
        });
    });

    describe('Script execution', () => {
        it('should throw error when executing script in inactive sandbox', () => {
            expect(() => {
                sandbox.execScript('var test = 1;');
            }).toThrow('Cannot execute script: Sandbox is not active');
        });

        it('should execute simple script when active', () => {
            sandbox.active();

            const result = sandbox.execScript('return 42;');
            expect(result).toBe(42);
        });

        it('should execute script with variables', () => {
            sandbox.active();

            // Set a variable in the fake window
            sandbox.execScript('testVar = "hello world";');
            const result = sandbox.execScript('return testVar;');
            expect(result).toBe('hello world');
        });

        it('should isolate variables from global scope', () => {
            sandbox.active();

            // Set a variable in sandbox
            sandbox.execScript('var isolatedVar = "sandbox value";');

            // Check that it doesn't exist in global scope
            expect((window as any).isolatedVar).toBeUndefined();
        });
    });

    describe('Property isolation', () => {
        it('should allow access to allowed properties', () => {
            sandbox.active();

            const result = sandbox.execScript('return typeof console;');
            expect(result).toBe('object');
        });

        it('should handle property setting and getting', () => {
            sandbox.active();

            sandbox.execScript('testProp = "test value";');
            const result = sandbox.execScript('return testProp;');
            expect(result).toBe('test value');
        });
    });

    describe('Options handling', () => {
        it('should respect strict isolation option', () => {
            const strictSandbox = new DefinePropertySandbox('strict-test', {
                strictIsolation: true,
                allowList: ['console']
            });

            strictSandbox.active();

            // Should allow console
            const consoleResult = strictSandbox.execScript('return typeof console;');
            expect(consoleResult).toBe('object');

            // Should throw error for document (not in allow list and strict isolation)
            expect(() => {
                strictSandbox.execScript('return document;');
            }).toThrow('Access to property "document" is denied');

            strictSandbox.destroy();
        });

        it('should respect deny list', () => {
            const denySandbox = new DefinePropertySandbox('deny-test', {
                denyList: ['document']
            });

            denySandbox.active();

            // Should throw error for document (in deny list)
            expect(() => {
                denySandbox.execScript('return document;');
            }).toThrow('Access to property "document" is denied');

            denySandbox.destroy();
        });
    });

    describe('Statistics and management', () => {
        it('should provide sandbox statistics', () => {
            sandbox.active();

            sandbox.execScript('newProp = "test";');

            const stats = sandbox.getStats();
            expect(stats).toHaveProperty('modifiedProps');
            expect(stats).toHaveProperty('addedProps');
            expect(stats).toHaveProperty('interceptedProps');
            expect(stats).toHaveProperty('trackedDescriptors');
            expect(stats).toHaveProperty('snapshotSize');
        });

        it('should provide fake window object', () => {
            const fakeWindow = sandbox.getFakeWindow();
            expect(fakeWindow).toBeDefined();
            expect(fakeWindow.window).toBe(fakeWindow);
            expect(fakeWindow.self).toBe(fakeWindow);
        });

        it('should reset sandbox state', () => {
            sandbox.active();
            sandbox.execScript('testVar = "test";');

            sandbox.reset();

            expect(sandbox.isActive()).toBe(false);
            const stats = sandbox.getStats();
            expect(stats.modifiedProps).toHaveLength(0);
            expect(stats.addedProps).toHaveLength(0);
        });
    });

    describe('Property interception', () => {
        it('should allow adding properties to intercept', () => {
            // Create sandbox with less strict isolation for this test
            const testSandbox = new DefinePropertySandbox('intercept-test', {
                strictIsolation: false
            });

            testSandbox.active();

            // Add a custom property to intercept
            (window as any).customTestProp = 'original value';
            testSandbox.addPropertyToIntercept('customTestProp');

            // Modify it in sandbox
            testSandbox.execScript('customTestProp = "modified value";');

            // Should be modified in sandbox context
            const result = testSandbox.execScript('return customTestProp;');
            expect(result).toBe('modified value');

            // Clean up
            delete (window as any).customTestProp;
            testSandbox.destroy();
        });

        it('should allow removing properties from interception', () => {
            // Create sandbox with less strict isolation for this test
            const testSandbox = new DefinePropertySandbox('remove-test', {
                strictIsolation: false
            });

            testSandbox.active();

            (window as any).customTestProp = 'original value';
            testSandbox.addPropertyToIntercept('customTestProp');
            testSandbox.removePropertyFromIntercept('customTestProp');

            // Clean up
            delete (window as any).customTestProp;
            testSandbox.destroy();
        });
    });

    describe('Error handling', () => {
        it('should handle script execution errors', () => {
            sandbox.active();

            expect(() => {
                sandbox.execScript('throw new Error("Test error");');
            }).toThrow('Script execution failed');
        });

        it('should handle property access errors with strict isolation', () => {
            const strictSandbox = new DefinePropertySandbox('strict-error-test', {
                strictIsolation: true,
                denyList: ['document']
            });

            strictSandbox.active();

            expect(() => {
                strictSandbox.execScript('return document;');
            }).toThrow('Access to property "document" is denied');

            strictSandbox.destroy();
        });
    });
});