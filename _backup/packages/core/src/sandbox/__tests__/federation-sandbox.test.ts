/**
 * FederationSandbox tests
 */

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { SANDBOX_TYPES } from '../../constants';
import { FederationSandbox } from '../federation-sandbox';

describe('FederationSandbox', () => {
    let sandbox: FederationSandbox;

    beforeEach(() => {
        sandbox = new FederationSandbox('test-sandbox', {});
    });

    afterEach(() => {
        if (sandbox) {
            sandbox.destroy();
        }
    });

    describe('Basic functionality', () => {
        it('should create sandbox with correct name and type', () => {
            expect(sandbox.name).toBe('test-sandbox');
            expect(sandbox.type).toBe(SANDBOX_TYPES.FEDERATION);
        });

        it('should start inactive', () => {
            expect(sandbox.isActive()).toBe(false);
        });

        it('should activate and deactivate correctly', () => {
            sandbox.active();
            expect(sandbox.isActive()).toBe(true);

            sandbox.inactive();
            expect(sandbox.isActive()).toBe(false);
        });
    });

    describe('Remote entries management', () => {
        beforeEach(() => {
            sandbox.active();
        });

        it('should register remote entries', () => {
            sandbox.registerRemoteEntry('remote1', 'http://localhost:3001/remoteEntry.js');

            const remoteEntries = sandbox.getRemoteEntries();
            expect(remoteEntries.has('remote1')).toBe(true);
            expect(remoteEntries.get('remote1')?.url).toBe('http://localhost:3001/remoteEntry.js');
        });

        it('should initialize with configured remotes', () => {
            const federationSandbox = new FederationSandbox('federation-test', {
                remotes: {
                    'app1': 'http://localhost:3001/remoteEntry.js',
                    'app2': 'http://localhost:3002/remoteEntry.js'
                }
            });

            federationSandbox.active();

            const remoteEntries = federationSandbox.getRemoteEntries();
            expect(remoteEntries.size).toBe(2);
            expect(remoteEntries.has('app1')).toBe(true);
            expect(remoteEntries.has('app2')).toBe(true);

            federationSandbox.destroy();
        });

        it('should track remote entry loading state', () => {
            sandbox.registerRemoteEntry('remote1', 'http://localhost:3001/remoteEntry.js');

            const remoteEntries = sandbox.getRemoteEntries();
            const remoteEntry = remoteEntries.get('remote1');

            expect(remoteEntry?.isLoaded).toBe(false);
            expect(remoteEntry?.container).toBeUndefined();
        });
    });

    describe('Shared modules management', () => {
        beforeEach(() => {
            sandbox.active();
        });

        it('should register shared modules', () => {
            sandbox.registerSharedModule('react', {
                version: '18.0.0',
                singleton: true,
                factory: () => ({ createElement: vi.fn() })
            });

            const sharedModules = sandbox.getSharedModules();
            expect(sharedModules.has('react')).toBe(true);
            expect(sharedModules.get('react')?.version).toBe('18.0.0');
            expect(sharedModules.get('react')?.singleton).toBe(true);
        });

        it('should initialize with configured shared modules', () => {
            const federationSandbox = new FederationSandbox('federation-test', {
                shared: {
                    'react': {
                        version: '18.0.0',
                        singleton: true
                    },
                    'react-dom': {
                        version: '18.0.0',
                        singleton: true
                    }
                }
            });

            federationSandbox.active();

            const sharedModules = federationSandbox.getSharedModules();
            expect(sharedModules.size).toBe(2);
            expect(sharedModules.has('react')).toBe(true);
            expect(sharedModules.has('react-dom')).toBe(true);

            federationSandbox.destroy();
        });
    });

    describe('Module loading', () => {
        beforeEach(() => {
            sandbox.active();
        });

        it('should import modules', async () => {
            // Register a shared module with factory
            sandbox.registerSharedModule('test-module', {
                factory: () => ({ test: 'value' })
            });

            const module = await sandbox.importModule('test-module');
            expect(module).toEqual({ test: 'value' });
        });

        it('should handle module import errors', async () => {
            await expect(sandbox.importModule('non-existent-module')).rejects.toThrow(
                'Module not found: non-existent-module'
            );
        });

        it('should use fallback modules', async () => {
            const federationSandbox = new FederationSandbox('fallback-test', {
                fallbacks: {
                    'fallback-module': { fallback: 'value' }
                }
            });

            federationSandbox.active();

            const module = await federationSandbox.importModule('fallback-module');
            expect(module).toEqual({ fallback: 'value' });

            federationSandbox.destroy();
        });

        it('should use custom module resolver', async () => {
            const federationSandbox = new FederationSandbox('resolver-test', {
                moduleResolver: async (moduleName: string) => {
                    if (moduleName === 'custom-module') {
                        return { custom: 'resolved' };
                    }
                    throw new Error(`Cannot resolve: ${moduleName}`);
                }
            });

            federationSandbox.active();

            const module = await federationSandbox.importModule('custom-module');
            expect(module).toEqual({ custom: 'resolved' });

            federationSandbox.destroy();
        });
    });

    describe('Module caching', () => {
        beforeEach(() => {
            sandbox.active();
        });

        it('should cache loaded modules when enabled', async () => {
            const cachingSandbox = new FederationSandbox('caching-test', {
                enableCaching: true
            });

            cachingSandbox.active();

            cachingSandbox.registerSharedModule('cached-module', {
                factory: () => ({ cached: 'value' })
            });

            // Load module twice
            await cachingSandbox.importModule('cached-module');
            await cachingSandbox.importModule('cached-module');

            const cache = cachingSandbox.getModuleCache();
            expect(cache.has('cached-module')).toBe(true);
            expect(cache.get('cached-module')?.usageCount).toBe(2);

            cachingSandbox.destroy();
        });

        it('should clear module cache', () => {
            sandbox.registerSharedModule('test-module', {
                factory: () => ({ test: 'value' })
            });

            // Manually add to cache for testing
            const cache = sandbox.getModuleCache();
            cache.set('test-module', {
                module: { test: 'value' },
                version: '1.0.0',
                loadedAt: new Date(),
                usageCount: 1
            });

            expect(cache.size).toBe(1);

            sandbox.clearModuleCache();
            expect(cache.size).toBe(0);
        });

        it('should not cache when caching is disabled', async () => {
            const noCachingSandbox = new FederationSandbox('no-caching-test', {
                enableCaching: false
            });

            noCachingSandbox.active();

            noCachingSandbox.registerSharedModule('no-cache-module', {
                factory: () => ({ noCache: 'value' })
            });

            await noCachingSandbox.importModule('no-cache-module');

            const cache = noCachingSandbox.getModuleCache();
            expect(cache.size).toBe(0);

            noCachingSandbox.destroy();
        });
    });

    describe('Script execution', () => {
        it('should throw error when executing script in inactive sandbox', () => {
            expect(() => {
                sandbox.execScript('var test = 1;');
            }).toThrow('Cannot execute script: Sandbox is not active');
        });

        it('should execute simple script when active', () => {
            sandbox.active();

            const result = sandbox.execScript('return 42;');
            expect(result).toBe(42);
        });

        it('should provide module scope in script execution', () => {
            sandbox.active();

            const result = sandbox.execScript('return typeof import;');
            expect(result).toBe('function');
        });

        it('should handle script execution errors', () => {
            sandbox.active();

            expect(() => {
                sandbox.execScript('throw new Error("Test error");');
            }).toThrow('Script execution failed');
        });
    });

    describe('Options handling', () => {
        it('should respect module timeout option', () => {
            const timeoutSandbox = new FederationSandbox('timeout-test', {
                moduleTimeout: 5000
            });

            // Test that the option is accepted
            expect(timeoutSandbox).toBeTruthy();

            timeoutSandbox.destroy();
        });

        it('should handle version checking option', () => {
            const versionSandbox = new FederationSandbox('version-test', {
                enableVersionCheck: false
            });

            const stats = versionSandbox.getStats();
            expect(stats.isVersionCheckEnabled).toBe(false);

            versionSandbox.destroy();
        });

        it('should handle strict isolation option', () => {
            const strictSandbox = new FederationSandbox('strict-test', {
                strictIsolation: false
            });

            // Test that the option is accepted
            expect(strictSandbox).toBeTruthy();

            strictSandbox.destroy();
        });
    });

    describe('Statistics and management', () => {
        beforeEach(() => {
            sandbox.active();
        });

        it('should provide sandbox statistics', () => {
            sandbox.registerRemoteEntry('remote1', 'http://localhost:3001/remoteEntry.js');
            sandbox.registerSharedModule('shared1', { version: '1.0.0' });

            const stats = sandbox.getStats();
            expect(stats).toHaveProperty('remoteEntries');
            expect(stats).toHaveProperty('sharedModules');
            expect(stats).toHaveProperty('cachedModules');
            expect(stats).toHaveProperty('loadingPromises');
            expect(stats).toHaveProperty('isActivated');
            expect(stats).toHaveProperty('isCachingEnabled');
            expect(stats).toHaveProperty('isVersionCheckEnabled');

            expect(stats.remoteEntries).toBe(1);
            expect(stats.sharedModules).toBe(1);
            expect(stats.isActivated).toBe(true);
        });

        it('should reset sandbox state', () => {
            sandbox.registerRemoteEntry('remote1', 'http://localhost:3001/remoteEntry.js');
            sandbox.registerSharedModule('shared1', { version: '1.0.0' });

            sandbox.reset();

            expect(sandbox.isActive()).toBe(false);
            const stats = sandbox.getStats();
            expect(stats.cachedModules).toBe(0);
            expect(stats.loadingPromises).toBe(0);
        });

        it('should track loading promises', () => {
            const stats = sandbox.getStats();
            expect(stats.loadingPromises).toBe(0);

            // Loading promises would be tracked during actual module loading
        });
    });

    describe('Error handling', () => {
        beforeEach(() => {
            sandbox.active();
        });

        it('should handle module import timeout', async () => {
            const timeoutSandbox = new FederationSandbox('timeout-test', {
                moduleTimeout: 100 // Very short timeout
            });

            timeoutSandbox.active();
            timeoutSandbox.registerRemoteEntry('slow-remote', 'http://slow-server/remoteEntry.js');

            // This would timeout in a real scenario
            // For testing, we just verify the method exists
            expect(typeof timeoutSandbox.importModule).toBe('function');

            timeoutSandbox.destroy();
        });

        it('should handle remote entry loading errors', () => {
            sandbox.registerRemoteEntry('invalid-remote', 'http://invalid-url/remoteEntry.js');

            // Error handling would be tested in integration tests
            // For unit tests, we verify the structure is correct
            const remoteEntries = sandbox.getRemoteEntries();
            expect(remoteEntries.has('invalid-remote')).toBe(true);
        });

        it('should handle module not found errors', async () => {
            await expect(sandbox.importModule('non-existent')).rejects.toThrow(
                'Module not found: non-existent'
            );
        });

        it('should handle inactive sandbox errors', async () => {
            sandbox.inactive();

            await expect(sandbox.importModule('any-module')).rejects.toThrow(
                'Cannot import module: Sandbox is not active'
            );
        });
    });

    describe('Module scope', () => {
        beforeEach(() => {
            sandbox.active();
        });

        it('should provide import function in module scope', () => {
            const result = sandbox.execScript('return typeof import;');
            expect(result).toBe('function');
        });

        it('should provide require function in module scope', () => {
            const result = sandbox.execScript('return typeof require;');
            expect(result).toBe('function');
        });

        it('should provide webpack compatibility', () => {
            const result = sandbox.execScript('return typeof __webpack_require__;');
            expect(result).toBe('function');
        });

        it('should provide federation import function', () => {
            const result = sandbox.execScript('return typeof __federation_import__;');
            expect(result).toBe('function');
        });
    });

    describe('Version compatibility', () => {
        it('should handle version checking when enabled', () => {
            const versionSandbox = new FederationSandbox('version-test', {
                enableVersionCheck: true,
                shared: {
                    'test-lib': {
                        version: '1.0.0',
                        strictVersion: true
                    }
                }
            });

            versionSandbox.active();

            const sharedModules = versionSandbox.getSharedModules();
            const testLib = sharedModules.get('test-lib');
            expect(testLib?.strictVersion).toBe(true);

            versionSandbox.destroy();
        });

        it('should handle singleton modules', () => {
            const singletonSandbox = new FederationSandbox('singleton-test', {
                shared: {
                    'singleton-lib': {
                        singleton: true,
                        version: '1.0.0'
                    }
                }
            });

            singletonSandbox.active();

            const sharedModules = singletonSandbox.getSharedModules();
            const singletonLib = sharedModules.get('singleton-lib');
            expect(singletonLib?.singleton).toBe(true);

            singletonSandbox.destroy();
        });
    });
});