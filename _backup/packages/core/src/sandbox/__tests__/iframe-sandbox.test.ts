/**
 * IframeSandbox tests
 */

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { SANDBOX_TYPES } from '../../constants';
import { IframeSandbox } from '../iframe-sandbox';

describe('IframeSandbox', () => {
    let sandbox: IframeSandbox;

    beforeEach(() => {
        sandbox = new IframeSandbox('test-sandbox', {});
    });

    afterEach(() => {
        if (sandbox) {
            sandbox.destroy();
        }
    });

    describe('Basic functionality', () => {
        it('should create sandbox with correct name and type', () => {
            expect(sandbox.name).toBe('test-sandbox');
            expect(sandbox.type).toBe(SANDBOX_TYPES.IFRAME);
        });

        it('should start inactive', () => {
            expect(sandbox.isActive()).toBe(false);
        });

        it('should activate and deactivate correctly', () => {
            sandbox.active();
            expect(sandbox.isActive()).toBe(true);

            sandbox.inactive();
            expect(sandbox.isActive()).toBe(false);
        });
    });

    describe('Iframe management', () => {
        it('should create iframe when activated', () => {
            sandbox.active();

            const iframe = sandbox.getIframe();
            expect(iframe).toBeTruthy();
            expect(iframe?.tagName.toLowerCase()).toBe('iframe');
        });

        it('should add iframe to DOM', () => {
            sandbox.active();

            const iframe = sandbox.getIframe();
            expect(iframe?.parentNode).toBeTruthy();
        });

        it('should remove iframe from DOM when deactivated', () => {
            sandbox.active();
            const iframe = sandbox.getIframe();

            sandbox.inactive();

            expect(iframe?.parentNode).toBeFalsy();
        });

        it('should set sandbox attributes on iframe', () => {
            const sandboxWithAttrs = new IframeSandbox('attrs-test', {
                allowScripts: true,
                allowSameOrigin: false,
                allowForms: true
            });

            sandboxWithAttrs.active();

            const iframe = sandboxWithAttrs.getIframe();
            const sandboxAttr = iframe?.getAttribute('sandbox');
            expect(sandboxAttr).toContain('allow-scripts');
            expect(sandboxAttr).toContain('allow-forms');
            expect(sandboxAttr).not.toContain('allow-same-origin');

            sandboxWithAttrs.destroy();
        });
    });

    describe('Script execution', () => {
        it('should throw error when executing script in inactive sandbox', async () => {
            await expect(sandbox.execScript('var test = 1;')).rejects.toThrow(
                'Cannot execute script: Sandbox is not active'
            );
        });

        it('should execute simple script when active', async () => {
            sandbox.active();

            // Mock the iframe ready state and message handling
            const iframe = sandbox.getIframe();
            if (iframe) {
                // Simulate iframe ready
                setTimeout(() => {
                    window.dispatchEvent(new MessageEvent('message', {
                        data: {
                            type: 'ready',
                            id: 'ready',
                            data: true,
                            sandboxName: 'test-sandbox'
                        },
                        source: iframe.contentWindow
                    }));
                }, 10);
            }

            // This test would need more complex mocking in a real scenario
            // For now, we'll test that the method exists and can be called
            expect(typeof sandbox.execScript).toBe('function');
        }, 1000);
    });

    describe('Communication', () => {
        it('should handle ping/pong communication', async () => {
            sandbox.active();

            // Mock iframe ready and ping response
            const iframe = sandbox.getIframe();
            if (iframe) {
                setTimeout(() => {
                    // Simulate ready message
                    window.dispatchEvent(new MessageEvent('message', {
                        data: {
                            type: 'ready',
                            id: 'ready',
                            data: true,
                            sandboxName: 'test-sandbox'
                        },
                        source: iframe.contentWindow
                    }));

                    // Simulate pong response
                    setTimeout(() => {
                        window.dispatchEvent(new MessageEvent('message', {
                            data: {
                                type: 'pong',
                                id: expect.any(String),
                                data: true,
                                sandboxName: 'test-sandbox'
                            },
                            source: iframe.contentWindow
                        }));
                    }, 10);
                }, 10);
            }

            // Test ping method exists
            expect(typeof sandbox.ping).toBe('function');
        });
    });

    describe('Content management', () => {
        it('should have addCSS method', () => {
            expect(typeof sandbox.addCSS).toBe('function');
        });

        it('should have addHTML method', () => {
            expect(typeof sandbox.addHTML).toBe('function');
        });

        it('should throw error when adding CSS to inactive sandbox', async () => {
            await expect(sandbox.addCSS('body { color: red; }')).rejects.toThrow(
                'Cannot add CSS: Sandbox is not active'
            );
        });

        it('should throw error when adding HTML to inactive sandbox', async () => {
            await expect(sandbox.addHTML('<div>test</div>')).rejects.toThrow(
                'Cannot add HTML: Sandbox is not active'
            );
        });
    });

    describe('Options handling', () => {
        it('should respect custom container', () => {
            const container = document.createElement('div');
            document.body.appendChild(container);

            const containerSandbox = new IframeSandbox('container-test', {
                container: container
            });

            containerSandbox.active();

            const iframe = containerSandbox.getIframe();
            expect(iframe?.parentNode).toBe(container);

            containerSandbox.destroy();
            document.body.removeChild(container);
        });

        it('should handle custom sandbox attributes', () => {
            const customSandbox = new IframeSandbox('custom-test', {
                sandboxAttributes: ['allow-modals', 'allow-orientation-lock']
            });

            customSandbox.active();

            const iframe = customSandbox.getIframe();
            const sandboxAttr = iframe?.getAttribute('sandbox');
            expect(sandboxAttr).toContain('allow-modals');
            expect(sandboxAttr).toContain('allow-orientation-lock');

            customSandbox.destroy();
        });

        it('should handle custom srcDoc', () => {
            const customSrcDoc = '<html><body><h1>Custom Content</h1></body></html>';
            const customSandbox = new IframeSandbox('srcdoc-test', {
                srcDoc: customSrcDoc
            });

            customSandbox.active();

            const iframe = customSandbox.getIframe();
            expect(iframe?.srcdoc).toBe(customSrcDoc);

            customSandbox.destroy();
        });

        it('should handle communication timeout option', () => {
            const timeoutSandbox = new IframeSandbox('timeout-test', {
                communicationTimeout: 1000
            });

            // Test that the option is accepted
            expect(timeoutSandbox).toBeTruthy();

            timeoutSandbox.destroy();
        });
    });

    describe('Statistics and management', () => {
        it('should provide sandbox statistics', () => {
            sandbox.active();

            const stats = sandbox.getStats();
            expect(stats).toHaveProperty('hasIframe');
            expect(stats).toHaveProperty('isReady');
            expect(stats).toHaveProperty('pendingMessages');
            expect(stats).toHaveProperty('eventListeners');
            expect(stats).toHaveProperty('sandboxAttributes');

            expect(stats.hasIframe).toBe(true);
            expect(stats.isReady).toBe(false); // Not ready until iframe loads
        });

        it('should reset sandbox state', () => {
            sandbox.active();

            sandbox.reset();

            expect(sandbox.isActive()).toBe(false);
            const stats = sandbox.getStats();
            expect(stats.hasIframe).toBe(false);
            expect(stats.pendingMessages).toBe(0);
        });

        it('should track pending messages', () => {
            sandbox.active();

            const stats = sandbox.getStats();
            expect(stats.pendingMessages).toBe(0);

            // Pending messages would increase during actual communication
            // This is more of an integration test
        });
    });

    describe('Error handling', () => {
        it('should handle container not found error', () => {
            const invalidSandbox = new IframeSandbox('invalid-test', {
                container: '#non-existent-container'
            });

            expect(() => {
                invalidSandbox.active();
            }).toThrow('Container element not found');

            invalidSandbox.destroy();
        });

        it('should handle activation errors gracefully', () => {
            // Test error handling during activation
            const errorSandbox = new IframeSandbox('error-test', {});

            // Mock document.createElement to throw an error
            const originalCreateElement = document.createElement;
            document.createElement = vi.fn().mockImplementation((tagName) => {
                if (tagName === 'iframe') {
                    throw new Error('Failed to create iframe');
                }
                return originalCreateElement.call(document, tagName);
            });

            expect(() => {
                errorSandbox.active();
            }).toThrow('Failed to activate Iframe sandbox');

            // Restore original method
            document.createElement = originalCreateElement;

            errorSandbox.destroy();
        });

        it('should handle deactivation errors gracefully', () => {
            sandbox.active();

            // Mock removeChild to throw an error
            const iframe = sandbox.getIframe();
            if (iframe && iframe.parentNode) {
                const originalRemoveChild = iframe.parentNode.removeChild;
                iframe.parentNode.removeChild = vi.fn().mockImplementation(() => {
                    throw new Error('Failed to remove iframe');
                });

                expect(() => {
                    sandbox.inactive();
                }).toThrow('Failed to deactivate Iframe sandbox');

                // Restore original method
                iframe.parentNode.removeChild = originalRemoveChild;
            }
        });
    });

    describe('Message handling', () => {
        it('should ignore messages from other sources', () => {
            sandbox.active();

            // Dispatch a message from a different source
            const fakeMessage = new MessageEvent('message', {
                data: {
                    type: 'ready',
                    id: 'ready',
                    data: true
                },
                source: window // Different source
            });

            // Should not throw or cause issues
            expect(() => {
                window.dispatchEvent(fakeMessage);
            }).not.toThrow();
        });

        it('should ignore malformed messages', () => {
            sandbox.active();

            const iframe = sandbox.getIframe();
            if (iframe) {
                // Dispatch malformed messages
                const malformedMessages = [
                    new MessageEvent('message', { data: null, source: iframe.contentWindow }),
                    new MessageEvent('message', { data: {}, source: iframe.contentWindow }),
                    new MessageEvent('message', { data: { type: 'unknown' }, source: iframe.contentWindow }),
                    new MessageEvent('message', { data: { id: 'test' }, source: iframe.contentWindow })
                ];

                malformedMessages.forEach(message => {
                    expect(() => {
                        window.dispatchEvent(message);
                    }).not.toThrow();
                });
            }
        });
    });
});