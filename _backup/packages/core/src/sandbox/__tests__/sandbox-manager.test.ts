/**
 * @fileoverview 沙箱管理器测试
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { SandboxManager } from '../sandbox-manager';
import { SANDBOX_TYPES } from '../../constants';
import type { SandboxConfig } from '../../types';

// Mock DOM
Object.defineProperty(window, 'document', {
    value: {
        createElement: vi.fn(() => ({
            style: {},
            setAttribute: vi.fn(),
            appendChild: vi.fn(),
            removeChild: vi.fn(),
            querySelector: vi.fn(),
            querySelectorAll: vi.fn(() => [])
        })),
        querySelector: vi.fn(),
        querySelectorAll: vi.fn(() => []),
        body: {
            appendChild: vi.fn(),
            removeChild: vi.fn()
        }
    },
    writable: true
});

describe('SandboxManager', () => {
    let sandboxManager: SandboxManager;
    let mockConfig: SandboxConfig;

    beforeEach(() => {
        sandboxManager = new SandboxManager();
        
        mockConfig = {
            name: 'test-sandbox',
            type: SANDBOX_TYPES.NAMESPACE,
            container: '#test-container',
            isolation: {
                css: true,
                js: true,
                dom: true
            }
        };
    });

    describe('createSandbox', () => {
        it('应该成功创建命名空间沙箱', () => {
            const sandbox = sandboxManager.createSandbox({
                ...mockConfig,
                type: SANDBOX_TYPES.NAMESPACE
            });

            expect(sandbox).toBeDefined();
            expect(sandbox.getName()).toBe('test-sandbox');
            expect(sandbox.getType()).toBe(SANDBOX_TYPES.NAMESPACE);
        });

        it('应该成功创建代理沙箱', () => {
            const sandbox = sandboxManager.createSandbox({
                ...mockConfig,
                type: SANDBOX_TYPES.PROXY
            });

            expect(sandbox).toBeDefined();
            expect(sandbox.getType()).toBe(SANDBOX_TYPES.PROXY);
        });

        it('应该成功创建iframe沙箱', () => {
            const sandbox = sandboxManager.createSandbox({
                ...mockConfig,
                type: SANDBOX_TYPES.IFRAME
            });

            expect(sandbox).toBeDefined();
            expect(sandbox.getType()).toBe(SANDBOX_TYPES.IFRAME);
        });

        it('应该阻止创建重名沙箱', () => {
            sandboxManager.createSandbox(mockConfig);

            expect(() => {
                sandboxManager.createSandbox(mockConfig);
            }).toThrow('沙箱已存在');
        });

        it('应该验证沙箱配置', () => {
            const invalidConfig = { ...mockConfig };
            delete (invalidConfig as any).name;

            expect(() => {
                sandboxManager.createSandbox(invalidConfig as SandboxConfig);
            }).toThrow('沙箱名称不能为空');
        });

        it('应该支持自动类型选择', () => {
            const autoConfig = {
                ...mockConfig,
                type: SANDBOX_TYPES.AUTO
            };

            const sandbox = sandboxManager.createSandbox(autoConfig);
            expect(sandbox).toBeDefined();
            // 自动选择应该选择一个具体的沙箱类型
            expect([
                SANDBOX_TYPES.NAMESPACE,
                SANDBOX_TYPES.PROXY,
                SANDBOX_TYPES.IFRAME
            ]).toContain(sandbox.getType());
        });
    });

    describe('getSandbox', () => {
        beforeEach(() => {
            sandboxManager.createSandbox(mockConfig);
        });

        it('应该返回已创建的沙箱', () => {
            const sandbox = sandboxManager.getSandbox('test-sandbox');
            expect(sandbox).toBeDefined();
            expect(sandbox?.getName()).toBe('test-sandbox');
        });

        it('应该返回null对于不存在的沙箱', () => {
            const sandbox = sandboxManager.getSandbox('non-existent');
            expect(sandbox).toBeNull();
        });
    });

    describe('destroySandbox', () => {
        beforeEach(() => {
            sandboxManager.createSandbox(mockConfig);
        });

        it('应该成功销毁沙箱', () => {
            expect(sandboxManager.getSandbox('test-sandbox')).toBeDefined();

            sandboxManager.destroySandbox('test-sandbox');

            expect(sandboxManager.getSandbox('test-sandbox')).toBeNull();
        });

        it('销毁不存在的沙箱应该不抛出错误', () => {
            expect(() => {
                sandboxManager.destroySandbox('non-existent');
            }).not.toThrow();
        });

        it('应该清理沙箱资源', () => {
            const sandbox = sandboxManager.getSandbox('test-sandbox');
            const destroySpy = vi.spyOn(sandbox!, 'destroy');

            sandboxManager.destroySandbox('test-sandbox');

            expect(destroySpy).toHaveBeenCalled();
        });
    });

    describe('getAllSandboxes', () => {
        it('应该返回空数组当没有沙箱时', () => {
            const sandboxes = sandboxManager.getAllSandboxes();
            expect(sandboxes).toEqual([]);
        });

        it('应该返回所有已创建的沙箱', () => {
            const config2 = { ...mockConfig, name: 'test-sandbox-2' };
            
            sandboxManager.createSandbox(mockConfig);
            sandboxManager.createSandbox(config2);

            const sandboxes = sandboxManager.getAllSandboxes();
            expect(sandboxes).toHaveLength(2);
            expect(sandboxes.map(s => s.getName())).toContain('test-sandbox');
            expect(sandboxes.map(s => s.getName())).toContain('test-sandbox-2');
        });
    });

    describe('getSandboxesByType', () => {
        beforeEach(() => {
            sandboxManager.createSandbox({
                ...mockConfig,
                name: 'namespace-sandbox',
                type: SANDBOX_TYPES.NAMESPACE
            });
            sandboxManager.createSandbox({
                ...mockConfig,
                name: 'proxy-sandbox',
                type: SANDBOX_TYPES.PROXY
            });
            sandboxManager.createSandbox({
                ...mockConfig,
                name: 'iframe-sandbox',
                type: SANDBOX_TYPES.IFRAME
            });
        });

        it('应该返回指定类型的所有沙箱', () => {
            const namespaceSandboxes = sandboxManager.getSandboxesByType(SANDBOX_TYPES.NAMESPACE);
            const proxySandboxes = sandboxManager.getSandboxesByType(SANDBOX_TYPES.PROXY);
            const iframeSandboxes = sandboxManager.getSandboxesByType(SANDBOX_TYPES.IFRAME);

            expect(namespaceSandboxes).toHaveLength(1);
            expect(proxySandboxes).toHaveLength(1);
            expect(iframeSandboxes).toHaveLength(1);

            expect(namespaceSandboxes[0].getName()).toBe('namespace-sandbox');
            expect(proxySandboxes[0].getName()).toBe('proxy-sandbox');
            expect(iframeSandboxes[0].getName()).toBe('iframe-sandbox');
        });

        it('应该返回空数组当没有匹配类型的沙箱时', () => {
            sandboxManager.clear();
            const sandboxes = sandboxManager.getSandboxesByType(SANDBOX_TYPES.NAMESPACE);
            expect(sandboxes).toEqual([]);
        });
    });

    describe('clear', () => {
        beforeEach(() => {
            const config2 = { ...mockConfig, name: 'test-sandbox-2' };
            sandboxManager.createSandbox(mockConfig);
            sandboxManager.createSandbox(config2);
        });

        it('应该清除所有沙箱', () => {
            expect(sandboxManager.getAllSandboxes()).toHaveLength(2);

            sandboxManager.clear();

            expect(sandboxManager.getAllSandboxes()).toHaveLength(0);
        });

        it('应该销毁所有沙箱实例', () => {
            const sandboxes = sandboxManager.getAllSandboxes();
            const destroySpies = sandboxes.map(sandbox => vi.spyOn(sandbox, 'destroy'));

            sandboxManager.clear();

            destroySpies.forEach(spy => {
                expect(spy).toHaveBeenCalled();
            });
        });
    });

    describe('getStats', () => {
        beforeEach(() => {
            sandboxManager.createSandbox({
                ...mockConfig,
                name: 'namespace-sandbox',
                type: SANDBOX_TYPES.NAMESPACE
            });
            sandboxManager.createSandbox({
                ...mockConfig,
                name: 'proxy-sandbox',
                type: SANDBOX_TYPES.PROXY
            });
        });

        it('应该返回沙箱统计信息', () => {
            const stats = sandboxManager.getStats();

            expect(stats).toBeDefined();
            expect(stats.total).toBe(2);
            expect(stats.byType).toBeDefined();
            expect(stats.byType[SANDBOX_TYPES.NAMESPACE]).toBe(1);
            expect(stats.byType[SANDBOX_TYPES.PROXY]).toBe(1);
        });

        it('应该统计活跃沙箱数量', () => {
            const sandbox = sandboxManager.getSandbox('namespace-sandbox');
            sandbox?.active();

            const stats = sandboxManager.getStats();
            expect(stats.active).toBe(1);
        });
    });

    describe('沙箱生命周期管理', () => {
        beforeEach(() => {
            sandboxManager.createSandbox(mockConfig);
        });

        it('应该支持激活沙箱', () => {
            const sandbox = sandboxManager.getSandbox('test-sandbox');
            expect(sandbox?.isActive()).toBe(false);

            sandboxManager.activateSandbox('test-sandbox');

            expect(sandbox?.isActive()).toBe(true);
        });

        it('应该支持停用沙箱', () => {
            const sandbox = sandboxManager.getSandbox('test-sandbox');
            sandboxManager.activateSandbox('test-sandbox');
            expect(sandbox?.isActive()).toBe(true);

            sandboxManager.deactivateSandbox('test-sandbox');

            expect(sandbox?.isActive()).toBe(false);
        });

        it('激活不存在的沙箱应该不抛出错误', () => {
            expect(() => {
                sandboxManager.activateSandbox('non-existent');
            }).not.toThrow();
        });

        it('停用不存在的沙箱应该不抛出错误', () => {
            expect(() => {
                sandboxManager.deactivateSandbox('non-existent');
            }).not.toThrow();
        });
    });

    describe('配置验证', () => {
        it('应该验证name字段', () => {
            const invalidConfig = { ...mockConfig };
            delete (invalidConfig as any).name;

            expect(() => {
                sandboxManager.createSandbox(invalidConfig as SandboxConfig);
            }).toThrow('沙箱名称不能为空');
        });

        it('应该验证type字段', () => {
            const invalidConfig = { ...mockConfig };
            delete (invalidConfig as any).type;

            expect(() => {
                sandboxManager.createSandbox(invalidConfig as SandboxConfig);
            }).toThrow('沙箱类型不能为空');
        });

        it('应该验证container字段', () => {
            const invalidConfig = { ...mockConfig };
            delete (invalidConfig as any).container;

            expect(() => {
                sandboxManager.createSandbox(invalidConfig as SandboxConfig);
            }).toThrow('沙箱容器不能为空');
        });

        it('应该验证沙箱名称格式', () => {
            const invalidConfig = { ...mockConfig, name: '123-invalid' };

            expect(() => {
                sandboxManager.createSandbox(invalidConfig);
            }).toThrow('沙箱名称格式不正确');
        });
    });

    describe('事件发射', () => {
        it('应该发射沙箱创建事件', () => {
            const eventSpy = vi.fn();
            sandboxManager.on('sandboxCreated', eventSpy);

            sandboxManager.createSandbox(mockConfig);

            expect(eventSpy).toHaveBeenCalledWith(
                expect.objectContaining({
                    name: 'test-sandbox',
                    type: SANDBOX_TYPES.NAMESPACE
                })
            );
        });

        it('应该发射沙箱销毁事件', () => {
            sandboxManager.createSandbox(mockConfig);

            const eventSpy = vi.fn();
            sandboxManager.on('sandboxDestroyed', eventSpy);

            sandboxManager.destroySandbox('test-sandbox');

            expect(eventSpy).toHaveBeenCalledWith(
                expect.objectContaining({
                    name: 'test-sandbox'
                })
            );
        });
    });
});
