/**
 * 测试环境设置
 */

import { vi } from 'vitest';

// 模拟浏览器环境
Object.defineProperty(window, 'location', {
    value: {
        href: 'http://localhost:3000',
        origin: 'http://localhost:3000',
        protocol: 'http:',
        host: 'localhost:3000',
        hostname: 'localhost',
        port: '3000',
        pathname: '/',
        search: '',
        hash: ''
    },
    writable: true
});

// 模拟 fetch API
global.fetch = vi.fn();

// 模拟 Web Worker
global.Worker = vi.fn().mockImplementation(() => ({
    postMessage: vi.fn(),
    terminate: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn()
}));

// 模拟 WebAssembly
global.WebAssembly = {
    compile: vi.fn(),
    compileStreaming: vi.fn(),
    instantiate: vi.fn(),
    Module: vi.fn(),
    Instance: vi.fn(),
    Memory: vi.fn(),
    Table: vi.fn(),
    Global: vi.fn(),
    CompileError: Error,
    RuntimeError: Error,
    LinkError: Error
} as any;

// 模拟 IndexedDB
const mockIDBRequest = {
    result: null,
    error: null,
    onsuccess: null,
    onerror: null,
    onupgradeneeded: null
};

global.indexedDB = {
    open: vi.fn().mockReturnValue(mockIDBRequest),
    deleteDatabase: vi.fn().mockReturnValue(mockIDBRequest),
    databases: vi.fn().mockResolvedValue([])
} as any;

// 模拟 localStorage
const localStorageMock = {
    getItem: vi.fn(),
    setItem: vi.fn(),
    removeItem: vi.fn(),
    clear: vi.fn(),
    length: 0,
    key: vi.fn()
};

Object.defineProperty(window, 'localStorage', {
    value: localStorageMock
});

// 模拟 sessionStorage
Object.defineProperty(window, 'sessionStorage', {
    value: localStorageMock
});

// 模拟 MutationObserver
global.MutationObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    disconnect: vi.fn(),
    takeRecords: vi.fn()
}));

// 模拟 ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn()
}));

// 模拟 IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn()
}));

// 设置测试环境标识
(window as any).__POWERED_BY_MICRO_CORE__ = true;
(window as any).__MICRO_CORE_TEST_ENV__ = true;

// 清理函数
afterEach(() => {
    vi.clearAllMocks();
    localStorageMock.clear();
});