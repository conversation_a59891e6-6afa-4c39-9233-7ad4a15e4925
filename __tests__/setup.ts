/**
 * Vitest 测试环境设置文件
 */

import { vi } from 'vitest'

// 模拟浏览器环境
Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: vi.fn().mockImplementation(query => ({
        matches: false,
        media: query,
        onchange: null,
        addListener: vi.fn(), // deprecated
        removeListener: vi.fn(), // deprecated
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn(),
    })),
})

// 模拟 ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn(),
}))

// 模拟 IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn(),
}))

// 模拟 MutationObserver
global.MutationObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    disconnect: vi.fn(),
    takeRecords: vi.fn(),
}))

// 模拟 fetch
global.fetch = vi.fn()

// 模拟 localStorage
const localStorageMock = {
    getItem: vi.fn(),
    setItem: vi.fn(),
    removeItem: vi.fn(),
    clear: vi.fn(),
}
global.localStorage = localStorageMock

// 模拟 sessionStorage
const sessionStorageMock = {
    getItem: vi.fn(),
    setItem: vi.fn(),
    removeItem: vi.fn(),
    clear: vi.fn(),
}
global.sessionStorage = sessionStorageMock

// 模拟 URL
global.URL.createObjectURL = vi.fn()
global.URL.revokeObjectURL = vi.fn()

// 模拟 console 方法（在测试中静默）
const originalConsole = global.console
global.console = {
    ...originalConsole,
    log: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
    info: vi.fn(),
    debug: vi.fn(),
}

// 设置测试环境变量
process.env.NODE_ENV = 'test'
process.env.MICRO_CORE_DEBUG = 'false'

// 全局测试工具函数
global.sleep = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

// 模拟微前端环境标识
global.__MICRO_CORE__ = true
global.__POWERED_BY_MICRO_CORE__ = true

// 清理函数 - 在每个测试后运行
afterEach(() => {
    vi.clearAllMocks()
    vi.clearAllTimers()

    // 清理 DOM
    document.body.innerHTML = ''
    document.head.innerHTML = ''

    // 重置 localStorage 和 sessionStorage
    localStorageMock.getItem.mockClear()
    localStorageMock.setItem.mockClear()
    localStorageMock.removeItem.mockClear()
    localStorageMock.clear.mockClear()

    sessionStorageMock.getItem.mockClear()
    sessionStorageMock.setItem.mockClear()
    sessionStorageMock.removeItem.mockClear()
    sessionStorageMock.clear.mockClear()
})

// 全局错误处理
process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason)
})

process.on('uncaughtException', (error) => {
    console.error('Uncaught Exception:', error)
})