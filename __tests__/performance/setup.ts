/**
 * @fileoverview 性能基准测试环境设置
 * <AUTHOR> <<EMAIL>>
 */

import { afterAll, afterEach, beforeAll, beforeEach } from 'vitest';

// 性能监控工具
interface PerformanceMetrics {
    startTime: number;
    endTime: number;
    duration: number;
    memoryUsage: {
        used: number;
        total: number;
    };
    cpuUsage?: number;
}

// 全局性能监控对象
declare global {
    var __PERFORMANCE_MONITOR__: {
        startMeasure: (name: string) => void;
        endMeasure: (name: string) => PerformanceMetrics;
        getMetrics: () => Record<string, PerformanceMetrics>;
        clearMetrics: () => void;
        setThreshold: (name: string, threshold: number) => void;
        checkThresholds: () => { passed: boolean; violations: string[] };
    };
}

// 性能监控实现
const performanceMonitor = {
    metrics: {} as Record<string, PerformanceMetrics>,
    thresholds: {} as Record<string, number>,
    activeTimers: {} as Record<string, number>,

    startMeasure(name: string) {
        this.activeTimers[name] = performance.now();

        // 记录内存使用情况（如果可用）
        if (typeof process !== 'undefined' && process.memoryUsage) {
            const memUsage = process.memoryUsage();
            this.metrics[name] = {
                startTime: this.activeTimers[name],
                endTime: 0,
                duration: 0,
                memoryUsage: {
                    used: memUsage.heapUsed,
                    total: memUsage.heapTotal
                }
            };
        } else {
            this.metrics[name] = {
                startTime: this.activeTimers[name],
                endTime: 0,
                duration: 0,
                memoryUsage: {
                    used: 0,
                    total: 0
                }
            };
        }
    },

    endMeasure(name: string): PerformanceMetrics {
        const endTime = performance.now();
        const startTime = this.activeTimers[name];

        if (!startTime) {
            throw new Error(`性能测量 "${name}" 未开始`);
        }

        const duration = endTime - startTime;

        // 更新内存使用情况
        let memoryUsage = { used: 0, total: 0 };
        if (typeof process !== 'undefined' && process.memoryUsage) {
            const memUsage = process.memoryUsage();
            memoryUsage = {
                used: memUsage.heapUsed,
                total: memUsage.heapTotal
            };
        }

        const metrics: PerformanceMetrics = {
            startTime,
            endTime,
            duration,
            memoryUsage
        };

        this.metrics[name] = metrics;
        delete this.activeTimers[name];

        return metrics;
    },

    getMetrics() {
        return { ...this.metrics };
    },

    clearMetrics() {
        this.metrics = {};
        this.activeTimers = {};
    },

    setThreshold(name: string, threshold: number) {
        this.thresholds[name] = threshold;
    },

    checkThresholds() {
        const violations: string[] = [];

        for (const [name, threshold] of Object.entries(this.thresholds)) {
            const metric = this.metrics[name];
            if (metric && metric.duration > threshold) {
                violations.push(`${name}: ${metric.duration.toFixed(2)}ms > ${threshold}ms`);
            }
        }

        return {
            passed: violations.length === 0,
            violations
        };
    }
};

// 挂载到全局对象
global.__PERFORMANCE_MONITOR__ = performanceMonitor;

// 模拟浏览器环境
beforeAll(() => {
    // 模拟 Performance API
    if (!global.performance) {
        global.performance = {
            now: () => Date.now(),
            mark: () => { },
            measure: () => { },
            getEntriesByName: () => [],
            getEntriesByType: () => [],
            clearMarks: () => { },
            clearMeasures: () => { }
        } as any;
    }

    // 模拟 requestAnimationFrame
    if (!global.requestAnimationFrame) {
        global.requestAnimationFrame = (callback: FrameRequestCallback) => {
            return setTimeout(callback, 16) as any;
        };
    }

    // 模拟 cancelAnimationFrame
    if (!global.cancelAnimationFrame) {
        global.cancelAnimationFrame = (id: number) => {
            clearTimeout(id);
        };
    }

    // 模拟 requestIdleCallback
    if (!global.requestIdleCallback) {
        global.requestIdleCallback = (callback: IdleRequestCallback) => {
            return setTimeout(() => {
                callback({
                    didTimeout: false,
                    timeRemaining: () => 50
                });
            }, 1) as any;
        };
    }

    // 模拟 IntersectionObserver
    if (!global.IntersectionObserver) {
        global.IntersectionObserver = class IntersectionObserver {
            constructor(callback: IntersectionObserverCallback) { }
            observe() { }
            unobserve() { }
            disconnect() { }
        } as any;
    }

    // 模拟 ResizeObserver
    if (!global.ResizeObserver) {
        global.ResizeObserver = class ResizeObserver {
            constructor(callback: ResizeObserverCallback) { }
            observe() { }
            unobserve() { }
            disconnect() { }
        } as any;
    }

    // 模拟 MutationObserver
    if (!global.MutationObserver) {
        global.MutationObserver = class MutationObserver {
            constructor(callback: MutationCallback) { }
            observe() { }
            disconnect() { }
            takeRecords() { return []; }
        } as any;
    }

    console.log('性能测试环境初始化完成');
});

afterAll(() => {
    // 输出性能测试总结
    const metrics = performanceMonitor.getMetrics();
    const thresholdCheck = performanceMonitor.checkThresholds();

    console.log('\n=== 性能测试总结 ===');
    console.log('测试指标:');

    for (const [name, metric] of Object.entries(metrics)) {
        console.log(`  ${name}:`);
        console.log(`    耗时: ${metric.duration.toFixed(2)}ms`);
        console.log(`    内存: ${(metric.memoryUsage.used / 1024 / 1024).toFixed(2)}MB`);
    }

    if (!thresholdCheck.passed) {
        console.log('\n性能阈值违规:');
        thresholdCheck.violations.forEach(violation => {
            console.log(`  ❌ ${violation}`);
        });
    } else {
        console.log('\n✅ 所有性能指标均在阈值范围内');
    }

    console.log('===================\n');
});

beforeEach(() => {
    // 每个测试前清理性能指标
    performanceMonitor.clearMetrics();
});

afterEach(() => {
    // 每个测试后检查是否有未完成的测量
    const activeTimers = Object.keys(performanceMonitor.activeTimers);
    if (activeTimers.length > 0) {
        console.warn(`警告: 发现未完成的性能测量: ${activeTimers.join(', ')}`);
    }
});

// 导出性能测试工具函数
export function measurePerformance<T>(
    name: string,
    fn: () => T | Promise<T>,
    threshold?: number
): Promise<{ result: T; metrics: PerformanceMetrics }> {
    return new Promise(async (resolve, reject) => {
        try {
            if (threshold) {
                performanceMonitor.setThreshold(name, threshold);
            }

            performanceMonitor.startMeasure(name);
            const result = await fn();
            const metrics = performanceMonitor.endMeasure(name);

            resolve({ result, metrics });
        } catch (error) {
            reject(error);
        }
    });
}

export function createPerformanceTest(
    name: string,
    threshold: number,
    testFn: () => void | Promise<void>
) {
    return async () => {
        const { metrics } = await measurePerformance(name, testFn, threshold);

        if (metrics.duration > threshold) {
            throw new Error(
                `性能测试失败: ${name} 耗时 ${metrics.duration.toFixed(2)}ms，超过阈值 ${threshold}ms`
            );
        }

        return metrics;
    };
}

export function createMemoryTest(
    name: string,
    maxMemoryMB: number,
    testFn: () => void | Promise<void>
) {
    return async () => {
        const { metrics } = await measurePerformance(name, testFn);
        const memoryUsedMB = metrics.memoryUsage.used / 1024 / 1024;

        if (memoryUsedMB > maxMemoryMB) {
            throw new Error(
                `内存测试失败: ${name} 使用内存 ${memoryUsedMB.toFixed(2)}MB，超过限制 ${maxMemoryMB}MB`
            );
        }

        return metrics;
    };
}

export function createLoadTest(
    name: string,
    iterations: number,
    maxAverageTime: number,
    testFn: () => void | Promise<void>
) {
    return async () => {
        const results: number[] = [];

        for (let i = 0; i < iterations; i++) {
            const { metrics } = await measurePerformance(`${name}-${i}`, testFn);
            results.push(metrics.duration);
        }

        const averageTime = results.reduce((sum, time) => sum + time, 0) / results.length;
        const minTime = Math.min(...results);
        const maxTime = Math.max(...results);

        if (averageTime > maxAverageTime) {
            throw new Error(
                `负载测试失败: ${name} 平均耗时 ${averageTime.toFixed(2)}ms，超过限制 ${maxAverageTime}ms`
            );
        }

        return {
            iterations,
            averageTime,
            minTime,
            maxTime,
            results
        };
    };
}