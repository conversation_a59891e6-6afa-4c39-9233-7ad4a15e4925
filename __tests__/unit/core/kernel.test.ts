/**
 * 微内核核心功能测试
 */

import { MicroCoreKernel } from '@micro-core/core';
import { beforeEach, describe, expect, it, vi } from 'vitest';

describe('MicroCoreKernel', () => {
    let kernel: MicroCoreKernel;

    beforeEach(() => {
        kernel = new MicroCoreKernel();
    });

    describe('基础功能', () => {
        it('应该能够创建内核实例', () => {
            expect(kernel).toBeInstanceOf(MicroCoreKernel);
            expect(kernel.isStarted()).toBe(false);
        });

        it('应该有完整的管理器', () => {
            expect(kernel.lifecycle).toBeDefined();
            expect(kernel.plugins).toBeDefined();
            expect(kernel.appRegistry).toBeDefined();
            expect(kernel.sandboxManager).toBeDefined();
            expect(kernel.routerManager).toBeDefined();
            expect(kernel.communicationManager).toBeDefined();
            expect(kernel.eventBus).toBeDefined();
            expect(kernel.resourceManager).toBeDefined();
        });
    });

    describe('应用注册', () => {
        it('应该能够注册应用', () => {
            const appConfig = {
                name: 'test-app',
                entry: 'http://localhost:3000/app.js',
                container: '#app-container',
                activeWhen: '/test-app'
            };

            expect(() => kernel.registerApplication(appConfig)).not.toThrow();

            const registeredApp = kernel.getApplication('test-app');
            expect(registeredApp).toBeDefined();
            expect(registeredApp.name).toBe('test-app');
        });

        it('应该拒绝重复注册同名应用', () => {
            const appConfig = {
                name: 'duplicate-app',
                entry: 'http://localhost:3000/app.js',
                container: '#app-container',
                activeWhen: '/duplicate-app'
            };

            kernel.registerApplication(appConfig);

            expect(() => kernel.registerApplication(appConfig)).toThrow();
        });

        it('应该验证应用配置', () => {
            expect(() => kernel.registerApplication({} as any)).toThrow();
            expect(() => kernel.registerApplication({ name: '' } as any)).toThrow();
            expect(() => kernel.registerApplication({ name: 'test' } as any)).toThrow();
        });
    });

    describe('生命周期管理', () => {
        it('应该能够启动内核', async () => {
            expect(kernel.isStarted()).toBe(false);

            await kernel.start();

            expect(kernel.isStarted()).toBe(true);
        });

        it('应该能够停止内核', async () => {
            await kernel.start();
            expect(kernel.isStarted()).toBe(true);

            await kernel.stop();

            expect(kernel.isStarted()).toBe(false);
        });
    });

    describe('插件系统', () => {
        it('应该能够注册插件', () => {
            const mockPlugin = {
                name: 'test-plugin',
                version: '1.0.0',
                install: vi.fn()
            };

            kernel.use(mockPlugin);

            expect(mockPlugin.install).toHaveBeenCalledWith(kernel);
        });

        it('应该能够卸载插件', () => {
            const mockPlugin = {
                name: 'test-plugin',
                version: '1.0.0',
                install: vi.fn(),
                uninstall: vi.fn()
            };

            kernel.use(mockPlugin);
            kernel.unuse('test-plugin');

            expect(mockPlugin.uninstall).toHaveBeenCalledWith(kernel);
        });
    });

    describe('事件系统', () => {
        it('应该能够发布和订阅事件', () => {
            const callback = vi.fn();

            kernel.eventBus.on('test-event', callback);
            kernel.eventBus.emit('test-event', { data: 'test' });

            expect(callback).toHaveBeenCalledWith({ data: 'test' });
        });

        it('应该能够取消事件订阅', () => {
            const callback = vi.fn();

            kernel.eventBus.on('test-event', callback);
            kernel.eventBus.off('test-event', callback);
            kernel.eventBus.emit('test-event', { data: 'test' });

            expect(callback).not.toHaveBeenCalled();
        });
    });

    describe('错误处理', () => {
        it('应该能够捕获和处理错误', () => {
            const errorHandler = vi.fn();
            kernel.onError(errorHandler);

            const error = new Error('Test error');
            kernel.handleError(error);

            expect(errorHandler).toHaveBeenCalledWith(error);
        });
    });
});