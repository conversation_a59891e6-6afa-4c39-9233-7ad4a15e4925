/**
 * @fileoverview 适配器系统集成测试
 * <AUTHOR> <<EMAIL>>
 */

import { AngularAdapter, ReactAdapter, VueAdapter } from '@micro-core/adapters';
import { MicroCore } from '@micro-core/core';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

describe('适配器系统集成测试', () => {
    let microCore: MicroCore;
    let reactAdapter: ReactAdapter;
    let vueAdapter: VueAdapter;
    let angularAdapter: AngularAdapter;

    beforeEach(() => {
        microCore = new MicroCore({
            debug: true,
            sandbox: true
        });

        reactAdapter = new ReactAdapter();
        vueAdapter = new VueAdapter();
        angularAdapter = new AngularAdapter();
    });

    afterEach(() => {
        microCore.destroy();
    });

    describe('适配器注册和检测', () => {
        it('应该能够自动检测应用框架类型', () => {
            // 模拟React应用环境
            global.React = { version: '18.0.0' };
            global.ReactDOM = { version: '18.0.0' };

            const detectedFramework = microCore.detectFramework();
            expect(detectedFramework).toBe('react');

            // 清理
            delete global.React;
            delete global.ReactDOM;
        });

        it('应该能够注册多个框架适配器', () => {
            microCore.registerAdapter('react', reactAdapter);
            microCore.registerAdapter('vue', vueAdapter);
            microCore.registerAdapter('angular', angularAdapter);

            const adapters = microCore.getAdapters();
            expect(Object.keys(adapters)).toEqual(['react', 'vue', 'angular']);
        });

        it('应该能够根据应用配置选择正确的适配器', () => {
            microCore.registerAdapter('react', reactAdapter);
            microCore.registerAdapter('vue', vueAdapter);

            const mockReactApp = global.createMockApp('react-app', 'react');
            const mockVueApp = global.createMockApp('vue-app', 'vue');

            microCore.registerApp({
                name: 'react-app',
                entry: mockReactApp.entry,
                container: '#react-container',
                activeWhen: '/react',
                framework: 'react'
            });

            microCore.registerApp({
                name: 'vue-app',
                entry: mockVueApp.entry,
                container: '#vue-container',
                activeWhen: '/vue',
                framework: 'vue'
            });

            const reactAppAdapter = microCore.getAppAdapter('react-app');
            const vueAppAdapter = microCore.getAppAdapter('vue-app');

            expect(reactAppAdapter).toBe(reactAdapter);
            expect(vueAppAdapter).toBe(vueAdapter);
        });
    });

    describe('React适配器集成', () => {
        beforeEach(() => {
            microCore.registerAdapter('react', reactAdapter);

            // 模拟React环境
            global.React = {
                version: '18.0.0',
                createElement: vi.fn(),
                Component: class Component { },
                useState: vi.fn(),
                useEffect: vi.fn()
            };

            global.ReactDOM = {
                version: '18.0.0',
                render: vi.fn(),
                unmountComponentAtNode: vi.fn(),
                createRoot: vi.fn(() => ({
                    render: vi.fn(),
                    unmount: vi.fn()
                }))
            };
        });

        afterEach(() => {
            delete global.React;
            delete global.ReactDOM;
        });

        it('应该能够挂载React应用', async () => {
            const mockApp = global.createMockApp('react-app', 'react');
            const container = global.createMockContainer('react-container');

            microCore.registerApp({
                name: 'react-app',
                entry: mockApp.entry,
                container: '#react-container',
                activeWhen: '/react',
                framework: 'react'
            });

            // 模拟React应用导出
            global.fetch = vi.fn().mockResolvedValue({
                ok: true,
                text: () => Promise.resolve(`
                    window['react-app'] = {
                        mount: function(props) {
                            const element = React.createElement('div', null, 'React App');
                            ReactDOM.render(element, props.container);
                            return Promise.resolve();
                        },
                        unmount: function(props) {
                            ReactDOM.unmountComponentAtNode(props.container);
                            return Promise.resolve();
                        }
                    };
                `)
            });

            await microCore.startApp('react-app');

            const app = microCore.getApp('react-app');
            expect(app?.status).toBe('MOUNTED');
            expect(global.ReactDOM.render).toHaveBeenCalled();
        });

        it('应该能够传递props给React应用', async () => {
            const mockApp = global.createMockApp('react-app', 'react');
            const container = global.createMockContainer('react-container');

            const testProps = {
                user: { id: 1, name: 'Test User' },
                theme: 'dark'
            };

            microCore.registerApp({
                name: 'react-app',
                entry: mockApp.entry,
                container: '#react-container',
                activeWhen: '/react',
                framework: 'react',
                props: testProps
            });

            const mountSpy = vi.fn();
            global.fetch = vi.fn().mockResolvedValue({
                ok: true,
                text: () => Promise.resolve(`
                    window['react-app'] = {
                        mount: ${mountSpy.toString()},
                        unmount: function() { return Promise.resolve(); }
                    };
                `)
            });

            await microCore.startApp('react-app');

            expect(mountSpy).toHaveBeenCalledWith(
                expect.objectContaining(testProps)
            );
        });

        it('应该能够处理React应用的错误边界', async () => {
            const mockApp = global.createMockApp('react-app', 'react');
            const container = global.createMockContainer('react-container');

            microCore.registerApp({
                name: 'react-app',
                entry: mockApp.entry,
                container: '#react-container',
                activeWhen: '/react',
                framework: 'react'
            });

            // 模拟React应用挂载时出错
            global.fetch = vi.fn().mockResolvedValue({
                ok: true,
                text: () => Promise.resolve(`
                    window['react-app'] = {
                        mount: function() {
                            throw new Error('React component error');
                        },
                        unmount: function() { return Promise.resolve(); }
                    };
                `)
            });

            await expect(microCore.startApp('react-app')).rejects.toThrow('React component error');

            const app = microCore.getApp('react-app');
            expect(app?.status).toBe('MOUNT_ERROR');
        });
    });

    describe('Vue适配器集成', () => {
        beforeEach(() => {
            microCore.registerAdapter('vue', vueAdapter);

            // 模拟Vue环境
            global.Vue = {
                version: '3.0.0',
                createApp: vi.fn(() => ({
                    mount: vi.fn(),
                    unmount: vi.fn(),
                    use: vi.fn(),
                    provide: vi.fn()
                })),
                h: vi.fn(),
                ref: vi.fn(),
                reactive: vi.fn()
            };
        });

        afterEach(() => {
            delete global.Vue;
        });

        it('应该能够挂载Vue应用', async () => {
            const mockApp = global.createMockApp('vue-app', 'vue');
            const container = global.createMockContainer('vue-container');

            microCore.registerApp({
                name: 'vue-app',
                entry: mockApp.entry,
                container: '#vue-container',
                activeWhen: '/vue',
                framework: 'vue'
            });

            const mockVueApp = {
                mount: vi.fn(),
                unmount: vi.fn()
            };

            global.fetch = vi.fn().mockResolvedValue({
                ok: true,
                text: () => Promise.resolve(`
                    window['vue-app'] = {
                        mount: function(props) {
                            const app = Vue.createApp({
                                template: '<div>Vue App</div>'
                            });
                            app.mount(props.container);
                            return Promise.resolve();
                        },
                        unmount: function(props) {
                            // Vue 3 unmount logic
                            return Promise.resolve();
                        }
                    };
                `)
            });

            await microCore.startApp('vue-app');

            const app = microCore.getApp('vue-app');
            expect(app?.status).toBe('MOUNTED');
            expect(global.Vue.createApp).toHaveBeenCalled();
        });

        it('应该能够处理Vue 2和Vue 3的差异', async () => {
            // 测试Vue 2
            global.Vue = {
                version: '2.6.0',
                extend: vi.fn(),
                component: vi.fn(),
                new: vi.fn()
            };

            const vue2Adapter = new VueAdapter({ version: 2 });
            microCore.registerAdapter('vue2', vue2Adapter);

            const mockApp = global.createMockApp('vue2-app', 'vue');
            microCore.registerApp({
                name: 'vue2-app',
                entry: mockApp.entry,
                container: '#vue2-container',
                activeWhen: '/vue2',
                framework: 'vue2'
            });

            global.createMockContainer('vue2-container');

            global.fetch = vi.fn().mockResolvedValue({
                ok: true,
                text: () => Promise.resolve(`
                    window['vue2-app'] = {
                        mount: function(props) {
                            new Vue({
                                el: props.container,
                                template: '<div>Vue 2 App</div>'
                            });
                            return Promise.resolve();
                        },
                        unmount: function() { return Promise.resolve(); }
                    };
                `)
            });

            await microCore.startApp('vue2-app');

            const app = microCore.getApp('vue2-app');
            expect(app?.status).toBe('MOUNTED');
        });
    });

    describe('Angular适配器集成', () => {
        beforeEach(() => {
            microCore.registerAdapter('angular', angularAdapter);

            // 模拟Angular环境
            global.ng = {
                version: '15.0.0',
                core: {
                    createApplication: vi.fn(),
                    bootstrapApplication: vi.fn()
                },
                platformBrowser: {
                    platformBrowser: vi.fn()
                }
            };
        });

        afterEach(() => {
            delete global.ng;
        });

        it('应该能够挂载Angular应用', async () => {
            const mockApp = global.createMockApp('angular-app', 'angular');
            const container = global.createMockContainer('angular-container');

            microCore.registerApp({
                name: 'angular-app',
                entry: mockApp.entry,
                container: '#angular-container',
                activeWhen: '/angular',
                framework: 'angular'
            });

            global.fetch = vi.fn().mockResolvedValue({
                ok: true,
                text: () => Promise.resolve(`
                    window['angular-app'] = {
                        mount: function(props) {
                            // Angular bootstrap logic
                            return Promise.resolve();
                        },
                        unmount: function() {
                            // Angular destroy logic
                            return Promise.resolve();
                        }
                    };
                `)
            });

            await microCore.startApp('angular-app');

            const app = microCore.getApp('angular-app');
            expect(app?.status).toBe('MOUNTED');
        });
    });

    describe('适配器间协作', () => {
        beforeEach(() => {
            microCore.registerAdapter('react', reactAdapter);
            microCore.registerAdapter('vue', vueAdapter);
            microCore.registerAdapter('angular', angularAdapter);

            // 模拟所有框架环境
            global.React = { version: '18.0.0' };
            global.ReactDOM = { version: '18.0.0', render: vi.fn(), unmountComponentAtNode: vi.fn() };
            global.Vue = { version: '3.0.0', createApp: vi.fn(() => ({ mount: vi.fn(), unmount: vi.fn() })) };
            global.ng = { version: '15.0.0' };
        });

        afterEach(() => {
            delete global.React;
            delete global.ReactDOM;
            delete global.Vue;
            delete global.ng;
        });

        it('应该能够同时运行多个不同框架的应用', async () => {
            const mockApps = [
                global.createMockApp('react-app', 'react'),
                global.createMockApp('vue-app', 'vue'),
                global.createMockApp('angular-app', 'angular')
            ];

            mockApps.forEach((app, index) => {
                microCore.registerApp({
                    name: app.name,
                    entry: app.entry,
                    container: `#${app.name}-container`,
                    activeWhen: `/${app.name}`,
                    framework: app.framework
                });
                global.createMockContainer(`${app.name}-container`);
            });

            // 模拟所有应用的导出
            global.fetch = vi.fn().mockImplementation((url) => {
                const app = mockApps.find(app => url.startsWith(app.baseUrl));
                return Promise.resolve({
                    ok: true,
                    text: () => Promise.resolve(`
                        window['${app.name}'] = {
                            mount: function(props) {
                                console.log('Mounting ${app.framework} app:', '${app.name}');
                                return Promise.resolve();
                            },
                            unmount: function(props) {
                                console.log('Unmounting ${app.framework} app:', '${app.name}');
                                return Promise.resolve();
                            }
                        };
                    `)
                });
            });

            // 并行启动所有应用
            await Promise.all(mockApps.map(app => microCore.startApp(app.name)));

            // 验证所有应用都已启动
            mockApps.forEach(mockApp => {
                const app = microCore.getApp(mockApp.name);
                expect(app?.status).toBe('MOUNTED');
            });
        });

        it('应该能够在不同框架应用间共享状态', async () => {
            const mockApps = [
                global.createMockApp('react-app', 'react'),
                global.createMockApp('vue-app', 'vue')
            ];

            mockApps.forEach(app => {
                microCore.registerApp({
                    name: app.name,
                    entry: app.entry,
                    container: `#${app.name}-container`,
                    activeWhen: `/${app.name}`,
                    framework: app.framework
                });
                global.createMockContainer(`${app.name}-container`);
            });

            global.fetch = vi.fn().mockImplementation((url) => {
                const app = mockApps.find(app => url.startsWith(app.baseUrl));
                return Promise.resolve({
                    ok: true,
                    text: () => Promise.resolve(`
                        window['${app.name}'] = {
                            mount: function(props) {
                                // 设置应用特定状态
                                window.__MICRO_CORE__.setState('${app.name}:ready', true);
                                return Promise.resolve();
                            },
                            unmount: function() { return Promise.resolve(); }
                        };
                    `)
                });
            });

            await Promise.all(mockApps.map(app => microCore.startApp(app.name)));

            // 验证状态共享
            const reactReady = microCore.getState('react-app:ready');
            const vueReady = microCore.getState('vue-app:ready');

            expect(reactReady).toBe(true);
            expect(vueReady).toBe(true);
        });

        it('应该能够处理适配器间的通信', async () => {
            const mockApps = [
                global.createMockApp('react-app', 'react'),
                global.createMockApp('vue-app', 'vue')
            ];

            mockApps.forEach(app => {
                microCore.registerApp({
                    name: app.name,
                    entry: app.entry,
                    container: `#${app.name}-container`,
                    activeWhen: `/${app.name}`,
                    framework: app.framework
                });
                global.createMockContainer(`${app.name}-container`);
            });

            const communicationMock = global.mockAppCommunication();

            global.fetch = vi.fn().mockImplementation((url) => {
                const app = mockApps.find(app => url.startsWith(app.baseUrl));
                return Promise.resolve({
                    ok: true,
                    text: () => Promise.resolve(`
                        window['${app.name}'] = {
                            mount: function(props) {
                                // 发送就绪消息
                                window.postMessage({
                                    type: 'APP_READY',
                                    from: '${app.name}',
                                    framework: '${app.framework}'
                                }, '*');
                                return Promise.resolve();
                            },
                            unmount: function() { return Promise.resolve(); }
                        };
                    `)
                });
            });

            await Promise.all(mockApps.map(app => microCore.startApp(app.name)));

            await new Promise(resolve => setTimeout(resolve, 100));

            expect(communicationMock.messages.length).toBeGreaterThanOrEqual(2);

            const reactMessage = communicationMock.messages.find(m =>
                m.message.from === 'react-app'
            );
            const vueMessage = communicationMock.messages.find(m =>
                m.message.from === 'vue-app'
            );

            expect(reactMessage).toBeDefined();
            expect(vueMessage).toBeDefined();

            communicationMock.restore();
        });
    });

    describe('适配器错误处理', () => {
        beforeEach(() => {
            microCore.registerAdapter('react', reactAdapter);
            microCore.registerAdapter('vue', vueAdapter);
        });

        it('应该能够处理适配器初始化失败', async () => {
            const faultyAdapter = {
                name: 'faulty-adapter',
                version: '1.0.0',
                initialize: vi.fn().mockRejectedValue(new Error('适配器初始化失败')),
                mount: vi.fn(),
                unmount: vi.fn(),
                update: vi.fn()
            };

            microCore.registerAdapter('faulty', faultyAdapter);

            const mockApp = global.createMockApp('faulty-app', 'faulty');
            microCore.registerApp({
                name: 'faulty-app',
                entry: mockApp.entry,
                container: '#faulty-container',
                activeWhen: '/faulty',
                framework: 'faulty'
            });

            global.createMockContainer('faulty-container');

            await expect(microCore.startApp('faulty-app')).rejects.toThrow();

            const app = microCore.getApp('faulty-app');
            expect(app?.status).toBe('MOUNT_ERROR');
        });

        it('应该能够隔离适配器错误不影响其他应用', async () => {
            const mockApps = [
                global.createMockApp('react-app', 'react'),
                global.createMockApp('vue-app', 'vue')
            ];

            mockApps.forEach(app => {
                microCore.registerApp({
                    name: app.name,
                    entry: app.entry,
                    container: `#${app.name}-container`,
                    activeWhen: `/${app.name}`,
                    framework: app.framework
                });
                global.createMockContainer(`${app.name}-container`);
            });

            // 模拟React应用出错
            global.fetch = vi.fn().mockImplementation((url) => {
                const app = mockApps.find(app => url.startsWith(app.baseUrl));

                if (app.framework === 'react') {
                    return Promise.resolve({
                        ok: true,
                        text: () => Promise.resolve(`
                            window['${app.name}'] = {
                                mount: function() {
                                    throw new Error('React适配器错误');
                                },
                                unmount: function() { return Promise.resolve(); }
                            };
                        `)
                    });
                }

                return Promise.resolve({
                    ok: true,
                    text: () => Promise.resolve(`
                        window['${app.name}'] = {
                            mount: function() { return Promise.resolve(); },
                            unmount: function() { return Promise.resolve(); }
                        };
                    `)
                });
            });

            // React应用启动失败
            await expect(microCore.startApp('react-app')).rejects.toThrow('React适配器错误');

            // Vue应用应该正常启动
            await expect(microCore.startApp('vue-app')).resolves.not.toThrow();

            const reactApp = microCore.getApp('react-app');
            const vueApp = microCore.getApp('vue-app');

            expect(reactApp?.status).toBe('MOUNT_ERROR');
            expect(vueApp?.status).toBe('MOUNTED');
        });

        it('应该能够在适配器卸载时清理资源', async () => {
            const cleanupSpy = vi.fn();
            const testAdapter = {
                name: 'test-adapter',
                version: '1.0.0',
                initialize: vi.fn(),
                mount: vi.fn(),
                unmount: vi.fn(),
                update: vi.fn(),
                destroy: cleanupSpy
            };

            microCore.registerAdapter('test', testAdapter);

            const mockApp = global.createMockApp('test-app', 'test');
            microCore.registerApp({
                name: 'test-app',
                entry: mockApp.entry,
                container: '#test-container',
                activeWhen: '/test',
                framework: 'test'
            });

            global.createMockContainer('test-container');

            global.fetch = vi.fn().mockResolvedValue({
                ok: true,
                text: () => Promise.resolve(`
                    window['test-app'] = {
                        mount: function() { return Promise.resolve(); },
                        unmount: function() { return Promise.resolve(); }
                    };
                `)
            });

            await microCore.startApp('test-app');
            await microCore.stopApp('test-app');

            microCore.unregisterAdapter('test');

            expect(cleanupSpy).toHaveBeenCalled();
        });
    });

    describe('适配器性能优化', () => {
        beforeEach(() => {
            microCore.registerAdapter('react', reactAdapter);
            microCore.registerAdapter('vue', vueAdapter);
        });

        it('应该能够缓存适配器实例', () => {
            const adapter1 = microCore.getAdapter('react');
            const adapter2 = microCore.getAdapter('react');

            expect(adapter1).toBe(adapter2);
        });

        it('应该能够预加载适配器资源', async () => {
            const preloadSpy = vi.fn();
            const optimizedAdapter = {
                ...reactAdapter,
                preload: preloadSpy
            };

            microCore.registerAdapter('optimized-react', optimizedAdapter);

            await microCore.preloadAdapter('optimized-react');

            expect(preloadSpy).toHaveBeenCalled();
        });

        it('应该能够监控适配器性能指标', async () => {
            const performanceMonitor = global.mockPerformanceMonitor();

            const mockApp = global.createMockApp('react-app', 'react');
            microCore.registerApp({
                name: 'react-app',
                entry: mockApp.entry,
                container: '#react-container',
                activeWhen: '/react',
                framework: 'react'
            });

            global.createMockContainer('react-container');

            global.fetch = vi.fn().mockResolvedValue({
                ok: true,
                text: () => Promise.resolve(`
                    window['react-app'] = {
                        mount: function() { return Promise.resolve(); },
                        unmount: function() { return Promise.resolve(); }
                    };
                `)
            });

            await microCore.startApp('react-app');

            const app = microCore.getApp('react-app');
            expect(app?.mountTime).toBeGreaterThan(0);

            const adapterMetrics = performanceMonitor.metrics.filter(m =>
                m.name.includes('adapter')
            );
            expect(adapterMetrics.length).toBeGreaterThan(0);

            performanceMonitor.restore();
        });
    });
});
