/**
 * @fileoverview Vue 组件测试工具
 * <AUTHOR> <<EMAIL>>
 */

import { mount, VueWrapper } from '@vue/test-utils'
import { vi } from 'vitest'
import { Component } from 'vue'

/**
 * 创建带路由的 Vue 组件测试挂载器
 */
export function mountWithRouter(component: Component, options: any = {}) {
    const mockRouter = {
        push: vi.fn(),
        replace: vi.fn(),
        go: vi.fn(),
        back: vi.fn(),
        forward: vi.fn(),
        currentRoute: {
            value: {
                path: '/test',
                query: {},
                params: {}
            }
        }
    }

    const mockRoute = {
        path: '/test',
        query: {},
        params: {}
    }

    return mount(component, {
        global: {
            mocks: {
                $router: mockRouter,
                $route: mockRoute
            }
        },
        ...options
    })
}

/**
 * 创建模拟的 Vue 组件属性
 */
export function createMockVueProps(overrides: Record<string, any> = {}) {
    return {
        name: 'test-app',
        theme: 'light',
        basename: '/test',
        ...overrides
    }
}

/**
 * 等待 Vue 组件更新
 */
export async function waitForVueUpdate(wrapper: VueWrapper<any>) {
    await wrapper.vm.$nextTick()
}

/**
 * 模拟 Vue 3 Composition API
 */
export function mockVue3CompositionAPI() {
    const mockRef = vi.fn((value) => ({ value }))
    const mockReactive = vi.fn((obj) => obj)
    const mockComputed = vi.fn((fn) => ({ value: fn() }))
    const mockWatch = vi.fn()
    const mockOnMounted = vi.fn()
    const mockOnUnmounted = vi.fn()

    vi.mock('vue', async () => {
        const actual = await vi.importActual('vue')
        return {
            ...actual,
            ref: mockRef,
            reactive: mockReactive,
            computed: mockComputed,
            watch: mockWatch,
            onMounted: mockOnMounted,
            onUnmounted: mockOnUnmounted
        }
    })

    return {
        mockRef,
        mockReactive,
        mockComputed,
        mockWatch,
        mockOnMounted,
        mockOnUnmounted
    }
}

/**
 * 创建模拟的 Vuex store
 */
export function createMockVuexStore(initialState: Record<string, any> = {}) {
    return {
        state: initialState,
        getters: {},
        mutations: {},
        actions: {},
        commit: vi.fn(),
        dispatch: vi.fn()
    }
}

/**
 * 创建模拟的 Pinia store
 */
export function createMockPiniaStore(initialState: Record<string, any> = {}) {
    return {
        ...initialState,
        $patch: vi.fn(),
        $reset: vi.fn(),
        $subscribe: vi.fn(),
        $onAction: vi.fn()
    }
}