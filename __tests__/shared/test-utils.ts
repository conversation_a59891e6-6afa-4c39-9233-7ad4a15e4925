/**
 * @fileoverview 共享测试工具函数
 * <AUTHOR> <<EMAIL>>
 */

import { vi } from 'vitest'

/**
 * 创建模拟的微前端应用属性
 */
export function createMockMicroAppProps(overrides: Record<string, any> = {}) {
    return {
        container: document.createElement('div'),
        basename: '/test',
        theme: 'light',
        name: 'test-app',
        ...overrides
    }
}

/**
 * 创建模拟的容器元素
 */
export function createMockContainer(id = 'test-container') {
    const container = document.createElement('div')
    container.id = id
    document.body.appendChild(container)
    return container
}

/**
 * 清理测试容器
 */
export function cleanupContainer(container: HTMLElement) {
    if (container.parentNode) {
        container.parentNode.removeChild(container)
    }
}

/**
 * 模拟微前端生命周期状态
 */
export function createMockLifecycleState() {
    return {
        isBootstrapped: false,
        isMounted: false,
        name: 'test-app',
        framework: 'Test',
        version: '1.0.0'
    }
}

/**
 * 等待异步操作完成
 */
export function waitFor(ms: number = 0) {
    return new Promise(resolve => setTimeout(resolve, ms))
}

/**
 * 模拟事件对象
 */
export function createMockEvent(type: string, properties: Record<string, any> = {}) {
    return {
        type,
        preventDefault: vi.fn(),
        stopPropagation: vi.fn(),
        target: document.createElement('div'),
        currentTarget: document.createElement('div'),
        ...properties
    }
}

/**
 * 模拟 postMessage 通信
 */
export function createMockPostMessage() {
    const listeners: Array<(event: any) => void> = []

    const mockPostMessage = vi.fn((data: any, origin = '*') => {
        const event = {
            data,
            origin,
            source: window,
            type: 'message'
        }
        listeners.forEach(listener => listener(event))
    })

    const addEventListener = vi.fn((type: string, listener: (event: any) => void) => {
        if (type === 'message') {
            listeners.push(listener)
        }
    })

    const removeEventListener = vi.fn((type: string, listener: (event: any) => void) => {
        if (type === 'message') {
            const index = listeners.indexOf(listener)
            if (index > -1) {
                listeners.splice(index, 1)
            }
        }
    })

    return {
        postMessage: mockPostMessage,
        addEventListener,
        removeEventListener,
        listeners
    }
}

/**
 * 模拟路由器
 */
export function createMockRouter() {
    return {
        push: vi.fn(),
        replace: vi.fn(),
        go: vi.fn(),
        back: vi.fn(),
        forward: vi.fn(),
        currentRoute: {
            path: '/test',
            query: {},
            params: {}
        }
    }
}

/**
 * 模拟存储
 */
export function createMockStorage() {
    const storage: Record<string, string> = {}

    return {
        getItem: vi.fn((key: string) => storage[key] || null),
        setItem: vi.fn((key: string, value: string) => {
            storage[key] = value
        }),
        removeItem: vi.fn((key: string) => {
            delete storage[key]
        }),
        clear: vi.fn(() => {
            Object.keys(storage).forEach(key => delete storage[key])
        }),
        length: 0,
        key: vi.fn()
    }
}

/**
 * 模拟性能 API
 */
export function createMockPerformance() {
    return {
        now: vi.fn(() => Date.now()),
        mark: vi.fn(),
        measure: vi.fn(),
        getEntriesByName: vi.fn(() => []),
        getEntriesByType: vi.fn(() => []),
        clearMarks: vi.fn(),
        clearMeasures: vi.fn(),
        memory: {
            usedJSHeapSize: 1000000,
            totalJSHeapSize: 2000000,
            jsHeapSizeLimit: 4000000
        }
    }
}

/**
 * 设置通用的测试环境
 */
export function setupTestEnvironment() {
    // 模拟 console 方法以避免测试输出污染
    global.console = {
        ...console,
        log: vi.fn(),
        warn: vi.fn(),
        error: vi.fn(),
        info: vi.fn(),
        debug: vi.fn()
    }

    // 模拟 localStorage 和 sessionStorage
    Object.defineProperty(window, 'localStorage', {
        value: createMockStorage()
    })

    Object.defineProperty(window, 'sessionStorage', {
        value: createMockStorage()
    })

    // 模拟 performance
    if (!global.performance) {
        global.performance = createMockPerformance() as any
    }
}

/**
 * 重置所有模拟
 */
export function resetAllMocks() {
    vi.clearAllMocks()
    vi.resetAllMocks()
}