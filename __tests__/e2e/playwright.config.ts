import { defineConfig, devices } from '@playwright/test';

/**
 * @fileoverview Playwright E2E测试配置
 * <AUTHOR> <<EMAIL>>
 */

export default defineConfig({
    testDir: './tests',
    /* 并行运行测试 */
    fullyParallel: true,
    /* 在CI环境中禁止重试失败的测试 */
    forbidOnly: !!process.env.CI,
    /* 在CI环境中重试失败的测试 */
    retries: process.env.CI ? 2 : 0,
    /* 并行工作进程数量 */
    workers: process.env.CI ? 1 : undefined,
    /* 测试报告配置 */
    reporter: [
        ['html'],
        ['json', { outputFile: 'test-results/results.json' }],
        ['junit', { outputFile: 'test-results/results.xml' }]
    ],
    /* 全局测试配置 */
    use: {
        /* 基础URL */
        baseURL: 'http://localhost:3000',
        /* 截图配置 */
        screenshot: 'only-on-failure',
        /* 视频录制 */
        video: 'retain-on-failure',
        /* 追踪配置 */
        trace: 'on-first-retry',
        /* 浏览器上下文配置 */
        viewport: { width: 1280, height: 720 },
        ignoreHTTPSErrors: true,
        /* 等待策略 */
        actionTimeout: 10000,
        navigationTimeout: 30000
    },

    /* 测试项目配置 */
    projects: [
        {
            name: 'chromium',
            use: { ...devices['Desktop Chrome'] },
        },
        {
            name: 'firefox',
            use: { ...devices['Desktop Firefox'] },
        },
        {
            name: 'webkit',
            use: { ...devices['Desktop Safari'] },
        },
        /* 移动端测试 */
        {
            name: 'Mobile Chrome',
            use: { ...devices['Pixel 5'] },
        },
        {
            name: 'Mobile Safari',
            use: { ...devices['iPhone 12'] },
        },
    ],

    /* 本地开发服务器配置 */
    webServer: [
        {
            command: 'pnpm dev:playground',
            port: 3000,
            reuseExistingServer: !process.env.CI,
            timeout: 120000
        },
        {
            command: 'pnpm dev:examples',
            port: 3001,
            reuseExistingServer: !process.env.CI,
            timeout: 120000
        }
    ],

    /* 输出目录 */
    outputDir: 'test-results/',

    /* 全局设置和清理 */
    globalSetup: require.resolve('./global-setup.ts'),
    globalTeardown: require.resolve('./global-teardown.ts'),
});