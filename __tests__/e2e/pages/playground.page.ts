/**
 * @fileoverview Playground页面对象模型
 * <AUTHOR> <<EMAIL>>
 */

import { Locator, Page, expect } from '@playwright/test';

export class PlaygroundPage {
    readonly page: Page;
    readonly header: Locator;
    readonly navigation: Locator;
    readonly mainContent: Locator;
    readonly sidebar: Locator;
    readonly footer: Locator;

    // 导航菜单
    readonly overviewTab: Locator;
    readonly appsTab: Locator;
    readonly pluginsTab: Locator;
    readonly adaptersTab: Locator;
    readonly buildersTab: Locator;
    readonly sidecarTab: Locator;
    readonly debugTab: Locator;
    readonly settingsTab: Locator;

    // 应用管理相关
    readonly appList: Locator;
    readonly addAppButton: Locator;
    readonly appStatusIndicator: Locator;
    readonly startAppButton: Locator;
    readonly stopAppButton: Locator;
    readonly restartAppButton: Locator;

    // 系统状态
    readonly systemStatus: Locator;
    readonly loadingIndicator: Locator;
    readonly errorMessage: Locator;
    readonly successMessage: Locator;

    constructor(page: Page) {
        this.page = page;

        // 页面布局元素
        this.header = page.locator('[data-testid="header"]');
        this.navigation = page.locator('[data-testid="navigation"]');
        this.mainContent = page.locator('[data-testid="main-content"]');
        this.sidebar = page.locator('[data-testid="sidebar"]');
        this.footer = page.locator('[data-testid="footer"]');

        // 导航菜单
        this.overviewTab = page.locator('[data-testid="nav-overview"]');
        this.appsTab = page.locator('[data-testid="nav-apps"]');
        this.pluginsTab = page.locator('[data-testid="nav-plugins"]');
        this.adaptersTab = page.locator('[data-testid="nav-adapters"]');
        this.buildersTab = page.locator('[data-testid="nav-builders"]');
        this.sidecarTab = page.locator('[data-testid="nav-sidecar"]');
        this.debugTab = page.locator('[data-testid="nav-debug"]');
        this.settingsTab = page.locator('[data-testid="nav-settings"]');

        // 应用管理
        this.appList = page.locator('[data-testid="app-list"]');
        this.addAppButton = page.locator('[data-testid="add-app-button"]');
        this.appStatusIndicator = page.locator('[data-testid="app-status"]');
        this.startAppButton = page.locator('[data-testid="start-app-button"]');
        this.stopAppButton = page.locator('[data-testid="stop-app-button"]');
        this.restartAppButton = page.locator('[data-testid="restart-app-button"]');

        // 系统状态
        this.systemStatus = page.locator('[data-testid="system-status"]');
        this.loadingIndicator = page.locator('[data-testid="loading"]');
        this.errorMessage = page.locator('[data-testid="error-message"]');
        this.successMessage = page.locator('[data-testid="success-message"]');
    }

    async goto() {
        await this.page.goto('/');
        await this.waitForLoad();
    }

    async waitForLoad() {
        // 等待页面加载完成
        await this.page.waitForLoadState('networkidle');

        // 等待主要内容出现
        await this.mainContent.waitFor({ state: 'visible' });

        // 等待加载指示器消失
        await this.loadingIndicator.waitFor({ state: 'hidden', timeout: 10000 });
    }

    async navigateToOverview() {
        await this.overviewTab.click();
        await this.page.waitForURL('**/overview');
        await this.waitForLoad();
    }

    async navigateToApps() {
        await this.appsTab.click();
        await this.page.waitForURL('**/apps');
        await this.waitForLoad();
    }

    async navigateToPlugins() {
        await this.pluginsTab.click();
        await this.page.waitForURL('**/plugins');
        await this.waitForLoad();
    }

    async navigateToAdapters() {
        await this.adaptersTab.click();
        await this.page.waitForURL('**/adapters');
        await this.waitForLoad();
    }

    async navigateToBuilders() {
        await this.buildersTab.click();
        await this.page.waitForURL('**/builders');
        await this.waitForLoad();
    }

    async navigateToSidecar() {
        await this.sidecarTab.click();
        await this.page.waitForURL('**/sidecar');
        await this.waitForLoad();
    }

    async navigateToDebug() {
        await this.debugTab.click();
        await this.page.waitForURL('**/debug');
        await this.waitForLoad();
    }

    async navigateToSettings() {
        await this.settingsTab.click();
        await this.page.waitForURL('**/settings');
        await this.waitForLoad();
    }

    async getAppList() {
        await this.appList.waitFor({ state: 'visible' });
        const apps = await this.appList.locator('[data-testid="app-item"]').all();

        const appData = [];
        for (const app of apps) {
            const name = await app.locator('[data-testid="app-name"]').textContent();
            const status = await app.locator('[data-testid="app-status"]').textContent();
            const framework = await app.locator('[data-testid="app-framework"]').textContent();

            appData.push({ name, status, framework });
        }

        return appData;
    }

    async startApp(appName: string) {
        const appItem = this.appList.locator(`[data-app-name="${appName}"]`);
        await appItem.locator('[data-testid="start-app-button"]').click();

        // 等待应用启动
        await this.waitForAppStatus(appName, 'MOUNTED');
    }

    async stopApp(appName: string) {
        const appItem = this.appList.locator(`[data-app-name="${appName}"]`);
        await appItem.locator('[data-testid="stop-app-button"]').click();

        // 等待应用停止
        await this.waitForAppStatus(appName, 'UNMOUNTED');
    }

    async restartApp(appName: string) {
        const appItem = this.appList.locator(`[data-app-name="${appName}"]`);
        await appItem.locator('[data-testid="restart-app-button"]').click();

        // 等待应用重启
        await this.waitForAppStatus(appName, 'MOUNTED');
    }

    async waitForAppStatus(appName: string, expectedStatus: string, timeout = 10000) {
        const appItem = this.appList.locator(`[data-app-name="${appName}"]`);
        const statusElement = appItem.locator('[data-testid="app-status"]');

        await expect(statusElement).toHaveText(expectedStatus, { timeout });
    }

    async addApp(appConfig: {
        name: string;
        entry: string;
        container: string;
        activeWhen: string;
        framework?: string;
    }) {
        await this.addAppButton.click();

        // 填写应用配置表单
        await this.page.locator('[data-testid="app-name-input"]').fill(appConfig.name);
        await this.page.locator('[data-testid="app-entry-input"]').fill(appConfig.entry);
        await this.page.locator('[data-testid="app-container-input"]').fill(appConfig.container);
        await this.page.locator('[data-testid="app-active-when-input"]').fill(appConfig.activeWhen);

        if (appConfig.framework) {
            await this.page.locator('[data-testid="app-framework-select"]').selectOption(appConfig.framework);
        }

        // 提交表单
        await this.page.locator('[data-testid="add-app-submit"]').click();

        // 等待成功消息
        await this.successMessage.waitFor({ state: 'visible' });
    }

    async removeApp(appName: string) {
        const appItem = this.appList.locator(`[data-app-name="${appName}"]`);
        await appItem.locator('[data-testid="remove-app-button"]').click();

        // 确认删除
        await this.page.locator('[data-testid="confirm-remove"]').click();

        // 等待应用从列表中消失
        await expect(appItem).toHaveCount(0);
    }

    async getSystemStatus() {
        await this.systemStatus.waitFor({ state: 'visible' });

        const coreStatus = await this.systemStatus.locator('[data-testid="core-status"]').textContent();
        const pluginCount = await this.systemStatus.locator('[data-testid="plugin-count"]').textContent();
        const appCount = await this.systemStatus.locator('[data-testid="app-count"]').textContent();
        const adapterCount = await this.systemStatus.locator('[data-testid="adapter-count"]').textContent();

        return {
            coreStatus,
            pluginCount: parseInt(pluginCount || '0'),
            appCount: parseInt(appCount || '0'),
            adapterCount: parseInt(adapterCount || '0')
        };
    }

    async waitForError() {
        await this.errorMessage.waitFor({ state: 'visible' });
        return await this.errorMessage.textContent();
    }

    async waitForSuccess() {
        await this.successMessage.waitFor({ state: 'visible' });
        return await this.successMessage.textContent();
    }

    async takeScreenshot(name: string) {
        await this.page.screenshot({
            path: `test-results/screenshots/${name}.png`,
            fullPage: true
        });
    }

    async getConsoleErrors() {
        const errors: string[] = [];

        this.page.on('console', msg => {
            if (msg.type() === 'error') {
                errors.push(msg.text());
            }
        });

        return errors;
    }

    async getNetworkErrors() {
        const errors: string[] = [];

        this.page.on('response', response => {
            if (response.status() >= 400) {
                errors.push(`${response.status()} ${response.url()}`);
            }
        });

        return errors;
    }

    async enableDebugMode() {
        await this.navigateToSettings();

        const debugToggle = this.page.locator('[data-testid="debug-mode-toggle"]');
        await debugToggle.check();

        // 等待设置保存
        await this.waitForSuccess();
    }

    async getDebugInfo() {
        await this.navigateToDebug();

        const debugInfo = await this.page.locator('[data-testid="debug-info"]').textContent();
        return JSON.parse(debugInfo || '{}');
    }
}