/**
 * @fileoverview 用户工作流程E2E测试
 * <AUTHOR> <<EMAIL>>
 */

import { expect, test } from '@playwright/test';
import { PlaygroundPage } from '../pages/playground.page';

test.describe('用户工作流程E2E测试', () => {
    let playgroundPage: PlaygroundPage;

    test.beforeEach(async ({ page }) => {
        playgroundPage = new PlaygroundPage(page);
        await playgroundPage.goto();
    });

    test.describe('新用户入门流程', () => {
        test('新用户应该能够快速了解系统功能', async () => {
            // 访问概览页面
            await playgroundPage.navigateToOverview();

            // 验证欢迎信息
            const welcomeSection = playgroundPage.page.locator('[data-testid="welcome-section"]');
            await expect(welcomeSection).toBeVisible();

            // 验证功能介绍卡片
            const featureCards = playgroundPage.page.locator('[data-testid="feature-card"]');
            const cardCount = await featureCards.count();
            expect(cardCount).toBeGreaterThan(0);

            // 验证快速开始指南
            const quickStartGuide = playgroundPage.page.locator('[data-testid="quick-start-guide"]');
            await expect(quickStartGuide).toBeVisible();

            await playgroundPage.takeScreenshot('new-user-overview');
        });

        test('新用户应该能够通过向导创建第一个应用', async () => {
            await playgroundPage.navigateToApps();

            // 查找新手向导按钮
            const wizardButton = playgroundPage.page.locator('[data-testid="app-wizard-button"]');
            await wizardButton.click();

            // 步骤1: 选择框架
            const frameworkSelect = playgroundPage.page.locator('[data-testid="framework-select"]');
            await frameworkSelect.selectOption('react');

            const nextButton = playgroundPage.page.locator('[data-testid="wizard-next"]');
            await nextButton.click();

            // 步骤2: 配置应用信息
            await playgroundPage.page.locator('[data-testid="wizard-app-name"]').fill('my-first-app');
            await playgroundPage.page.locator('[data-testid="wizard-app-entry"]').fill('http://localhost:3001/index.js');
            await playgroundPage.page.locator('[data-testid="wizard-app-route"]').fill('/my-app');

            await nextButton.click();

            // 步骤3: 预览和确认
            const previewSection = playgroundPage.page.locator('[data-testid="wizard-preview"]');
            await expect(previewSection).toBeVisible();

            const finishButton = playgroundPage.page.locator('[data-testid="wizard-finish"]');
            await finishButton.click();

            // 验证应用创建成功
            await playgroundPage.waitForSuccess();

            // 验证应用出现在列表中
            const appList = await playgroundPage.getAppList();
            const createdApp = appList.find(app => app.name === 'my-first-app');
            expect(createdApp).toBeTruthy();

            await playgroundPage.takeScreenshot('wizard-completed');
        });
    });

    test.describe('开发者日常工作流程', () => {
        test('开发者应该能够快速部署和测试多个应用', async () => {
            await playgroundPage.navigateToApps();

            // 批量添加多个应用
            const testApps = [
                {
                    name: 'header-app',
                    entry: 'http://localhost:3001/index.js',
                    container: '#header-container',
                    activeWhen: '/header',
                    framework: 'react'
                },
                {
                    name: 'sidebar-app',
                    entry: 'http://localhost:3002/index.js',
                    container: '#sidebar-container',
                    activeWhen: '/sidebar',
                    framework: 'vue'
                },
                {
                    name: 'content-app',
                    entry: 'http://localhost:3001/content.js',
                    container: '#content-container',
                    activeWhen: '/content',
                    framework: 'react'
                }
            ];

            // 添加所有应用
            for (const appConfig of testApps) {
                await playgroundPage.addApp(appConfig);
                await playgroundPage.waitForSuccess();
            }

            // 批量启动所有应用
            for (const appConfig of testApps) {
                await playgroundPage.startApp(appConfig.name);
                await playgroundPage.waitForAppStatus(appConfig.name, 'MOUNTED');
            }

            // 验证所有应用都在运行
            const appList = await playgroundPage.getAppList();
            const runningApps = appList.filter(app => app.status === 'MOUNTED');
            expect(runningApps.length).toBe(testApps.length);

            await playgroundPage.takeScreenshot('multiple-apps-running');
        });

        test('开发者应该能够监控应用性能和状态', async () => {
            await playgroundPage.navigateToDebug();

            // 启用性能监控
            const performanceToggle = playgroundPage.page.locator('[data-testid="performance-monitor-toggle"]');
            await performanceToggle.check();

            // 查看性能指标
            const performanceMetrics = playgroundPage.page.locator('[data-testid="performance-metrics"]');
            await expect(performanceMetrics).toBeVisible();

            // 验证关键性能指标
            const cpuUsage = playgroundPage.page.locator('[data-testid="cpu-usage"]');
            const memoryUsage = playgroundPage.page.locator('[data-testid="memory-usage"]');
            const loadTime = playgroundPage.page.locator('[data-testid="load-time"]');

            await expect(cpuUsage).toBeVisible();
            await expect(memoryUsage).toBeVisible();
            await expect(loadTime).toBeVisible();

            // 查看实时日志
            const logViewer = playgroundPage.page.locator('[data-testid="log-viewer"]');
            await expect(logViewer).toBeVisible();

            await playgroundPage.takeScreenshot('performance-monitoring');
        });

        test('开发者应该能够调试应用间通信', async () => {
            await playgroundPage.navigateToApps();

            // 添加两个测试应用
            const senderApp = {
                name: 'sender-app',
                entry: 'http://localhost:3001/sender.js',
                container: '#sender-container',
                activeWhen: '/sender',
                framework: 'react'
            };

            const receiverApp = {
                name: 'receiver-app',
                entry: 'http://localhost:3002/receiver.js',
                container: '#receiver-container',
                activeWhen: '/receiver',
                framework: 'vue'
            };

            await playgroundPage.addApp(senderApp);
            await playgroundPage.waitForSuccess();
            await playgroundPage.addApp(receiverApp);
            await playgroundPage.waitForSuccess();

            // 启动两个应用
            await playgroundPage.startApp(senderApp.name);
            await playgroundPage.waitForAppStatus(senderApp.name, 'MOUNTED');
            await playgroundPage.startApp(receiverApp.name);
            await playgroundPage.waitForAppStatus(receiverApp.name, 'MOUNTED');

            // 切换到调试页面
            await playgroundPage.navigateToDebug();

            // 打开通信监控
            const communicationMonitor = playgroundPage.page.locator('[data-testid="communication-monitor"]');
            await expect(communicationMonitor).toBeVisible();

            // 模拟发送消息
            const sendMessageButton = playgroundPage.page.locator('[data-testid="send-test-message"]');
            await sendMessageButton.click();

            // 验证消息在监控器中显示
            const messageLog = playgroundPage.page.locator('[data-testid="message-log"]');
            await expect(messageLog).toBeVisible();

            const messageItems = messageLog.locator('[data-testid="message-item"]');
            const messageCount = await messageItems.count();
            expect(messageCount).toBeGreaterThan(0);

            await playgroundPage.takeScreenshot('communication-debugging');
        });
    });

    test.describe('系统管理员工作流程', () => {
        test('管理员应该能够配置系统全局设置', async () => {
            await playgroundPage.navigateToSettings();

            // 配置全局设置
            const globalSettings = playgroundPage.page.locator('[data-testid="global-settings"]');
            await expect(globalSettings).toBeVisible();

            // 修改默认配置
            const defaultFramework = playgroundPage.page.locator('[data-testid="default-framework-select"]');
            await defaultFramework.selectOption('vue');

            const sandboxMode = playgroundPage.page.locator('[data-testid="sandbox-mode-toggle"]');
            await sandboxMode.check();

            const prefetchEnabled = playgroundPage.page.locator('[data-testid="prefetch-enabled-toggle"]');
            await prefetchEnabled.check();

            // 保存设置
            const saveButton = playgroundPage.page.locator('[data-testid="save-settings"]');
            await saveButton.click();

            await playgroundPage.waitForSuccess();

            await playgroundPage.takeScreenshot('global-settings-configured');
        });

        test('管理员应该能够管理插件和适配器', async () => {
            // 管理插件
            await playgroundPage.navigateToPlugins();

            const pluginList = playgroundPage.page.locator('[data-testid="plugin-list"]');
            const routerPlugin = pluginList.locator('[data-plugin-name="router"]');

            // 查看插件配置
            const configButton = routerPlugin.locator('[data-testid="plugin-config"]');
            await configButton.click();

            const configModal = playgroundPage.page.locator('[data-testid="plugin-config-modal"]');
            await expect(configModal).toBeVisible();

            // 修改插件配置
            const routerMode = configModal.locator('[data-testid="router-mode-select"]');
            await routerMode.selectOption('history');

            const saveConfigButton = configModal.locator('[data-testid="save-plugin-config"]');
            await saveConfigButton.click();

            await playgroundPage.waitForSuccess();

            // 关闭配置弹窗
            const closeConfigButton = configModal.locator('[data-testid="close-config-modal"]');
            await closeConfigButton.click();

            await expect(configModal).not.toBeVisible();

            // 管理适配器
            await playgroundPage.navigateToAdapters();

            const adapterList = playgroundPage.page.locator('[data-testid="adapter-list"]');
            const reactAdapter = adapterList.locator('[data-adapter-name="react"]');

            // 查看适配器状态
            const adapterStatus = reactAdapter.locator('[data-testid="adapter-status"]');
            await expect(adapterStatus).toBeVisible();

            await playgroundPage.takeScreenshot('plugin-adapter-management');
        });

        test('管理员应该能够查看系统监控和日志', async () => {
            await playgroundPage.navigateToDebug();

            // 查看系统监控
            const systemMonitor = playgroundPage.page.locator('[data-testid="system-monitor"]');
            await expect(systemMonitor).toBeVisible();

            // 查看资源使用情况
            const resourceUsage = playgroundPage.page.locator('[data-testid="resource-usage"]');
            await expect(resourceUsage).toBeVisible();

            // 查看错误日志
            const errorLogs = playgroundPage.page.locator('[data-testid="error-logs"]');
            await expect(errorLogs).toBeVisible();

            // 导出日志
            const exportLogsButton = playgroundPage.page.locator('[data-testid="export-logs"]');
            await exportLogsButton.click();

            // 验证下载开始
            const downloadPromise = playgroundPage.page.waitForEvent('download');
            const download = await downloadPromise;
            expect(download.suggestedFilename()).toContain('micro-core-logs');

            await playgroundPage.takeScreenshot('system-monitoring');
        });
    });

    test.describe('复杂业务场景', () => {
        test('应该能够处理大型企业级应用场景', async () => {
            await playgroundPage.navigateToApps();

            // 模拟大型企业应用架构
            const enterpriseApps = [
                {
                    name: 'main-shell',
                    entry: 'http://localhost:3001/shell.js',
                    container: '#shell-container',
                    activeWhen: '/',
                    framework: 'react'
                },
                {
                    name: 'user-management',
                    entry: 'http://localhost:3002/user.js',
                    container: '#user-container',
                    activeWhen: '/users',
                    framework: 'vue'
                },
                {
                    name: 'order-system',
                    entry: 'http://localhost:3003/order.js',
                    container: '#order-container',
                    activeWhen: '/orders',
                    framework: 'angular'
                },
                {
                    name: 'reporting-dashboard',
                    entry: 'http://localhost:3004/reports.js',
                    container: '#reports-container',
                    activeWhen: '/reports',
                    framework: 'react'
                },
                {
                    name: 'notification-center',
                    entry: 'http://localhost:3005/notifications.js',
                    container: '#notifications-container',
                    activeWhen: '/notifications',
                    framework: 'vue'
                }
            ];

            // 批量添加企业应用
            for (const appConfig of enterpriseApps) {
                await playgroundPage.addApp(appConfig);
                await playgroundPage.waitForSuccess();
            }

            // 验证所有应用都已添加
            const appList = await playgroundPage.getAppList();
            expect(appList.length).toBeGreaterThanOrEqual(enterpriseApps.length);

            // 模拟用户在不同模块间切换
            const navigationSequence = [
                'main-shell',
                'user-management',
                'order-system',
                'reporting-dashboard',
                'notification-center'
            ];

            for (const appName of navigationSequence) {
                await playgroundPage.startApp(appName);
                await playgroundPage.waitForAppStatus(appName, 'MOUNTED');

                // 模拟用户操作延迟
                await playgroundPage.page.waitForTimeout(1000);

                await playgroundPage.stopApp(appName);
                await playgroundPage.waitForAppStatus(appName, 'UNMOUNTED');
            }

            await playgroundPage.takeScreenshot('enterprise-scenario');
        });

        test('应该能够处理高并发应用切换场景', async () => {
            await playgroundPage.navigateToApps();

            // 创建多个测试应用
            const concurrentApps = Array.from({ length: 5 }, (_, i) => ({
                name: `concurrent-app-${i + 1}`,
                entry: `http://localhost:300${i + 1}/index.js`,
                container: `#concurrent-container-${i + 1}`,
                activeWhen: `/concurrent-${i + 1}`,
                framework: i % 2 === 0 ? 'react' : 'vue'
            }));

            // 添加所有应用
            for (const appConfig of concurrentApps) {
                await playgroundPage.addApp(appConfig);
                await playgroundPage.waitForSuccess();
            }

            // 并发启动所有应用
            const startPromises = concurrentApps.map(app =>
                playgroundPage.startApp(app.name)
            );

            await Promise.all(startPromises);

            // 验证所有应用都已启动
            for (const app of concurrentApps) {
                await playgroundPage.waitForAppStatus(app.name, 'MOUNTED');
            }

            // 快速切换应用状态
            for (let i = 0; i < 3; i++) {
                // 停止所有应用
                const stopPromises = concurrentApps.map(app =>
                    playgroundPage.stopApp(app.name)
                );
                await Promise.all(stopPromises);

                // 等待所有应用停止
                for (const app of concurrentApps) {
                    await playgroundPage.waitForAppStatus(app.name, 'UNMOUNTED');
                }

                // 重新启动所有应用
                const restartPromises = concurrentApps.map(app =>
                    playgroundPage.startApp(app.name)
                );
                await Promise.all(restartPromises);

                // 等待所有应用启动
                for (const app of concurrentApps) {
                    await playgroundPage.waitForAppStatus(app.name, 'MOUNTED');
                }
            }

            await playgroundPage.takeScreenshot('concurrent-scenario');
        });

        test('应该能够处理应用间复杂通信场景', async () => {
            await playgroundPage.navigateToApps();

            // 创建通信测试应用
            const communicationApps = [
                {
                    name: 'message-sender',
                    entry: 'http://localhost:3001/sender.js',
                    container: '#sender-container',
                    activeWhen: '/sender',
                    framework: 'react'
                },
                {
                    name: 'message-receiver',
                    entry: 'http://localhost:3002/receiver.js',
                    container: '#receiver-container',
                    activeWhen: '/receiver',
                    framework: 'vue'
                },
                {
                    name: 'message-broker',
                    entry: 'http://localhost:3003/broker.js',
                    container: '#broker-container',
                    activeWhen: '/broker',
                    framework: 'react'
                }
            ];

            // 添加并启动通信应用
            for (const appConfig of communicationApps) {
                await playgroundPage.addApp(appConfig);
                await playgroundPage.waitForSuccess();
                await playgroundPage.startApp(appConfig.name);
                await playgroundPage.waitForAppStatus(appConfig.name, 'MOUNTED');
            }

            // 切换到调试页面监控通信
            await playgroundPage.navigateToDebug();

            // 启用通信监控
            const communicationMonitor = playgroundPage.page.locator('[data-testid="communication-monitor"]');
            await expect(communicationMonitor).toBeVisible();

            const enableMonitorButton = playgroundPage.page.locator('[data-testid="enable-communication-monitor"]');
            await enableMonitorButton.click();

            // 模拟复杂的消息传递场景
            const messageScenarios = [
                { type: 'point-to-point', from: 'message-sender', to: 'message-receiver' },
                { type: 'broadcast', from: 'message-broker', to: 'all' },
                { type: 'request-response', from: 'message-receiver', to: 'message-sender' },
                { type: 'event-chain', from: 'message-sender', to: 'message-broker' }
            ];

            for (const scenario of messageScenarios) {
                const sendButton = playgroundPage.page.locator(`[data-testid="send-${scenario.type}"]`);
                await sendButton.click();

                // 等待消息处理
                await playgroundPage.page.waitForTimeout(500);

                // 验证消息在监控器中显示
                const messageLog = playgroundPage.page.locator('[data-testid="message-log"]');
                const messageItems = messageLog.locator('[data-testid="message-item"]');
                const messageCount = await messageItems.count();
                expect(messageCount).toBeGreaterThan(0);
            }

            await playgroundPage.takeScreenshot('complex-communication');
        });
    });

    test.describe('移动端适配测试', () => {
        test('应该能够在移动设备上正常使用', async ({ page }) => {
            // 设置移动设备视口
            await page.setViewportSize({ width: 375, height: 667 });

            const mobilePage = new PlaygroundPage(page);
            await mobilePage.goto();

            // 验证移动端布局
            await expect(mobilePage.header).toBeVisible();
            await expect(mobilePage.navigation).toBeVisible();

            // 测试移动端导航
            await mobilePage.navigateToApps();
            await expect(mobilePage.mainContent).toBeVisible();

            // 测试移动端应用管理
            const mobileAppConfig = {
                name: 'mobile-test-app',
                entry: 'http://localhost:3001/mobile.js',
                container: '#mobile-container',
                activeWhen: '/mobile',
                framework: 'react'
            };

            await mobilePage.addApp(mobileAppConfig);
            await mobilePage.waitForSuccess();

            await mobilePage.startApp(mobileAppConfig.name);
            await mobilePage.waitForAppStatus(mobileAppConfig.name, 'MOUNTED');

            await mobilePage.takeScreenshot('mobile-layout');
        });

        test('应该能够响应触摸操作', async ({ page }) => {
            await page.setViewportSize({ width: 375, height: 667 });

            const mobilePage = new PlaygroundPage(page);
            await mobilePage.goto();

            await mobilePage.navigateToApps();

            // 测试触摸滑动
            const appList = mobilePage.page.locator('[data-testid="app-list"]');
            await appList.hover();

            // 模拟触摸滑动操作
            await page.touchscreen.tap(200, 300);
            await page.touchscreen.tap(200, 400);

            // 验证滑动响应
            await expect(appList).toBeVisible();

            await mobilePage.takeScreenshot('touch-interaction');
        });
    });

    test.describe('可访问性测试', () => {
        test('应该符合WCAG可访问性标准', async ({ page }) => {
            const playgroundPage = new PlaygroundPage(page);
            await playgroundPage.goto();

            // 检查页面标题
            await expect(page).toHaveTitle(/Micro-Core Playground/);

            // 检查主要导航的可访问性
            const navigation = playgroundPage.navigation;
            await expect(navigation).toHaveAttribute('role', 'navigation');

            // 检查按钮的可访问性
            const buttons = page.locator('button');
            const buttonCount = await buttons.count();

            for (let i = 0; i < Math.min(buttonCount, 10); i++) {
                const button = buttons.nth(i);
                const isVisible = await button.isVisible();

                if (isVisible) {
                    // 验证按钮有适当的标签或文本
                    const hasText = await button.textContent();
                    const hasAriaLabel = await button.getAttribute('aria-label');
                    const hasTitle = await button.getAttribute('title');

                    expect(hasText || hasAriaLabel || hasTitle).toBeTruthy();
                }
            }

            await playgroundPage.takeScreenshot('accessibility-check');
        });

        test('应该支持键盘导航', async ({ page }) => {
            const playgroundPage = new PlaygroundPage(page);
            await playgroundPage.goto();

            // 使用Tab键导航
            await page.keyboard.press('Tab');
            await page.keyboard.press('Tab');
            await page.keyboard.press('Tab');

            // 验证焦点可见性
            const focusedElement = page.locator(':focus');
            await expect(focusedElement).toBeVisible();

            // 使用Enter键激活元素
            await page.keyboard.press('Enter');

            // 验证键盘操作有效
            await expect(playgroundPage.mainContent).toBeVisible();

            await playgroundPage.takeScreenshot('keyboard-navigation');
        });
    });
});
