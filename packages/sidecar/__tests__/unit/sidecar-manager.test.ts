/**
 * @fileoverview SidecarManager 测试
 * @description 测试 SidecarManager 的核心功能
 * <AUTHOR> <<EMAIL>>
 */

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { SidecarManager } from '../core/sidecar-manager';
import type { SidecarConfig } from '../types';

// Mock 依赖
vi.mock('@micro-core/shared', () => ({
    logger: {
        info: vi.fn(),
        warn: vi.fn(),
        error: vi.fn(),
        debug: vi.fn()
    },
    createMicroCoreError: vi.fn((code, message) => new Error(`${code}: ${message}`))
}));

describe('SidecarManager', () => {
    let sidecarManager: SidecarManager;
    let mockConfig: SidecarConfig;

    beforeEach(() => {
        mockConfig = {
            app: {
                name: 'test-app',
                version: '1.0.0',
                entry: 'http://localhost:3000'
            },
            autoStart: false,
            debug: true
        };

        sidecarManager = new SidecarManager(mockConfig);
    });

    afterEach(() => {
        vi.clearAllMocks();
    });

    describe('构造函数', () => {
        it('应该正确创建 SidecarManager 实例', () => {
            expect(sidecarManager).toBeInstanceOf(SidecarManager);
        });

        it('应该接受配置参数', () => {
            const config = {
                app: {
                    name: 'custom-app',
                    version: '2.0.0',
                    entry: 'http://localhost:4000'
                }
            };
            const manager = new SidecarManager(config);
            expect(manager).toBeInstanceOf(SidecarManager);
        });
    });

    describe('初始化', () => {
        it('应该能够初始化', async () => {
            // 由于依赖的模块可能不存在，我们只测试方法存在
            expect(typeof sidecarManager.init).toBe('function');
        });

        it('应该能够启动', async () => {
            expect(typeof sidecarManager.start).toBe('function');
        });

        it('应该能够停止', async () => {
            expect(typeof sidecarManager.stop).toBe('function');
        });
    });

    describe('应用管理', () => {
        it('应该能够注册应用', () => {
            const appConfig = {
                name: 'test-app',
                entry: 'http://localhost:3000',
                container: '#app',
                activeWhen: '/test'
            };

            expect(typeof sidecarManager.registerApp).toBe('function');

            // 测试方法调用不抛出错误
            expect(() => {
                sidecarManager.registerApp(appConfig);
            }).not.toThrow();
        });

        it('应该能够卸载应用', () => {
            expect(typeof sidecarManager.unregisterApp).toBe('function');

            // 测试方法调用不抛出错误
            expect(() => {
                sidecarManager.unregisterApp('test-app');
            }).not.toThrow();
        });
    });

    describe('状态管理', () => {
        it('应该能够获取状态', () => {
            expect(typeof sidecarManager.getStatus).toBe('function');

            const status = sidecarManager.getStatus();
            expect(status).toBeDefined();
            expect(typeof status).toBe('object');
        });

        it('应该能够重新加载配置', () => {
            expect(typeof sidecarManager.reloadConfig).toBe('function');
        });
    });

    describe('获取器方法', () => {
        it('应该能够获取内核实例', () => {
            expect(typeof sidecarManager.getKernel).toBe('function');
        });

        it('应该能够获取配置管理器', () => {
            expect(typeof sidecarManager.getConfigManager).toBe('function');
        });

        it('应该能够获取自动发现实例', () => {
            expect(typeof sidecarManager.getAutoDiscovery).toBe('function');
        });

        it('应该能够获取框架检测器', () => {
            expect(typeof sidecarManager.getFrameworkDetector).toBe('function');
        });
    });

    describe('错误处理', () => {
        it('应该处理无效配置', () => {
            expect(() => {
                new SidecarManager({} as any);
            }).not.toThrow();
        });

        it('应该处理空配置', () => {
            expect(() => {
                new SidecarManager();
            }).not.toThrow();
        });
    });

    describe('生命周期', () => {
        it('应该支持完整的生命周期', () => {
            // 检查所有生命周期方法存在
            expect(typeof sidecarManager.init).toBe('function');
            expect(typeof sidecarManager.start).toBe('function');
            expect(typeof sidecarManager.stop).toBe('function');
        });
    });
});