/**
 * @fileoverview CompatMode 测试
 * @description 测试兼容模式管理器的功能
 * <AUTHOR> <<EMAIL>>
 */

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { CompatMode } from '../../src/compat-mode';
import type { CompatModeConfig, BrowserInfo } from '../../src/compat-mode';

// Mock 依赖
vi.mock('@micro-core/core', () => ({
    logger: {
        info: vi.fn(),
        warn: vi.fn(),
        error: vi.fn(),
        debug: vi.fn()
    },
    createMicroCoreError: vi.fn((code, message, context) => {
        const error = new Error(message);
        (error as any).code = code;
        (error as any).context = context;
        return error;
    })
}));

// Mock DOM
Object.defineProperty(window, 'navigator', {
    value: {
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    },
    writable: true
});

describe('CompatMode', () => {
    let compatMode: CompatMode;
    let mockConfig: CompatModeConfig;

    beforeEach(() => {
        mockConfig = {
            enabled: true,
            qiankun: false,
            wujie: false,
            singleSpa: false,
            polyfills: ['es6-promise', 'fetch'],
            browser: {
                ie11: false,
                oldChrome: false,
                oldSafari: false
            }
        };

        compatMode = new CompatMode(mockConfig);
    });

    afterEach(() => {
        vi.clearAllMocks();
    });

    describe('构造函数', () => {
        it('应该正确创建 CompatMode 实例', () => {
            expect(compatMode).toBeInstanceOf(CompatMode);
        });

        it('应该使用默认配置', () => {
            const defaultCompatMode = new CompatMode();
            expect(defaultCompatMode).toBeInstanceOf(CompatMode);
        });

        it('应该合并用户配置和默认配置', () => {
            const customConfig: CompatModeConfig = {
                enabled: false,
                qiankun: true
            };
            const customCompatMode = new CompatMode(customConfig);
            expect(customCompatMode).toBeInstanceOf(CompatMode);
        });
    });

    describe('initialize', () => {
        it('应该成功初始化兼容模式', async () => {
            await expect(compatMode.initialize()).resolves.toBeUndefined();
        });

        it('当禁用时应该跳过初始化', async () => {
            const disabledCompatMode = new CompatMode({ enabled: false });
            await expect(disabledCompatMode.initialize()).resolves.toBeUndefined();
        });

        it('应该处理初始化错误', async () => {
            // Mock 一个会抛出错误的方法
            vi.spyOn(compatMode as any, 'checkBrowserCompatibility').mockRejectedValue(new Error('Test error'));
            
            await expect(compatMode.initialize()).rejects.toThrow();
        });
    });

    describe('setup', () => {
        it('应该成功设置兼容模式', async () => {
            const mockKernel = {
                use: vi.fn()
            } as any;

            await expect(compatMode.setup(mockKernel)).resolves.toBeUndefined();
        });

        it('当禁用时应该跳过设置', async () => {
            const disabledCompatMode = new CompatMode({ enabled: false });
            const mockKernel = {
                use: vi.fn()
            } as any;

            await expect(disabledCompatMode.setup(mockKernel)).resolves.toBeUndefined();
        });

        it('应该设置 qiankun 兼容模式', async () => {
            const qiankunCompatMode = new CompatMode({ 
                enabled: true, 
                qiankun: true 
            });
            const mockKernel = {
                use: vi.fn()
            } as any;

            await expect(qiankunCompatMode.setup(mockKernel)).resolves.toBeUndefined();
        });

        it('应该设置 wujie 兼容模式', async () => {
            const wujieCompatMode = new CompatMode({ 
                enabled: true, 
                wujie: true 
            });
            const mockKernel = {
                use: vi.fn()
            } as any;

            await expect(wujieCompatMode.setup(mockKernel)).resolves.toBeUndefined();
        });

        it('应该设置 single-spa 兼容模式', async () => {
            const singleSpaCompatMode = new CompatMode({ 
                enabled: true, 
                singleSpa: true 
            });
            const mockKernel = {
                use: vi.fn()
            } as any;

            await expect(singleSpaCompatMode.setup(mockKernel)).resolves.toBeUndefined();
        });
    });

    describe('浏览器检测', () => {
        it('应该检测 Chrome 浏览器', () => {
            Object.defineProperty(window, 'navigator', {
                value: {
                    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
                },
                writable: true
            });

            const browserInfo = (compatMode as any).detectBrowser();
            expect(browserInfo.name).toBe('chrome');
            expect(browserInfo.version).toBe(91);
        });

        it('应该检测 Firefox 浏览器', () => {
            Object.defineProperty(window, 'navigator', {
                value: {
                    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0'
                },
                writable: true
            });

            const browserInfo = (compatMode as any).detectBrowser();
            expect(browserInfo.name).toBe('firefox');
            expect(browserInfo.version).toBe(89);
        });

        it('应该检测 Safari 浏览器', () => {
            Object.defineProperty(window, 'navigator', {
                value: {
                    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15'
                },
                writable: true
            });

            const browserInfo = (compatMode as any).detectBrowser();
            expect(browserInfo.name).toBe('safari');
            expect(browserInfo.version).toBe(14);
        });

        it('应该检测 IE 浏览器', () => {
            Object.defineProperty(window, 'navigator', {
                value: {
                    userAgent: 'Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko'
                },
                writable: true
            });

            const browserInfo = (compatMode as any).detectBrowser();
            expect(browserInfo.name).toBe('ie');
            expect(browserInfo.version).toBe(11);
        });

        it('应该检测 Edge 浏览器', () => {
            Object.defineProperty(window, 'navigator', {
                value: {
                    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 Edge/91.0.864.59'
                },
                writable: true
            });

            const browserInfo = (compatMode as any).detectBrowser();
            expect(browserInfo.name).toBe('edge');
            expect(browserInfo.version).toBe(91);
        });

        it('应该处理未知浏览器', () => {
            Object.defineProperty(window, 'navigator', {
                value: {
                    userAgent: 'Unknown Browser'
                },
                writable: true
            });

            const browserInfo = (compatMode as any).detectBrowser();
            expect(browserInfo.name).toBe('unknown');
            expect(browserInfo.version).toBe(0);
        });
    });

    describe('Polyfill 管理', () => {
        it('应该加载必要的 polyfills', async () => {
            // Mock document.createElement
            const mockScript = {
                src: '',
                onload: null as any,
                onerror: null as any
            };
            
            vi.spyOn(document, 'createElement').mockReturnValue(mockScript as any);
            vi.spyOn(document.head, 'appendChild').mockImplementation(() => {
                // 模拟脚本加载成功
                setTimeout(() => mockScript.onload?.(), 0);
                return mockScript as any;
            });

            await compatMode.initialize();
            
            expect(document.createElement).toHaveBeenCalledWith('script');
        });

        it('应该处理 polyfill 加载失败', async () => {
            const mockScript = {
                src: '',
                onload: null as any,
                onerror: null as any
            };
            
            vi.spyOn(document, 'createElement').mockReturnValue(mockScript as any);
            vi.spyOn(document.head, 'appendChild').mockImplementation(() => {
                // 模拟脚本加载失败
                setTimeout(() => mockScript.onerror?.(new Error('Load failed')), 0);
                return mockScript as any;
            });

            // 应该不抛出错误，只是记录警告
            await expect(compatMode.initialize()).resolves.toBeUndefined();
        });
    });

    describe('错误处理', () => {
        it('应该正确处理错误', () => {
            const testError = new Error('Test error');
            const testContext = { test: 'context' };

            // 调用私有方法进行测试
            expect(() => {
                (compatMode as any).handleError(testError, testContext);
            }).not.toThrow();
        });

        it('应该调用错误回调', () => {
            const errorCallback = vi.fn();
            const testError = new Error('Test error');
            const testContext = { callback: errorCallback };

            (compatMode as any).handleError(testError, testContext);
            expect(errorCallback).toHaveBeenCalledWith(testError);
        });
    });

    describe('移动设备检测', () => {
        it('应该检测移动设备', () => {
            Object.defineProperty(window, 'navigator', {
                value: {
                    userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15'
                },
                writable: true
            });

            const isMobile = (compatMode as any).isMobile();
            expect(isMobile).toBe(true);
        });

        it('应该检测桌面设备', () => {
            Object.defineProperty(window, 'navigator', {
                value: {
                    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                },
                writable: true
            });

            const isMobile = (compatMode as any).isMobile();
            expect(isMobile).toBe(false);
        });
    });
});
