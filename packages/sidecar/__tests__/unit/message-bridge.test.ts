/**
 * @fileoverview MessageBridge 测试
 * @description 测试消息桥接器的功能
 * <AUTHOR> <<EMAIL>>
 */

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { EventEmitter } from 'events';
import type { BridgeConfig, BridgeMessage } from '../../src/types';

// Mock 依赖
vi.mock('@micro-core/shared', () => ({
    logger: {
        info: vi.fn(),
        warn: vi.fn(),
        error: vi.fn(),
        debug: vi.fn()
    },
    createMicroCoreError: vi.fn((code, message) => {
        const error = new Error(message);
        (error as any).code = code;
        return error;
    })
}));

// 创建一个具体的 MessageBridge 实现用于测试
class TestMessageBridge extends EventEmitter {
    protected config: Required<BridgeConfig>;
    protected stats: any;
    protected isInitialized = false;
    protected isDestroyed = false;
    protected messageQueue: BridgeMessage[] = [];
    protected listeners = new Set<(message: BridgeMessage) => void>();

    constructor(config: BridgeConfig = {}) {
        super();
        
        this.config = {
            protocol: 'postMessage',
            format: 'json',
            timeout: 5000,
            serializer: this.createDefaultSerializer(),
            filter: this.createDefaultFilter(),
            retry: {
                maxAttempts: 3,
                delay: 1000,
                backoff: 'exponential',
                condition: (error: Error) => !error.message.includes('timeout')
            },
            ...config
        };

        this.stats = {
            messagesSent: 0,
            messagesReceived: 0,
            failedMessages: 0,
            averageLatency: 0
        };
    }

    async initialize(): Promise<void> {
        if (this.isInitialized) return;
        if (this.isDestroyed) {
            throw new Error('桥接器已被销毁，无法重新初始化');
        }
        
        await this.doInitialize();
        this.isInitialized = true;
        await this.processMessageQueue();
        this.emit('initialized');
    }

    async destroy(): Promise<void> {
        if (this.isDestroyed) return;
        
        await this.doDestroy();
        this.isDestroyed = true;
        this.isInitialized = false;
        this.messageQueue = [];
        this.listeners.clear();
        this.emit('destroyed');
    }

    async send(message: BridgeMessage): Promise<void> {
        if (!this.isInitialized) {
            this.messageQueue.push(message);
            return;
        }

        if (this.isDestroyed) {
            throw new Error('桥接器已被销毁');
        }

        const filteredMessage = this.config.filter(message);
        if (!filteredMessage) return;

        const serializedMessage = this.config.serializer.serialize(filteredMessage);
        await this.doSend(serializedMessage);
        
        this.stats.messagesSent++;
        this.emit('messageSent', message);
    }

    onMessage(listener: (message: BridgeMessage) => void): void {
        this.listeners.add(listener);
    }

    offMessage(listener: (message: BridgeMessage) => void): void {
        this.listeners.delete(listener);
    }

    getStats() {
        return { ...this.stats };
    }

    // 抽象方法的具体实现
    protected async doInitialize(): Promise<void> {
        // 测试实现
    }

    protected async doDestroy(): Promise<void> {
        // 测试实现
    }

    protected async doSend(data: string): Promise<void> {
        // 测试实现
        await new Promise(resolve => setTimeout(resolve, 10));
    }

    protected async processMessageQueue(): Promise<void> {
        const queue = [...this.messageQueue];
        this.messageQueue = [];
        
        for (const message of queue) {
            try {
                await this.send(message);
            } catch (error) {
                this.stats.failedMessages++;
            }
        }
    }

    protected createDefaultSerializer() {
        return {
            serialize: (message: BridgeMessage) => JSON.stringify(message),
            deserialize: (data: string) => JSON.parse(data) as BridgeMessage
        };
    }

    protected createDefaultFilter() {
        return (message: BridgeMessage) => message;
    }

    // 模拟接收消息
    simulateReceiveMessage(message: BridgeMessage): void {
        this.stats.messagesReceived++;
        this.listeners.forEach(listener => listener(message));
        this.emit('messageReceived', message);
    }
}

describe('MessageBridge', () => {
    let bridge: TestMessageBridge;
    let mockConfig: BridgeConfig;

    beforeEach(() => {
        mockConfig = {
            protocol: 'postMessage',
            format: 'json',
            timeout: 3000
        };

        bridge = new TestMessageBridge(mockConfig);
    });

    afterEach(() => {
        vi.clearAllMocks();
        if (bridge && !bridge.isDestroyed) {
            bridge.destroy();
        }
    });

    describe('构造函数', () => {
        it('应该正确创建 MessageBridge 实例', () => {
            expect(bridge).toBeInstanceOf(TestMessageBridge);
            expect(bridge).toBeInstanceOf(EventEmitter);
        });

        it('应该使用默认配置', () => {
            const defaultBridge = new TestMessageBridge();
            expect(defaultBridge.getStats()).toEqual({
                messagesSent: 0,
                messagesReceived: 0,
                failedMessages: 0,
                averageLatency: 0
            });
        });

        it('应该合并用户配置', () => {
            const customConfig: BridgeConfig = {
                timeout: 10000,
                protocol: 'websocket'
            };
            const customBridge = new TestMessageBridge(customConfig);
            expect(customBridge).toBeInstanceOf(TestMessageBridge);
        });
    });

    describe('initialize', () => {
        it('应该成功初始化桥接器', async () => {
            const initSpy = vi.fn();
            bridge.on('initialized', initSpy);

            await bridge.initialize();
            
            expect(bridge.isInitialized).toBe(true);
            expect(initSpy).toHaveBeenCalled();
        });

        it('应该防止重复初始化', async () => {
            await bridge.initialize();
            await bridge.initialize(); // 第二次调用应该被忽略
            
            expect(bridge.isInitialized).toBe(true);
        });

        it('应该拒绝已销毁桥接器的初始化', async () => {
            await bridge.destroy();
            
            await expect(bridge.initialize()).rejects.toThrow('桥接器已被销毁，无法重新初始化');
        });

        it('应该处理队列中的消息', async () => {
            const testMessage: BridgeMessage = {
                id: 'test-1',
                type: 'test',
                source: 'app1',
                target: 'app2',
                data: { test: 'data' },
                timestamp: Date.now()
            };

            // 在初始化前发送消息（应该被加入队列）
            await bridge.send(testMessage);
            expect(bridge.messageQueue).toHaveLength(1);

            // 初始化后队列应该被处理
            await bridge.initialize();
            expect(bridge.messageQueue).toHaveLength(0);
            expect(bridge.getStats().messagesSent).toBe(1);
        });
    });

    describe('destroy', () => {
        it('应该成功销毁桥接器', async () => {
            const destroySpy = vi.fn();
            bridge.on('destroyed', destroySpy);

            await bridge.initialize();
            await bridge.destroy();
            
            expect(bridge.isDestroyed).toBe(true);
            expect(bridge.isInitialized).toBe(false);
            expect(destroySpy).toHaveBeenCalled();
        });

        it('应该防止重复销毁', async () => {
            await bridge.initialize();
            await bridge.destroy();
            await bridge.destroy(); // 第二次调用应该被忽略
            
            expect(bridge.isDestroyed).toBe(true);
        });

        it('应该清理资源', async () => {
            const listener = vi.fn();
            bridge.onMessage(listener);
            
            await bridge.initialize();
            await bridge.destroy();
            
            expect(bridge.messageQueue).toHaveLength(0);
            expect(bridge.listeners.size).toBe(0);
        });
    });

    describe('send', () => {
        beforeEach(async () => {
            await bridge.initialize();
        });

        it('应该成功发送消息', async () => {
            const testMessage: BridgeMessage = {
                id: 'test-1',
                type: 'test',
                source: 'app1',
                target: 'app2',
                data: { test: 'data' },
                timestamp: Date.now()
            };

            const sendSpy = vi.fn();
            bridge.on('messageSent', sendSpy);

            await bridge.send(testMessage);
            
            expect(bridge.getStats().messagesSent).toBe(1);
            expect(sendSpy).toHaveBeenCalledWith(testMessage);
        });

        it('应该在未初始化时将消息加入队列', async () => {
            const uninitializedBridge = new TestMessageBridge();
            const testMessage: BridgeMessage = {
                id: 'test-1',
                type: 'test',
                source: 'app1',
                target: 'app2',
                data: { test: 'data' },
                timestamp: Date.now()
            };

            await uninitializedBridge.send(testMessage);
            expect(uninitializedBridge.messageQueue).toHaveLength(1);
        });

        it('应该拒绝已销毁桥接器的发送请求', async () => {
            await bridge.destroy();
            
            const testMessage: BridgeMessage = {
                id: 'test-1',
                type: 'test',
                source: 'app1',
                target: 'app2',
                data: { test: 'data' },
                timestamp: Date.now()
            };

            await expect(bridge.send(testMessage)).rejects.toThrow('桥接器已被销毁');
        });
    });

    describe('消息监听', () => {
        beforeEach(async () => {
            await bridge.initialize();
        });

        it('应该正确添加消息监听器', () => {
            const listener = vi.fn();
            bridge.onMessage(listener);
            
            expect(bridge.listeners.has(listener)).toBe(true);
        });

        it('应该正确移除消息监听器', () => {
            const listener = vi.fn();
            bridge.onMessage(listener);
            bridge.offMessage(listener);
            
            expect(bridge.listeners.has(listener)).toBe(false);
        });

        it('应该调用消息监听器', () => {
            const listener = vi.fn();
            bridge.onMessage(listener);

            const testMessage: BridgeMessage = {
                id: 'test-1',
                type: 'test',
                source: 'app1',
                target: 'app2',
                data: { test: 'data' },
                timestamp: Date.now()
            };

            bridge.simulateReceiveMessage(testMessage);
            
            expect(listener).toHaveBeenCalledWith(testMessage);
            expect(bridge.getStats().messagesReceived).toBe(1);
        });
    });

    describe('统计信息', () => {
        beforeEach(async () => {
            await bridge.initialize();
        });

        it('应该正确统计发送的消息', async () => {
            const testMessage: BridgeMessage = {
                id: 'test-1',
                type: 'test',
                source: 'app1',
                target: 'app2',
                data: { test: 'data' },
                timestamp: Date.now()
            };

            await bridge.send(testMessage);
            await bridge.send(testMessage);
            
            expect(bridge.getStats().messagesSent).toBe(2);
        });

        it('应该正确统计接收的消息', () => {
            const testMessage: BridgeMessage = {
                id: 'test-1',
                type: 'test',
                source: 'app1',
                target: 'app2',
                data: { test: 'data' },
                timestamp: Date.now()
            };

            bridge.simulateReceiveMessage(testMessage);
            bridge.simulateReceiveMessage(testMessage);
            
            expect(bridge.getStats().messagesReceived).toBe(2);
        });

        it('应该返回统计信息的副本', () => {
            const stats1 = bridge.getStats();
            const stats2 = bridge.getStats();
            
            expect(stats1).not.toBe(stats2);
            expect(stats1).toEqual(stats2);
        });
    });

    describe('序列化器', () => {
        it('应该正确序列化消息', () => {
            const testMessage: BridgeMessage = {
                id: 'test-1',
                type: 'test',
                source: 'app1',
                target: 'app2',
                data: { test: 'data' },
                timestamp: Date.now()
            };

            const serializer = (bridge as any).createDefaultSerializer();
            const serialized = serializer.serialize(testMessage);
            const deserialized = serializer.deserialize(serialized);
            
            expect(deserialized).toEqual(testMessage);
        });
    });

    describe('过滤器', () => {
        it('应该正确过滤消息', () => {
            const testMessage: BridgeMessage = {
                id: 'test-1',
                type: 'test',
                source: 'app1',
                target: 'app2',
                data: { test: 'data' },
                timestamp: Date.now()
            };

            const filter = (bridge as any).createDefaultFilter();
            const filtered = filter(testMessage);
            
            expect(filtered).toEqual(testMessage);
        });
    });
});
