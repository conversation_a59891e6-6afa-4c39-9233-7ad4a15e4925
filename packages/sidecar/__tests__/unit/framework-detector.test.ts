/**
 * @fileoverview FrameworkDetector 测试
 * @description 测试框架检测功能
 * <AUTHOR> <<EMAIL>>
 */

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { FrameworkDetector } from '../utils/framework-detector';

// Mock window 对象
const mockWindow = {
    React: undefined,
    ReactDOM: undefined,
    Vue: undefined,
    ng: undefined,
    jQuery: undefined,
    $: undefined,
    __REACT_DEVTOOLS_GLOBAL_HOOK__: undefined,
    __VUE_DEVTOOLS_GLOBAL_HOOK__: undefined
};

// Mock document 对象
const mockDocument = {
    querySelectorAll: vi.fn().mockReturnValue([]),
    querySelector: vi.fn().mockReturnValue(null)
};

// 设置全局 mock
Object.defineProperty(global, 'window', {
    value: mockWindow,
    writable: true
});

Object.defineProperty(global, 'document', {
    value: mockDocument,
    writable: true
});

describe('FrameworkDetector', () => {
    let detector: FrameworkDetector;

    beforeEach(() => {
        detector = new FrameworkDetector();
        vi.clearAllMocks();

        // 重置 window 对象
        Object.keys(mockWindow).forEach(key => {
            (mockWindow as any)[key] = undefined;
        });
    });

    afterEach(() => {
        detector.clearCache();
    });

    describe('构造函数', () => {
        it('应该正确创建 FrameworkDetector 实例', () => {
            expect(detector).toBeInstanceOf(FrameworkDetector);
        });
    });

    describe('React 检测', () => {
        it('应该检测到 React', () => {
            (mockWindow as any).React = { version: '18.0.0' };
            (mockWindow as any).ReactDOM = {};

            const result = detector.detectWithDetails();

            expect(result.framework).toBe('react');
            expect(result.version).toBe('18.0.0');
            expect(result.confidence).toBeGreaterThan(0);
            expect(result.evidence).toContain('Global React object found');
        });

        it('应该检测到 React DevTools', () => {
            (mockWindow as any).__REACT_DEVTOOLS_GLOBAL_HOOK__ = {};

            const result = detector.detectWithDetails();

            if (result.framework === 'react') {
                expect(result.evidence).toContain('React DevTools detected');
            }
        });

        it('应该检测到 React DOM 节点', () => {
            const mockElements = [
                { getAttribute: () => 'react-root' },
                { getAttribute: () => 'react-component' }
            ];

            mockDocument.querySelectorAll.mockReturnValue(mockElements);

            const result = detector.detectWithDetails();

            // 由于没有全局 React 对象，可能不会被识别为 React
            // 但应该有相关证据
            expect(result).toBeDefined();
        });
    });

    describe('Vue 检测', () => {
        it('应该检测到 Vue', () => {
            (mockWindow as any).Vue = { version: '3.0.0' };

            const result = detector.detectWithDetails();

            expect(result.framework).toBe('vue');
            expect(result.version).toBe('3.0.0');
            expect(result.confidence).toBeGreaterThan(0);
            expect(result.evidence).toContain('Global Vue object found');
        });

        it('应该检测到 Vue DevTools', () => {
            (mockWindow as any).__VUE_DEVTOOLS_GLOBAL_HOOK__ = {};

            const result = detector.detectWithDetails();

            if (result.framework === 'vue') {
                expect(result.evidence).toContain('Vue DevTools detected');
            }
        });

        it('应该检测到 Vue 指令', () => {
            const mockElements = [
                { tagName: 'DIV' },
                { tagName: 'SPAN' }
            ];

            mockDocument.querySelectorAll
                .mockReturnValueOnce([]) // 第一次调用返回空
                .mockReturnValueOnce(mockElements); // 第二次调用返回元素

            const result = detector.detectWithDetails();

            expect(result).toBeDefined();
        });
    });

    describe('Angular 检测', () => {
        it('应该检测到 Angular', () => {
            (mockWindow as any).ng = { version: '16.0.0' };

            const result = detector.detectWithDetails();

            expect(result.framework).toBe('angular');
            expect(result.confidence).toBeGreaterThan(0);
            expect(result.evidence).toContain('Global ng object found');
        });

        it('应该检测到 Angular 应用根元素', () => {
            mockDocument.querySelector.mockReturnValue({ tagName: 'APP-ROOT' });

            const result = detector.detectWithDetails();

            if (result.framework === 'angular') {
                expect(result.evidence).toContain('Angular app-root found');
            }
        });
    });

    describe('jQuery 检测', () => {
        it('应该检测到 jQuery', () => {
            (mockWindow as any).jQuery = { fn: { jquery: '3.6.0' } };
            (mockWindow as any).$ = (mockWindow as any).jQuery;

            const result = detector.detectWithDetails();

            expect(result.framework).toBe('jquery');
            expect(result.version).toBe('3.6.0');
            expect(result.confidence).toBeGreaterThan(0);
            expect(result.evidence).toContain('Global jQuery object found');
        });
    });

    describe('缓存机制', () => {
        it('应该缓存检测结果', () => {
            (mockWindow as any).React = { version: '18.0.0' };

            const result1 = detector.detectWithDetails();
            const result2 = detector.detectWithDetails();

            expect(result1).toEqual(result2);
        });

        it('应该支持强制刷新缓存', () => {
            (mockWindow as any).React = { version: '18.0.0' };

            const result1 = detector.detectWithDetails();

            // 修改环境
            (mockWindow as any).Vue = { version: '3.0.0' };
            delete (mockWindow as any).React;

            const result2 = detector.detectWithDetails(true); // 强制刷新

            expect(result1.framework).toBe('react');
            expect(result2.framework).toBe('vue');
        });

        it('应该支持清除缓存', () => {
            (mockWindow as any).React = { version: '18.0.0' };

            detector.detectWithDetails();
            detector.clearCache();

            // 修改环境
            (mockWindow as any).Vue = { version: '3.0.0' };
            delete (mockWindow as any).React;

            const result = detector.detectWithDetails();
            expect(result.framework).toBe('vue');
        });
    });

    describe('简化检测方法', () => {
        it('detect() 应该返回框架类型', () => {
            (mockWindow as any).React = { version: '18.0.0' };

            const framework = detector.detect();

            expect(framework).toBe('react');
        });
    });

    describe('支持的框架列表', () => {
        it('应该返回支持的框架列表', () => {
            const frameworks = detector.getSupportedFrameworks();

            expect(frameworks).toContain('react');
            expect(frameworks).toContain('vue');
            expect(frameworks).toContain('angular');
            expect(frameworks).toContain('svelte');
            expect(frameworks).toContain('solid');
            expect(frameworks).toContain('jquery');
            expect(frameworks).toContain('vanilla');
        });
    });

    describe('未知框架处理', () => {
        it('应该返回 vanilla 作为默认框架', () => {
            // 不设置任何框架标识
            const result = detector.detectWithDetails();

            expect(result.framework).toBe('vanilla');
            expect(result.confidence).toBeGreaterThan(0);
            expect(result.evidence).toContain('No major framework detected, assuming vanilla JavaScript');
        });
    });

    describe('错误处理', () => {
        it('应该处理检测过程中的错误', () => {
            // Mock 一个会抛出错误的情况
            mockDocument.querySelectorAll.mockImplementation(() => {
                throw new Error('DOM 访问错误');
            });

            // 应该不抛出错误，而是返回默认结果
            expect(() => {
                detector.detectWithDetails();
            }).not.toThrow();
        });
    });
});