/**
 * @fileoverview 测试环境设置
 * @description 配置测试环境和全局 mock
 * <AUTHOR> <<EMAIL>>
 */

import { vi } from 'vitest';

// Mock console 方法以减少测试输出噪音
const originalConsole = { ...console };

beforeEach(() => {
    // 保持 error 和 warn，但静默 info 和 debug
    console.info = vi.fn();
    console.debug = vi.fn();
    console.log = vi.fn();
});

afterEach(() => {
    // 恢复 console
    Object.assign(console, originalConsole);
    vi.clearAllMocks();
});

// Mock DOM API
Object.defineProperty(window, 'location', {
    value: {
        href: 'http://localhost:3000',
        origin: 'http://localhost:3000',
        protocol: 'http:',
        host: 'localhost:3000',
        hostname: 'localhost',
        port: '3000',
        pathname: '/',
        search: '',
        hash: ''
    },
    writable: true
});

// Mock localStorage
const localStorageMock = {
    getItem: vi.fn(),
    setItem: vi.fn(),
    removeItem: vi.fn(),
    clear: vi.fn(),
    length: 0,
    key: vi.fn()
};

Object.defineProperty(window, 'localStorage', {
    value: localStorageMock,
    writable: true
});

// Mock sessionStorage
Object.defineProperty(window, 'sessionStorage', {
    value: localStorageMock,
    writable: true
});

// Mock fetch
global.fetch = vi.fn();

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn()
}));

// Mock ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn()
}));

// Mock MutationObserver
global.MutationObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    disconnect: vi.fn(),
    takeRecords: vi.fn()
}));

// Mock CustomEvent
global.CustomEvent = vi.fn().mockImplementation((type, options) => ({
    type,
    detail: options?.detail,
    bubbles: options?.bubbles || false,
    cancelable: options?.cancelable || false,
    composed: options?.composed || false
}));

// Mock performance API
Object.defineProperty(window, 'performance', {
    value: {
        now: vi.fn(() => Date.now()),
        mark: vi.fn(),
        measure: vi.fn(),
        getEntriesByType: vi.fn(() => []),
        getEntriesByName: vi.fn(() => [])
    },
    writable: true
});

// Mock requestAnimationFrame
global.requestAnimationFrame = vi.fn((cb) => setTimeout(cb, 16));
global.cancelAnimationFrame = vi.fn((id) => clearTimeout(id));

// Mock URL constructor
global.URL = class URL {
    constructor(public href: string, base?: string) {
        if (base) {
            this.href = new URL(href, base).href;
        }
    }

    get origin() { return 'http://localhost:3000'; }
    get protocol() { return 'http:'; }
    get host() { return 'localhost:3000'; }
    get hostname() { return 'localhost'; }
    get port() { return '3000'; }
    get pathname() { return '/'; }
    get search() { return ''; }
    get hash() { return ''; }

    toString() { return this.href; }
};

// Mock URLSearchParams
global.URLSearchParams = class URLSearchParams {
    private params = new Map<string, string>();

    constructor(init?: string | URLSearchParams | Record<string, string>) {
        if (typeof init === 'string') {
            // 简单解析查询字符串
            init.replace(/^\?/, '').split('&').forEach(pair => {
                const [key, value] = pair.split('=');
                if (key) {
                    this.params.set(decodeURIComponent(key), decodeURIComponent(value || ''));
                }
            });
        }
    }

    get(name: string) { return this.params.get(name); }
    has(name: string) { return this.params.has(name); }
    set(name: string, value: string) { this.params.set(name, value); }
    delete(name: string) { this.params.delete(name); }

    toString() {
        const pairs: string[] = [];
        this.params.forEach((value, key) => {
            pairs.push(`${encodeURIComponent(key)}=${encodeURIComponent(value)}`);
        });
        return pairs.join('&');
    }
};

// 设置测试环境变量
process.env.NODE_ENV = 'test';
process.env.VITEST = 'true';

// 全局错误处理
process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

process.on('uncaughtException', (error) => {
    console.error('Uncaught Exception:', error);
});