/**
 * Solid 适配器工具函数测试
 */

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import {
    SolidMicroAppIntegration,
    createDefaultSolidConfig,
    createSolidAdapter,
    createSolidContainer,
    createSolidErrorInfo,
    extractSolidComponent,
    formatSolidError,
    getSolidVersion,
    isSolidApp,
    isSolidComponent,
    isSolidComponentEnhanced,
    isSolidEntry,
    isSolidVersionCompatible,
    mergeSolidConfigs,
    setupSolidDevTools,
    validateSolidConfig
} from '../src/utils';

// Mock shared utils
vi.mock('@micro-core/shared/utils', () => ({
    mergeConfigs: vi.fn((base, override) => ({ ...base, ...override })),
    createEnhancedContainer: vi.fn((name, framework, parent, options) => {
        const div = document.createElement('div');
        div.id = `micro-app-${name}`;
        div.className = options?.className || '';
        return div;
    }),
    cleanupContainer: vi.fn(),
    formatAdapterError: vi.fn((error, framework, operation, appName) =>
        new Error(`${framework} ${operation} error in ${appName}: ${error.message}`)
    ),
    createAdapterErrorInfo: vi.fn((error, framework, context) => ({
        framework,
        error: error.message,
        context
    })),
    isObject: vi.fn((value) => value !== null && typeof value === 'object'),
    isFunction: vi.fn((value) => typeof value === 'function'),
    isString: vi.fn((value) => typeof value === 'string')
}));

// Mock solid-js/web
vi.mock('solid-js/web', () => ({
    render: vi.fn((component, container) => {
        const disposer = vi.fn();
        // 模拟渲染过程
        if (typeof component === 'function') {
            component();
        }
        return disposer;
    })
}));

describe('Solid 适配器工具函数', () => {
    beforeEach(() => {
        vi.clearAllMocks();
        // 设置 DOM 环境
        Object.defineProperty(global, 'window', {
            value: {
                solid: { VERSION: '1.8.0' }
            },
            writable: true
        });
    });

    afterEach(() => {
        vi.restoreAllMocks();
    });

    describe('createSolidAdapter', () => {
        it('应该创建 Solid 适配器', () => {
            const mockComponent = vi.fn();
            const config = {
                name: 'test-app',
                component: mockComponent
            };

            const adapter = createSolidAdapter(config);

            expect(adapter).toHaveProperty('name', 'test-app');
            expect(adapter).toHaveProperty('config');
            expect(adapter).toHaveProperty('mount');
            expect(adapter).toHaveProperty('unmount');
            expect(typeof adapter.mount).toBe('function');
            expect(typeof adapter.unmount).toBe('function');
        });

        it('应该使用默认配置', () => {
            const mockComponent = vi.fn();
            const config = { name: 'test-app', component: mockComponent };
            const adapter = createSolidAdapter(config);

            expect(adapter.config).toMatchObject({
                name: 'test-app',
                solid: expect.objectContaining({
                    ssr: false,
                    hydratable: false
                })
            });
        });
    });

    describe('isSolidApp', () => {
        it('应该识别 Solid 应用', () => {
            expect(isSolidApp({ render: vi.fn() })).toBe(true);
            expect(isSolidApp({ Component: vi.fn() })).toBe(true);
            expect(isSolidApp({ solidConfig: {} })).toBe(true);
            expect(isSolidApp({ $$typeof: Symbol('solid.element') })).toBe(true);
            expect(isSolidApp({ type: 'div' })).toBe(true);
        });

        it('应该拒绝非 Solid 应用', () => {
            expect(isSolidApp(null)).toBe(false);
            expect(isSolidApp(undefined)).toBe(false);
            expect(isSolidApp('string')).toBe(false);
            expect(isSolidApp({})).toBe(false);
            expect(isSolidApp({ randomProp: 'value' })).toBe(false);
        });
    });

    describe('isSolidEntry', () => {
        it('应该识别 Solid 入口', () => {
            const mockComponent = vi.fn();

            expect(isSolidEntry({ App: mockComponent })).toBe(true);
            expect(isSolidEntry({ Component: mockComponent })).toBe(true);
            expect(isSolidEntry({ solidConfig: {} })).toBe(true);
            expect(isSolidEntry({ default: mockComponent })).toBe(true);
        });

        it('应该拒绝非 Solid 入口', () => {
            expect(isSolidEntry(null)).toBe(false);
            expect(isSolidEntry({})).toBe(false);
            expect(isSolidEntry({ randomExport: {} })).toBe(false);
        });
    });

    describe('getSolidVersion', () => {
        it('应该从全局对象获取版本', () => {
            expect(getSolidVersion()).toBe('1.8.0');
        });

        it('应该处理版本获取失败', () => {
            global.window = undefined as any;
            vi.doMock('solid-js/package.json', () => {
                throw new Error('Module not found');
            });

            expect(getSolidVersion()).toBeNull();
        });
    });

    describe('isSolidVersionCompatible', () => {
        it('应该检查版本兼容性', () => {
            expect(isSolidVersionCompatible('1.8.0', '1.0.0')).toBe(true);
            expect(isSolidVersionCompatible('0.9.0', '1.0.0')).toBe(false);
            expect(isSolidVersionCompatible('1.0.0', '1.0.0')).toBe(true);
            expect(isSolidVersionCompatible('1.1.0', '1.0.0')).toBe(true);
        });

        it('应该处理无效版本', () => {
            expect(isSolidVersionCompatible('', '1.0.0')).toBe(false);
            expect(isSolidVersionCompatible(null as any, '1.0.0')).toBe(false);
        });
    });

    describe('validateSolidConfig', () => {
        it('应该验证有效配置', () => {
            const mockComponent = vi.fn();
            const config = {
                name: 'test-app',
                component: mockComponent,
                solid: {
                    dev: true,
                    ssr: false
                }
            };

            const errors = validateSolidConfig(config);
            expect(errors).toHaveLength(0);
        });

        it('应该检测配置错误', () => {
            const config = {
                name: '',
                solid: {
                    dev: 'invalid' as any,
                    ssr: 'invalid' as any
                }
            };

            const errors = validateSolidConfig(config);
            expect(errors.length).toBeGreaterThan(0);
            expect(errors).toContain('配置中缺少有效的应用名称');
            expect(errors).toContain('配置中缺少 Solid 组件');
        });
    });

    describe('createDefaultSolidConfig', () => {
        it('应该创建默认配置', () => {
            const mockComponent = vi.fn();
            const config = createDefaultSolidConfig({
                name: 'test-app',
                component: mockComponent
            });

            expect(config).toMatchObject({
                name: 'test-app',
                component: mockComponent,
                props: {},
                solid: {
                    ssr: false,
                    hydratable: false
                },
                container: {
                    className: 'solid-app-container'
                }
            });
        });

        it('应该合并用户配置', () => {
            const mockComponent = vi.fn();
            const userConfig = {
                name: 'custom-app',
                component: mockComponent,
                solid: {
                    dev: true
                }
            };

            const config = createDefaultSolidConfig(userConfig);
            expect(config.solid?.dev).toBe(true);
        });
    });

    describe('extractSolidComponent', () => {
        it('应该提取首选组件', () => {
            const preferredComponent = vi.fn();
            const otherComponent = vi.fn();

            const moduleExports = {
                PreferredComponent: preferredComponent,
                OtherComponent: otherComponent
            };

            const component = extractSolidComponent(moduleExports, 'PreferredComponent');
            expect(component).toBe(preferredComponent);
        });

        it('应该提取默认导出', () => {
            const defaultComponent = vi.fn();
            const otherComponent = vi.fn();

            const moduleExports = {
                default: defaultComponent,
                OtherComponent: otherComponent
            };

            const component = extractSolidComponent(moduleExports);
            expect(component).toBe(defaultComponent);
        });

        it('应该提取命名导出', () => {
            const functionComponent = vi.fn();
            const objectComponent = { type: 'div' };

            const moduleExports = {
                FunctionComponent: functionComponent,
                ObjectComponent: objectComponent
            };

            const component = extractSolidComponent(moduleExports);
            expect(component).toBe(functionComponent); // 优先选择函数组件
        });

        it('应该抛出错误当没有找到组件时', () => {
            expect(() => extractSolidComponent({})).toThrow('未找到有效的 Solid 组件');
            expect(() => extractSolidComponent(null)).toThrow('模块导出必须是对象');
        });
    });

    describe('isSolidComponent', () => {
        it('应该识别函数组件', () => {
            const mockComponent = vi.fn();
            expect(isSolidComponent(mockComponent)).toBe(true);
        });

        it('应该识别 JSX 元素', () => {
            const jsxElement = { $$typeof: Symbol('solid.element'), type: 'div' };
            expect(isSolidComponent(jsxElement)).toBe(true);

            const jsxElement2 = { type: 'span' };
            expect(isSolidComponent(jsxElement2)).toBe(true);
        });

        it('应该拒绝非 Solid 组件', () => {
            expect(isSolidComponent(null)).toBe(false);
            expect(isSolidComponent(undefined)).toBe(false);
            expect(isSolidComponent('string')).toBe(false);
            expect(isSolidComponent({})).toBe(false);
        });
    });

    describe('isSolidComponentEnhanced', () => {
        it('应该识别增强的 Solid 组件', () => {
            const enhancedComponent = vi.fn();
            enhancedComponent.microAppConfig = {};
            enhancedComponent.microAppHooks = {};

            expect(isSolidComponentEnhanced(enhancedComponent)).toBe(true);
        });

        it('应该拒绝普通组件', () => {
            const normalComponent = vi.fn();
            expect(isSolidComponentEnhanced(normalComponent)).toBe(false);
        });
    });

    describe('createSolidContainer', () => {
        it('应该创建 Solid 容器', () => {
            const container = createSolidContainer('test-app');

            expect(container).toBeDefined();
            expect(container.id).toBe('micro-app-test-app');
        });
    });

    describe('setupSolidDevTools', () => {
        it('应该设置开发工具', () => {
            const mockDisposer = vi.fn();

            setupSolidDevTools('test-app', mockDisposer);

            expect((global.window as any).solid).toBeDefined();
            expect((global.window as any).solid['test-app']).toBeDefined();
        });

        it('应该处理设置失败', () => {
            const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => { });
            global.window = undefined as any;

            setupSolidDevTools('test-app', vi.fn());

            expect(consoleSpy).not.toHaveBeenCalled(); // 因为 window 检查
            consoleSpy.mockRestore();
        });
    });

    describe('formatSolidError', () => {
        it('应该格式化错误', () => {
            const error = new Error('Test error');
            const formattedError = formatSolidError(error, 'mount', 'test-app');

            expect(formattedError.message).toContain('Solid');
            expect(formattedError.message).toContain('mount');
            expect(formattedError.message).toContain('test-app');
        });
    });

    describe('createSolidErrorInfo', () => {
        it('应该创建错误信息', () => {
            const error = new Error('Test error');
            const context = { operation: 'mount' };
            const errorInfo = createSolidErrorInfo(error, context);

            expect(errorInfo).toMatchObject({
                framework: 'Solid',
                error: 'Test error',
                context
            });
        });
    });

    describe('mergeSolidConfigs', () => {
        it('应该合并配置', () => {
            const base = { name: 'base', solid: { dev: false } };
            const override = { solid: { dev: true } };

            const merged = mergeSolidConfigs(base, override);

            expect(merged).toMatchObject({
                name: 'base',
                solid: { dev: true }
            });
        });
    });

    describe('SolidMicroAppIntegration', () => {
        let integration: SolidMicroAppIntegration;
        let mockElement: HTMLElement;
        let mockComponent: any;

        beforeEach(() => {
            mockComponent = vi.fn();

            const config = {
                name: 'test-app',
                component: mockComponent,
                solid: { dev: true },
                lifecycle: {
                    mounted: vi.fn(),
                    beforeUnmount: vi.fn(),
                    unmounted: vi.fn(),
                    updated: vi.fn()
                }
            };
            integration = new SolidMicroAppIntegration(config);
            mockElement = document.createElement('div') as any;
        });

        describe('mount', () => {
            it('应该挂载应用', async () => {
                await integration.mount(mockElement);

                expect(integration.getContainer()).toBeDefined();
                expect(integration.getDisposer()).toBeDefined();
            });
        });

        describe('unmount', () => {
            it('应该卸载应用', async () => {
                await integration.mount(mockElement);
                const disposer = integration.getDisposer();

                await integration.unmount();

                expect(disposer).toHaveBeenCalled();
                expect(integration.getContainer()).toBeNull();
                expect(integration.getDisposer()).toBeNull();
            });
        });

        describe('updateComponent', () => {
            it('应该更新组件', async () => {
                await integration.mount(mockElement);
                const newComponent = vi.fn();

                await integration.updateComponent(newComponent, { newProp: 'value' });

                expect(integration.getDisposer()).toBeDefined();
            });

            it('应该处理未挂载时的更新', async () => {
                await expect(integration.updateComponent(vi.fn()))
                    .rejects.toThrow('应用未挂载，无法更新组件');
            });
        });
    });
});
