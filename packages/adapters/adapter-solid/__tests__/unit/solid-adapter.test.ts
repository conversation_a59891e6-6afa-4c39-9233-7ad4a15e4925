/**
 * Solid 适配器测试用例
 */

import { beforeEach, describe, expect, it, vi } from 'vitest';
import { SolidAdapter } from '../src';

// 模拟 Solid 依赖
vi.mock('solid-js/web', () => ({
    render: vi.fn().mockReturnValue({
        dispose: vi.fn()
    }),
    insert: vi.fn(),
    createComponent: vi.fn()
}));

import { render } from 'solid-js/web';

describe('SolidAdapter', () => {
    let adapter: SolidAdapter;
    let mockContainer: HTMLElement;
    let mockComponent: any;
    let mockProps: Record<string, any>;
    let mockDispose: any;

    beforeEach(() => {
        // 重置模拟
        vi.resetAllMocks();

        // 创建模拟 DOM 元素
        mockContainer = document.createElement('div');
        document.body.appendChild(mockContainer);

        // 创建模拟组件和属性
        mockComponent = () => () => <div>测试组件 </div>;
        mockProps = { testProp: 'value' };

        // 创建模拟 dispose 函数
        mockDispose = vi.fn();
        (render as any).mockReturnValue({ dispose: mockDispose });

        // 创建适配器实例
        adapter = new SolidAdapter();
    });

    afterEach(() => {
        // 清理 DOM
        document.body.removeChild(mockContainer);
    });

    describe('挂载', () => {
        it('应该能够挂载 Solid 组件', async () => {
            await adapter.mount({
                component: mockComponent,
                container: mockContainer,
                props: mockProps
            });

            expect(render).toHaveBeenCalled();
            expect(render.mock.calls[0][1]).toBe(mockContainer);
        });

        it('应该能够处理挂载选项', async () => {
            const options = {
                hydrate: true,
                delegateEvents: ['click']
            };

            await adapter.mount({
                component: mockComponent,
                container: mockContainer,
                props: mockProps,
                options
            });

            expect(render).toHaveBeenCalled();
            expect(render.mock.calls[0][2]).toEqual(options);
        });

        it('应该在挂载失败时抛出错误', async () => {
            (render as any).mockImplementationOnce(() => {
                throw new Error('挂载失败');
            });

            await expect(adapter.mount({
                component: mockComponent,
                container: mockContainer,
                props: mockProps
            })).rejects.toThrow('挂载失败');
        });
    });

    describe('卸载', () => {
        it('应该能够卸载 Solid 组件', async () => {
            // 先挂载组件
            await adapter.mount({
                component: mockComponent,
                container: mockContainer,
                props: mockProps
            });

            // 然后卸载
            await adapter.unmount({ container: mockContainer });

            expect(mockDispose).toHaveBeenCalled();
        });

        it('应该在卸载失败时抛出错误', async () => {
            // 先挂载组件
            await adapter.mount({
                component: mockComponent,
                container: mockContainer,
                props: mockProps
            });

            // 模拟卸载失败
            mockDispose.mockImplementationOnce(() => {
                throw new Error('卸载失败');
            });

            await expect(adapter.unmount({ container: mockContainer })).rejects.toThrow('卸载失败');
        });

        it('应该在容器不存在时不执行卸载', async () => {
            await adapter.unmount({ container: null as any });
            expect(mockDispose).not.toHaveBeenCalled();
        });
    });

    describe('更新', () => {
        it('应该能够更新 Solid 组件的属性', async () => {
            // 先挂载组件
            await adapter.mount({
                component: mockComponent,
                container: mockContainer,
                props: mockProps
            });

            // 然后更新属性
            const newProps = { testProp: 'newValue' };
            await adapter.update({
                container: mockContainer,
                props: newProps
            });

            // Solid 的更新是通过信号系统自动处理的，不需要额外的操作
            expect(true).toBe(true);
        });

        it('应该在更新失败时抛出错误', async () => {
            // 先挂载组件
            await adapter.mount({
                component: mockComponent,
                container: mockContainer,
                props: mockProps
            });

            // 模拟更新失败
            vi.spyOn(adapter as any, 'getComponentInstance').mockImplementationOnce(() => {
                throw new Error('更新失败');
            });

            // 然后更新属性
            const newProps = { testProp: 'newValue' };
            await expect(adapter.update({
                container: mockContainer,
                props: newProps
            })).rejects.toThrow('更新失败');
        });
    });

    describe('工具函数', () => {
        it('应该能够检查是否是 Solid 组件', () => {
            const solidComponent = () => () => <div>测试组件 </div>;
            const nonSolidComponent = { render: () => { } };

            expect(adapter.isValidComponent(solidComponent)).toBe(true);
            expect(adapter.isValidComponent(nonSolidComponent)).toBe(false);
        });

        it('应该能够获取适配器名称', () => {
            expect(adapter.getName()).toBe('solid');
        });
    });

    describe('组件实例管理', () => {
        it('应该能够存储和检索组件实例', async () => {
            // 先挂载组件
            await adapter.mount({
                component: mockComponent,
                container: mockContainer,
                props: mockProps
            });

            // 检查是否存储了组件实例
            const instance = (adapter as any).getComponentInstance(mockContainer);
            expect(instance).toBeDefined();
        });

        it('应该能够在卸载时移除组件实例', async () => {
            // 先挂载组件
            await adapter.mount({
                component: mockComponent,
                container: mockContainer,
                props: mockProps
            });

            // 然后卸载
            await adapter.unmount({ container: mockContainer });

            // 检查是否移除了组件实例
            const instance = (adapter as any).getComponentInstance(mockContainer);
            expect(instance).toBeUndefined();
        });
    });
});