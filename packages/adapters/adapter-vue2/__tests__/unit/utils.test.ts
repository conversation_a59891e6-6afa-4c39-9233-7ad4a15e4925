/**
 * Vue2 Adapter Utils 单元测试
 */

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import {
    cleanupVue2Container,
    createDefaultVue2Config,
    createVue2Adapter,
    createVue2Container,
    createVue2ErrorInfo,
    createVue2MicroAppMixin,
    enableVue2DevTools,
    extractVue2Component,
    formatVue2Error,
    getVue2Container,
    getVue2Version,
    isVue2App,
    isVue2Component,
    isVue2ComponentEnhanced,
    isVue2DevToolsAvailable,
    isVue2Entry,
    isVue2VersionCompatible,
    mergeVue2Configs,
    validateVue2Config,
    Vue2AdapterUtils
} from '../src/utils';

// Mock Vue
const mockVue = {
    version: '2.7.14',
    config: {
        devtools: false,
        productionTip: true
    },
    prototype: {
        $mount: vi.fn()
    }
};

vi.mock('vue', () => ({
    default: mockVue
}));

// Mock DOM
Object.defineProperty(global, 'document', {
    value: {
        createElement: vi.fn(() => ({
            id: '',
            className: '',
            setAttribute: vi.fn(),
            classList: {
                add: vi.fn()
            },
            appendChild: vi.fn(),
            removeChild: vi.fn(),
            firstChild: null,
            parentNode: null
        })),
        getElementById: vi.fn(),
        body: {
            appendChild: vi.fn()
        }
    }
});

// Mock window
Object.defineProperty(global, 'window', {
    value: {
        Vue: mockVue,
        __VUE_DEVTOOLS_GLOBAL_HOOK__: {},
        location: { href: 'http://localhost:3000' },
        navigator: { userAgent: 'test-agent' }
    }
});

describe('Vue2 Adapter Utils', () => {
    beforeEach(() => {
        vi.clearAllMocks();
    });

    afterEach(() => {
        vi.restoreAllMocks();
    });

    describe('createVue2Adapter', () => {
        it('应该在缺少依赖时抛出错误', () => {
            expect(() => createVue2Adapter()).toThrow('All dependencies are required to create Vue2Adapter');
        });

        it('应该在提供所有依赖时创建适配器', () => {
            const dependencies = {
                lifecycleManager: {},
                sandboxManager: {},
                communicationManager: {},
                errorHandler: {}
            };

            // Mock Vue2Adapter constructor
            const MockVue2Adapter = vi.fn();
            vi.doMock('../src/vue2-adapter', () => ({
                Vue2Adapter: MockVue2Adapter
            }));

            expect(() => createVue2Adapter({}, dependencies)).not.toThrow();
        });
    });

    describe('isVue2App', () => {
        it('应该识别 Vue2 应用配置', () => {
            expect(isVue2App({ vue2: { vueVersion: '2.7' } })).toBe(true);
            expect(isVue2App({ component: { template: '<div></div>' } })).toBe(true);
            expect(isVue2App({ vueOptions: { data: () => ({}) } })).toBe(true);
            expect(isVue2App({ entry: 'vue-app.js' })).toBe(true);
            expect(isVue2App({})).toBe(false);
        });
    });

    describe('isVue2Entry', () => {
        it('应该识别 Vue2 入口文件', () => {
            expect(isVue2Entry('vue-app.js')).toBe(true);
            expect(isVue2Entry('app.vue')).toBe(true);
            expect(isVue2Entry('main-vue2.js')).toBe(true);
            expect(isVue2Entry('index.js')).toBe(false);
        });
    });

    describe('getVue2Version', () => {
        it('应该从 Vue 获取版本', () => {
            expect(getVue2Version()).toBe('2.7.14');
        });

        it('应该在没有 Vue 时返回 null', () => {
            const originalVue = require('vue');
            vi.doMock('vue', () => ({}));

            // 重新导入函数以使用新的 mock
            const { getVue2Version: newGetVue2Version } = require('../src/utils');
            expect(newGetVue2Version()).toBe(null);

            // 恢复原始 mock
            vi.doMock('vue', () => ({ default: originalVue.default }));
        });
    });

    describe('isVue2VersionCompatible', () => {
        it('应该检查版本兼容性', () => {
            expect(isVue2VersionCompatible('2.7.14', '2.6.0')).toBe(true);
            expect(isVue2VersionCompatible('2.5.0', '2.6.0')).toBe(false);
            expect(isVue2VersionCompatible('2.6.0', '2.6.0')).toBe(true);
        });

        it('应该处理无效版本', () => {
            expect(isVue2VersionCompatible('invalid', '2.6.0')).toBe(false);
        });
    });

    describe('validateVue2Config', () => {
        it('应该验证有效配置', () => {
            const config = {
                name: 'test-app',
                component: { template: '<div></div>' }
            };
            expect(() => validateVue2Config(config)).not.toThrow();
        });

        it('应该在缺少名称时抛出错误', () => {
            const config = { component: { template: '<div></div>' } };
            expect(() => validateVue2Config(config as any)).toThrow('Vue 2 app name is required');
        });

        it('应该在缺少组件、选项和入口时抛出错误', () => {
            const config = { name: 'test-app' };
            expect(() => validateVue2Config(config as any)).toThrow('Either component, vueOptions, or entry must be specified');
        });
    });

    describe('createDefaultVue2Config', () => {
        it('应该创建默认配置', () => {
            const config = createDefaultVue2Config();
            expect(config.name).toBe('vue2-app');
            expect(config.vue2?.vueVersion).toBe('2.7');
            expect(config.vue2?.enableDevTools).toBe(false); // NODE_ENV !== 'development'
        });

        it('应该合并覆盖配置', () => {
            const overrides = { name: 'custom-app' };
            const config = createDefaultVue2Config(overrides);
            expect(config.name).toBe('custom-app');
        });
    });

    describe('extractVue2Component', () => {
        it('应该提取默认导出组件', () => {
            const TestComponent = { template: '<div>Test</div>' };
            const module = { default: TestComponent };
            const result = extractVue2Component(module);
            expect(result).toBe(TestComponent);
        });

        it('应该提取命名导出组件', () => {
            const TestComponent = { template: '<div>Test</div>' };
            const module = { TestComponent };
            const result = extractVue2Component(module);
            expect(result).toBe(TestComponent);
        });

        it('应该优先使用首选名称', () => {
            const PreferredComponent = { template: '<div>Preferred</div>' };
            const OtherComponent = { template: '<div>Other</div>' };
            const module = { PreferredComponent, OtherComponent };
            const result = extractVue2Component(module, { preferredName: 'PreferredComponent' });
            expect(result).toBe(PreferredComponent);
        });

        it('应该处理组件选项对象', () => {
            const componentOptions = { template: '<div>Direct</div>', data: () => ({}) };
            const result = extractVue2Component(componentOptions);
            expect(result).toBe(componentOptions);
        });

        it('应该在找不到组件时抛出错误', () => {
            const module = { notAComponent: 'string' };
            expect(() => extractVue2Component(module)).toThrow('No Vue 2 component found in module');
        });

        it('应该处理多个组件', () => {
            const App = { template: '<div>App</div>' };
            const Component = { template: '<div>Component</div>' };
            const module = { App, Component };
            const result = extractVue2Component(module);
            expect(result).toBe(App); // 'App' 在优先级列表中
        });

        it('应该返回所有组件当 allowMultiple 为 true', () => {
            const App = { template: '<div>App</div>' };
            const Component = { template: '<div>Component</div>' };
            const module = { App, Component };
            const result = extractVue2Component(module, { allowMultiple: true });
            expect(result).toEqual({ App, Component });
        });

        it('应该在没有模块时抛出错误', () => {
            expect(() => extractVue2Component(null)).toThrow('Module is required for component extraction');
        });
    });

    describe('isVue2Component', () => {
        it('应该识别组件选项对象', () => {
            expect(isVue2Component({ template: '<div></div>' })).toBe(true);
            expect(isVue2Component({ render: vi.fn() })).toBe(true);
            expect(isVue2Component({ data: () => ({}) })).toBe(true);
            expect(isVue2Component({ computed: {} })).toBe(true);
            expect(isVue2Component({ methods: {} })).toBe(true);
            expect(isVue2Component({ props: [] })).toBe(true);
            expect(isVue2Component({ components: {} })).toBe(true);
        });

        it('应该识别 Vue 构造函数', () => {
            const VueConstructor = function () { };
            VueConstructor.cid = 1;
            expect(isVue2Component(VueConstructor)).toBe(true);
        });

        it('应该识别 Vue 实例', () => {
            const vueInstance = Object.create(mockVue.prototype);
            expect(isVue2Component(vueInstance)).toBe(true);
        });

        it('应该拒绝非组件值', () => {
            expect(isVue2Component(null)).toBe(false);
            expect(isVue2Component('string')).toBe(false);
            expect(isVue2Component(123)).toBe(false);
            expect(isVue2Component({})).toBe(false);
        });
    });

    describe('isVue2ComponentEnhanced', () => {
        it('应该识别更多 Vue2 组件类型', () => {
            expect(isVue2ComponentEnhanced({ watch: {} })).toBe(true);
            expect(isVue2ComponentEnhanced({ mixins: [] })).toBe(true);
            expect(isVue2ComponentEnhanced({ directives: {} })).toBe(true);
            expect(isVue2ComponentEnhanced({ filters: {} })).toBe(true);
            expect(isVue2ComponentEnhanced({ beforeCreate: vi.fn() })).toBe(true);
            expect(isVue2ComponentEnhanced({ created: vi.fn() })).toBe(true);
            expect(isVue2ComponentEnhanced({ mounted: vi.fn() })).toBe(true);
            expect(isVue2ComponentEnhanced({ destroyed: vi.fn() })).toBe(true);
        });

        it('应该识别扩展的 Vue 构造函数', () => {
            const ExtendedVue = function () { };
            ExtendedVue.super = mockVue;
            expect(isVue2ComponentEnhanced(ExtendedVue)).toBe(true);
        });

        it('应该识别有 $mount 方法的原型', () => {
            const ComponentWithMount = function () { };
            ComponentWithMount.prototype = { $mount: vi.fn() };
            expect(isVue2ComponentEnhanced(ComponentWithMount)).toBe(true);
        });

        it('应该识别异步组件', () => {
            const AsyncComponent = function (resolve, reject) { };
            expect(isVue2ComponentEnhanced(AsyncComponent)).toBe(true);
        });
    });

    describe('容器管理', () => {
        beforeEach(() => {
            vi.mocked(document.getElementById).mockReturnValue(null);
            vi.mocked(document.createElement).mockReturnValue({
                id: '',
                className: '',
                setAttribute: vi.fn(),
                classList: { add: vi.fn() },
                appendChild: vi.fn(),
                removeChild: vi.fn(),
                firstChild: null,
                parentNode: null
            } as any);
        });

        describe('createVue2Container', () => {
            it('应该创建 Vue2 容器', () => {
                const container = createVue2Container('test-app');
                expect(container).toBeDefined();
            });
        });

        describe('getVue2Container', () => {
            it('应该获取 Vue2 容器', () => {
                const mockElement = document.createElement('div');
                vi.mocked(document.getElementById).mockReturnValue(mockElement);

                const container = getVue2Container('test-app');
                expect(container).toBe(mockElement);
                expect(document.getElementById).toHaveBeenCalledWith('micro-app-test-app');
            });

            it('应该在容器不存在时返回 null', () => {
                vi.mocked(document.getElementById).mockReturnValue(null);

                const container = getVue2Container('non-existent');
                expect(container).toBe(null);
            });
        });
    });

    describe('开发工具', () => {
        describe('isVue2DevToolsAvailable', () => {
            it('应该检测 Vue2 DevTools 可用性', () => {
                expect(isVue2DevToolsAvailable()).toBe(true);
            });

            it('应该在没有 DevTools 时返回 false', () => {
                const originalHook = (global as any).window.__VUE_DEVTOOLS_GLOBAL_HOOK__;
                delete (global as any).window.__VUE_DEVTOOLS_GLOBAL_HOOK__;
                expect(isVue2DevToolsAvailable()).toBe(false);
                (global as any).window.__VUE_DEVTOOLS_GLOBAL_HOOK__ = originalHook;
            });
        });

        describe('enableVue2DevTools', () => {
            it('应该启用 Vue2 DevTools', () => {
                const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => { });
                enableVue2DevTools('test-app');
                expect(consoleSpy).toHaveBeenCalledWith('Vue 2 DevTools enabled for app: test-app');
                expect(mockVue.config.devtools).toBe(true);
                consoleSpy.mockRestore();
            });
        });
    });

    describe('错误处理', () => {
        describe('createVue2ErrorInfo', () => {
            it('应该创建错误信息对象', () => {
                const error = new Error('Test error');
                const mockVm = {
                    $options: { name: 'TestComponent' },
                    $parent: null
                } as any;
                const errorInfo = createVue2ErrorInfo(error, mockVm, 'test info');

                expect(errorInfo).toBeDefined();
            });
        });

        describe('formatVue2Error', () => {
            it('应该格式化 Vue2 错误', () => {
                const error = new Error('Test error');
                const mockVm = {
                    $options: { name: 'TestComponent' },
                    $parent: null
                } as any;
                const formatted = formatVue2Error(error, mockVm, 'test info');

                expect(formatted).toContain('Test error');
            });
        });
    });

    describe('配置合并', () => {
        describe('mergeVue2Configs', () => {
            it('应该合并 Vue2 配置', () => {
                const base = {
                    name: 'base-app',
                    vue2: { vueVersion: '2.6' }
                };
                const override = {
                    name: 'override-app',
                    vue2: { enableDevTools: true }
                };

                const merged = mergeVue2Configs(base, override);
                expect(merged.name).toBe('override-app');
                expect(merged.vue2?.vueVersion).toBe('2.6');
                expect(merged.vue2?.enableDevTools).toBe(true);
            });
        });
    });

    describe('微应用集成', () => {
        describe('createVue2MicroAppMixin', () => {
            it('应该创建微应用 mixin', () => {
                const context = {
                    communication: {
                        emit: vi.fn(),
                        sendToApp: vi.fn(),
                        getGlobalState: vi.fn(),
                        setGlobalState: vi.fn()
                    }
                };

                const mixin = createVue2MicroAppMixin(context);
                expect(mixin.data).toBeDefined();
                expect(mixin.methods).toBeDefined();
                expect(mixin.methods.$emitToParent).toBeDefined();
                expect(mixin.methods.$sendToApp).toBeDefined();
                expect(mixin.methods.$getGlobalState).toBeDefined();
                expect(mixin.methods.$setGlobalState).toBeDefined();
            });
        });
    });

    describe('Vue2AdapterUtils', () => {
        it('应该导出所有工具函数', () => {
            expect(Vue2AdapterUtils.extractVue2Component).toBe(extractVue2Component);
            expect(Vue2AdapterUtils.isVue2Component).toBe(isVue2Component);
            expect(Vue2AdapterUtils.isVue2ComponentEnhanced).toBe(isVue2ComponentEnhanced);
            expect(Vue2AdapterUtils.isVue2App).toBe(isVue2App);
            expect(Vue2AdapterUtils.isVue2Entry).toBe(isVue2Entry);
            expect(Vue2AdapterUtils.getVue2Version).toBe(getVue2Version);
            expect(Vue2AdapterUtils.isVue2VersionCompatible).toBe(isVue2VersionCompatible);
            expect(Vue2AdapterUtils.validateVue2Config).toBe(validateVue2Config);
            expect(Vue2AdapterUtils.createDefaultVue2Config).toBe(createDefaultVue2Config);
            expect(Vue2AdapterUtils.mergeVue2Configs).toBe(mergeVue2Configs);
            expect(Vue2AdapterUtils.createVue2Container).toBe(createVue2Container);
            expect(Vue2AdapterUtils.cleanupVue2Container).toBe(cleanupVue2Container);
            expect(Vue2AdapterUtils.getVue2Container).toBe(getVue2Container);
            expect(Vue2AdapterUtils.formatVue2Error).toBe(formatVue2Error);
            expect(Vue2AdapterUtils.createVue2ErrorInfo).toBe(createVue2ErrorInfo);
            expect(Vue2AdapterUtils.isVue2DevToolsAvailable).toBe(isVue2DevToolsAvailable);
            expect(Vue2AdapterUtils.enableVue2DevTools).toBe(enableVue2DevTools);
            expect(Vue2AdapterUtils.createVue2MicroAppMixin).toBe(createVue2MicroAppMixin);
            expect(Vue2AdapterUtils.createVue2Adapter).toBe(createVue2Adapter);
        });
    });
});
