/**
 * Vue2 适配器测试用例
 */

import { beforeEach, describe, expect, it, vi } from 'vitest';
import { Vue2Adapter } from '../src';

// 模拟 Vue 依赖
vi.mock('vue', () => {
    const Vue = function (options: any) {
        return {
            $mount: vi.fn(),
            $destroy: vi.fn(),
            $props: {},
            $options: options
        };
    };
    Vue.extend = vi.fn().mockReturnValue(Vue);
    Vue.component = vi.fn();
    Vue.use = vi.fn();
    return Vue;
});

import Vue from 'vue';

describe('Vue2Adapter', () => {
    let adapter: Vue2Adapter;
    let mockContainer: HTMLElement;
    let mockComponent: any;
    let mockProps: Record<string, any>;
    let mockVueInstance: any;

    beforeEach(() => {
        // 重置模拟
        vi.resetAllMocks();

        // 创建模拟 DOM 元素
        mockContainer = document.createElement('div');
        document.body.appendChild(mockContainer);

        // 创建模拟组件和属性
        mockComponent = { name: 'TestComponent', template: '<div>测试组件</div>' };
        mockProps = { testProp: 'value' };

        // 创建模拟 Vue 实例
        mockVueInstance = {
            $mount: vi.fn(),
            $destroy: vi.fn(),
            $props: {},
            $options: {}
        };

        (Vue as any).mockReturnValue(mockVueInstance);

        // 创建适配器实例
        adapter = new Vue2Adapter();
    });

    afterEach(() => {
        // 清理 DOM
        document.body.removeChild(mockContainer);
    });

    describe('挂载', () => {
        it('应该能够挂载 Vue2 组件', async () => {
            await adapter.mount({
                component: mockComponent,
                container: mockContainer,
                props: mockProps
            });

            expect(Vue.extend).toHaveBeenCalledWith(mockComponent);
            expect(mockVueInstance.$mount).toHaveBeenCalled();
        });

        it('应该能够处理挂载选项', async () => {
            const plugins = [{ install: vi.fn() }];
            const components = [{ name: 'TestComponent' }];

            await adapter.mount({
                component: mockComponent,
                container: mockContainer,
                props: mockProps,
                plugins,
                components
            });

            expect(Vue.use).toHaveBeenCalledTimes(plugins.length);
            expect(Vue.component).toHaveBeenCalledTimes(components.length);
        });

        it('应该在挂载失败时抛出错误', async () => {
            mockVueInstance.$mount.mockImplementationOnce(() => {
                throw new Error('挂载失败');
            });

            await expect(adapter.mount({
                component: mockComponent,
                container: mockContainer,
                props: mockProps
            })).rejects.toThrow('挂载失败');
        });
    });

    describe('卸载', () => {
        it('应该能够卸载 Vue2 组件', async () => {
            // 先挂载组件
            await adapter.mount({
                component: mockComponent,
                container: mockContainer,
                props: mockProps
            });

            // 然后卸载
            await adapter.unmount({ container: mockContainer });

            expect(mockVueInstance.$destroy).toHaveBeenCalled();
        });

        it('应该在卸载失败时抛出错误', async () => {
            // 先挂载组件
            await adapter.mount({
                component: mockComponent,
                container: mockContainer,
                props: mockProps
            });

            // 模拟卸载失败
            mockVueInstance.$destroy.mockImplementationOnce(() => {
                throw new Error('卸载失败');
            });

            await expect(adapter.unmount({ container: mockContainer })).rejects.toThrow('卸载失败');
        });

        it('应该在容器不存在时不执行卸载', async () => {
            await adapter.unmount({ container: null as any });
            expect(mockVueInstance.$destroy).not.toHaveBeenCalled();
        });
    });

    describe('更新', () => {
        it('应该能够更新 Vue2 组件的属性', async () => {
            // 先挂载组件
            await adapter.mount({
                component: mockComponent,
                container: mockContainer,
                props: mockProps
            });

            // 模拟 Vue 实例
            const mockInstance = {
                $props: { ...mockProps }
            };
            vi.spyOn(adapter as any, 'getComponentInstance').mockReturnValue(mockInstance);

            // 然后更新属性
            const newProps = { testProp: 'newValue' };
            await adapter.update({
                container: mockContainer,
                props: newProps
            });

            // Vue2 的响应式系统会自动更新属性
            expect(mockInstance.$props.testProp).toBe('newValue');
        });

        it('应该在更新失败时抛出错误', async () => {
            // 先挂载组件
            await adapter.mount({
                component: mockComponent,
                container: mockContainer,
                props: mockProps
            });

            // 模拟更新失败
            vi.spyOn(adapter as any, 'getComponentInstance').mockImplementationOnce(() => {
                throw new Error('更新失败');
            });

            // 然后更新属性
            const newProps = { testProp: 'newValue' };
            await expect(adapter.update({
                container: mockContainer,
                props: newProps
            })).rejects.toThrow('更新失败');
        });
    });

    describe('工具函数', () => {
        it('应该能够检查是否是 Vue2 组件', () => {
            const vue2Component = { template: '<div>测试</div>' };
            const nonVue2Component = () => { };

            expect(adapter.isValidComponent(vue2Component)).toBe(true);
            expect(adapter.isValidComponent(nonVue2Component)).toBe(false);
        });

        it('应该能够获取适配器名称', () => {
            expect(adapter.getName()).toBe('vue2');
        });
    });

    describe('组件实例管理', () => {
        it('应该能够存储和检索组件实例', async () => {
            // 先挂载组件
            await adapter.mount({
                component: mockComponent,
                container: mockContainer,
                props: mockProps
            });

            // 检查是否存储了组件实例
            const instance = (adapter as any).getComponentInstance(mockContainer);
            expect(instance).toBeDefined();
        });

        it('应该能够在卸载时移除组件实例', async () => {
            // 先挂载组件
            await adapter.mount({
                component: mockComponent,
                container: mockContainer,
                props: mockProps
            });

            // 然后卸载
            await adapter.unmount({ container: mockContainer });

            // 检查是否移除了组件实例
            const instance = (adapter as any).getComponentInstance(mockContainer);
            expect(instance).toBeUndefined();
        });
    });
});