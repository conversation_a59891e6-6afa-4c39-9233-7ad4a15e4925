/**
 * Vue3 适配器测试用例
 */

import { beforeEach, describe, expect, it, vi } from 'vitest';
import { createApp, h } from 'vue';
import { Vue3Adapter } from '../src';

// 模拟 Vue 依赖
vi.mock('vue', () => ({
    createApp: vi.fn().mockReturnValue({
        mount: vi.fn(),
        unmount: vi.fn(),
        component: vi.fn().mockReturnThis(),
        use: vi.fn().mockReturnThis(),
        provide: vi.fn().mockReturnThis()
    }),
    h: vi.fn()
}));

describe('Vue3Adapter', () => {
    let adapter: Vue3Adapter;
    let mockContainer: HTMLElement;
    let mockComponent: any;
    let mockProps: Record<string, any>;
    let mockApp: any;

    beforeEach(() => {
        // 重置模拟
        vi.resetAllMocks();

        // 创建模拟 DOM 元素
        mockContainer = document.createElement('div');
        document.body.appendChild(mockContainer);

        // 创建模拟组件和属性
        mockComponent = { name: 'TestComponent' };
        mockProps = { testProp: 'value' };

        // 创建模拟 Vue 应用
        mockApp = {
            mount: vi.fn(),
            unmount: vi.fn(),
            component: vi.fn().mockReturnThis(),
            use: vi.fn().mockReturnThis(),
            provide: vi.fn().mockReturnThis()
        };

        (createApp as any).mockReturnValue(mockApp);

        // 创建适配器实例
        adapter = new Vue3Adapter();
    });

    afterEach(() => {
        // 清理 DOM
        document.body.removeChild(mockContainer);
    });

    describe('挂载', () => {
        it('应该能够挂载 Vue3 组件', async () => {
            await adapter.mount({
                component: mockComponent,
                container: mockContainer,
                props: mockProps
            });

            expect(createApp).toHaveBeenCalled();
            expect(mockApp.mount).toHaveBeenCalledWith(mockContainer);
        });

        it('应该能够处理挂载选项', async () => {
            const plugins = [{ install: vi.fn() }];
            const components = [{ name: 'TestComponent' }];
            const appContext = { provides: { key: 'value' } };

            await adapter.mount({
                component: mockComponent,
                container: mockContainer,
                props: mockProps,
                appContext,
                plugins,
                components
            });

            expect(mockApp.use).toHaveBeenCalledTimes(plugins.length);
            expect(mockApp.component).toHaveBeenCalledTimes(components.length);
            expect(mockApp.provide).toHaveBeenCalled();
        });

        it('应该在挂载失败时抛出错误', async () => {
            const error = new Error('挂载失败');
            mockApp.mount.mockRejectedValue(error);

            await expect(adapter.mount({
                component: mockComponent,
                container: mockContainer,
                props: mockProps
            })).rejects.toThrow('挂载失败');
        });
    });

    describe('卸载', () => {
        it('应该能够卸载 Vue3 组件', async () => {
            // 先挂载组件
            await adapter.mount({
                component: mockComponent,
                container: mockContainer,
                props: mockProps
            });

            // 然后卸载
            await adapter.unmount({ container: mockContainer });

            expect(mockApp.unmount).toHaveBeenCalled();
        });

        it('应该在卸载失败时抛出错误', async () => {
            // 先挂载组件
            await adapter.mount({
                component: mockComponent,
                container: mockContainer,
                props: mockProps
            });

            // 模拟卸载失败
            const error = new Error('卸载失败');
            mockApp.unmount.mockRejectedValue(error);

            await expect(adapter.unmount({ container: mockContainer })).rejects.toThrow('卸载失败');
        });

        it('应该在容器不存在时不执行卸载', async () => {
            await adapter.unmount({ container: null as any });
            expect(mockApp.unmount).not.toHaveBeenCalled();
        });
    });

    describe('更新', () => {
        it('应该能够更新 Vue3 组件的属性', async () => {
            // 先挂载组件
            await adapter.mount({
                component: mockComponent,
                container: mockContainer,
                props: mockProps
            });

            // 然后更新属性
            const newProps = { testProp: 'newValue' };
            await adapter.update({
                container: mockContainer,
                props: newProps
            });

            // Vue3 的响应式系统会自动更新属性，不需要额外的操作
            expect(true).toBe(true);
        });
    });

    describe('工具函数', () => {
        it('应该能够检查是否是 Vue3 组件', () => {
            const vueComponent = { render: () => h('div') };
            const nonVueComponent = { template: '<div></div>' };

            expect(adapter.isValidComponent(vueComponent)).toBe(true);
            expect(adapter.isValidComponent(nonVueComponent)).toBe(false);
        });

        it('应该能够获取适配器名称', () => {
            expect(adapter.getName()).toBe('vue3');
        });
    });
});