/**
 * React Adapter Utils 单元测试
 */

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import {
    cleanupReactContainer,
    createDefaultReactConfig,
    createReactAdapter,
    createReactContainer,
    createReactErrorInfo,
    enableReactDevTools,
    extractReactComponent,
    formatReactError,
    getReactContainer,
    getReactVersion,
    isReactApp,
    isReactComponent,
    isReactComponentEnhanced,
    isReactDevToolsAvailable,
    isReactEntry,
    isReactVersionCompatible,
    mergeReactConfigs,
    ReactAdapterUtils,
    validateReactConfig
} from '../../src/utils';

// Mock DOM
Object.defineProperty(global, 'document', {
    value: {
        createElement: vi.fn(() => ({
            id: '',
            className: '',
            setAttribute: vi.fn(),
            classList: {
                add: vi.fn()
            },
            appendChild: vi.fn(),
            removeChild: vi.fn(),
            firstChild: null,
            parentNode: null
        })),
        getElementById: vi.fn(),
        body: {
            appendChild: vi.fn()
        }
    }
});

// Mock window
Object.defineProperty(global, 'window', {
    value: {
        React: { version: '18.2.0' },
        __REACT_DEVTOOLS_GLOBAL_HOOK__: {},
        location: { href: 'http://localhost:3000' },
        navigator: { userAgent: 'test-agent' }
    }
});

describe('React Adapter Utils', () => {
    beforeEach(() => {
        vi.clearAllMocks();
    });

    afterEach(() => {
        vi.restoreAllMocks();
    });

    describe('createReactAdapter', () => {
        it('应该创建 React 适配器实例', () => {
            const adapter = createReactAdapter();
            expect(adapter).toBeDefined();
            expect(adapter.constructor.name).toBe('ReactAdapter');
        });

        it('应该接受配置参数', () => {
            const config = { reactVersion: '18', enableDevTools: true };
            const adapter = createReactAdapter(config);
            expect(adapter).toBeDefined();
        });
    });

    describe('isReactApp', () => {
        it('应该识别 React 应用配置', () => {
            expect(isReactApp({ react: { reactVersion: '18' } })).toBe(true);
            expect(isReactApp({ component: () => null })).toBe(true);
            expect(isReactApp({ entry: 'react-app.js' })).toBe(true);
            expect(isReactApp({})).toBe(false);
        });
    });

    describe('isReactEntry', () => {
        it('应该识别 React 入口文件', () => {
            expect(isReactEntry('react-app.js')).toBe(true);
            expect(isReactEntry('app.jsx')).toBe(true);
            expect(isReactEntry('main.tsx')).toBe(true);
            expect(isReactEntry('index.js')).toBe(false);
        });
    });

    describe('getReactVersion', () => {
        it('应该从 window.React 获取版本', () => {
            expect(getReactVersion()).toBe('18.2.0');
        });

        it('应该在没有 React 时返回 null', () => {
            const originalReact = (global as any).window.React;
            delete (global as any).window.React;
            expect(getReactVersion()).toBe(null);
            (global as any).window.React = originalReact;
        });
    });

    describe('isReactVersionCompatible', () => {
        it('应该检查版本兼容性', () => {
            expect(isReactVersionCompatible('18.2.0', '16.8.0')).toBe(true);
            expect(isReactVersionCompatible('16.7.0', '16.8.0')).toBe(false);
            expect(isReactVersionCompatible('17.0.0', '16.8.0')).toBe(true);
        });

        it('应该处理无效版本', () => {
            expect(isReactVersionCompatible('invalid', '16.8.0')).toBe(false);
        });
    });

    describe('validateReactConfig', () => {
        it('应该验证有效配置', () => {
            const config = {
                name: 'test-app',
                component: () => null
            };
            expect(() => validateReactConfig(config)).not.toThrow();
        });

        it('应该在缺少名称时抛出错误', () => {
            const config = { component: () => null };
            expect(() => validateReactConfig(config as any)).toThrow('React app name is required');
        });

        it('应该在缺少组件和入口时抛出错误', () => {
            const config = { name: 'test-app' };
            expect(() => validateReactConfig(config as any)).toThrow('Either component or entry must be specified');
        });
    });

    describe('createDefaultReactConfig', () => {
        it('应该创建默认配置', () => {
            const config = createDefaultReactConfig();
            expect(config.name).toBe('react-app');
            expect(config.react?.reactVersion).toBe('18');
            expect(config.react?.enableDevTools).toBe(false); // NODE_ENV !== 'development'
        });

        it('应该合并覆盖配置', () => {
            const overrides = { name: 'custom-app' };
            const config = createDefaultReactConfig(overrides);
            expect(config.name).toBe('custom-app');
        });
    });

    describe('extractReactComponent', () => {
        it('应该提取默认导出组件', () => {
            const TestComponent = () => null;
            const module = { default: TestComponent };
            const result = extractReactComponent(module);
            expect(result).toBe(TestComponent);
        });

        it('应该提取命名导出组件', () => {
            const TestComponent = () => null;
            const module = { TestComponent };
            const result = extractReactComponent(module);
            expect(result).toBe(TestComponent);
        });

        it('应该优先使用首选名称', () => {
            const PreferredComponent = () => null;
            const OtherComponent = () => null;
            const module = { PreferredComponent, OtherComponent };
            const result = extractReactComponent(module, { preferredName: 'PreferredComponent' });
            expect(result).toBe(PreferredComponent);
        });

        it('应该在找不到组件时抛出错误', () => {
            const module = { notAComponent: 'string' };
            expect(() => extractReactComponent(module)).toThrow('No React component found in module');
        });

        it('应该处理多个组件', () => {
            const App = () => null;
            const Component = () => null;
            const module = { App, Component };
            const result = extractReactComponent(module);
            expect(result).toBe(App); // 'App' 在优先级列表中
        });

        it('应该返回所有组件当 allowMultiple 为 true', () => {
            const App = () => null;
            const Component = () => null;
            const module = { App, Component };
            const result = extractReactComponent(module, { allowMultiple: true });
            expect(result).toEqual({ App, Component });
        });

        it('应该在没有模块时抛出错误', () => {
            expect(() => extractReactComponent(null)).toThrow('Module is required for component extraction');
        });
    });

    describe('isReactComponent', () => {
        it('应该识别函数组件', () => {
            const FunctionComponent = () => null;
            expect(isReactComponent(FunctionComponent)).toBe(true);
        });

        it('应该识别类组件', () => {
            class ClassComponent {
                static prototype = { isReactComponent: true };
            }
            expect(isReactComponent(ClassComponent)).toBe(true);
        });

        it('应该识别 Forward Ref 组件', () => {
            const ForwardRefComponent = { $$typeof: Symbol.for('react.forward_ref') };
            expect(isReactComponent(ForwardRefComponent)).toBe(true);
        });

        it('应该识别 Memo 组件', () => {
            const MemoComponent = { $$typeof: Symbol.for('react.memo') };
            expect(isReactComponent(MemoComponent)).toBe(true);
        });

        it('应该拒绝非组件值', () => {
            expect(isReactComponent(null)).toBe(false);
            expect(isReactComponent('string')).toBe(false);
            expect(isReactComponent(123)).toBe(false);
            expect(isReactComponent({})).toBe(false);
        });
    });

    describe('isReactComponentEnhanced', () => {
        it('应该识别更多 React 组件类型', () => {
            const LazyComponent = { $$typeof: Symbol.for('react.lazy') };
            expect(isReactComponentEnhanced(LazyComponent)).toBe(true);

            const SuspenseComponent = { $$typeof: Symbol.for('react.suspense') };
            expect(isReactComponentEnhanced(SuspenseComponent)).toBe(true);

            const FragmentComponent = { $$typeof: Symbol.for('react.fragment') };
            expect(isReactComponentEnhanced(FragmentComponent)).toBe(true);
        });

        it('应该通过函数内容识别组件', () => {
            const ComponentWithJSX = function () { return React.createElement('div'); };
            expect(isReactComponentEnhanced(ComponentWithJSX)).toBe(true);

            const ComponentWithHooks = function () { const [state] = useState(); };
            expect(isReactComponentEnhanced(ComponentWithHooks)).toBe(true);
        });

        it('应该识别有 render 方法的对象', () => {
            const ObjectWithRender = { render: () => null };
            expect(isReactComponentEnhanced(ObjectWithRender)).toBe(true);
        });
    });

    describe('容器管理', () => {
        beforeEach(() => {
            vi.mocked(document.getElementById).mockReturnValue(null);
            vi.mocked(document.createElement).mockReturnValue({
                id: '',
                className: '',
                setAttribute: vi.fn(),
                classList: { add: vi.fn() },
                appendChild: vi.fn(),
                removeChild: vi.fn(),
                firstChild: null,
                parentNode: null
            } as any);
        });

        describe('createReactContainer', () => {
            it('应该创建 React 容器', () => {
                const container = createReactContainer('test-app');
                expect(container).toBeDefined();
            });
        });

        describe('getReactContainer', () => {
            it('应该获取 React 容器', () => {
                const mockElement = document.createElement('div');
                vi.mocked(document.getElementById).mockReturnValue(mockElement);

                const container = getReactContainer('test-app');
                expect(container).toBe(mockElement);
                expect(document.getElementById).toHaveBeenCalledWith('micro-app-test-app');
            });

            it('应该在容器不存在时返回 null', () => {
                vi.mocked(document.getElementById).mockReturnValue(null);

                const container = getReactContainer('non-existent');
                expect(container).toBe(null);
            });
        });
    });

    describe('开发工具', () => {
        describe('isReactDevToolsAvailable', () => {
            it('应该检测 React DevTools 可用性', () => {
                expect(isReactDevToolsAvailable()).toBe(true);
            });

            it('应该在没有 DevTools 时返回 false', () => {
                const originalHook = (global as any).window.__REACT_DEVTOOLS_GLOBAL_HOOK__;
                delete (global as any).window.__REACT_DEVTOOLS_GLOBAL_HOOK__;
                expect(isReactDevToolsAvailable()).toBe(false);
                (global as any).window.__REACT_DEVTOOLS_GLOBAL_HOOK__ = originalHook;
            });
        });

        describe('enableReactDevTools', () => {
            it('应该启用 React DevTools', () => {
                const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => { });
                enableReactDevTools('test-app');
                expect(consoleSpy).toHaveBeenCalledWith('React DevTools enabled for app: test-app');
                consoleSpy.mockRestore();
            });
        });
    });

    describe('错误处理', () => {
        describe('createReactErrorInfo', () => {
            it('应该创建错误信息对象', () => {
                const error = new Error('Test error');
                const errorInfo = createReactErrorInfo(error, 'component stack');

                expect(errorInfo.componentStack).toBe('component stack');
                expect(errorInfo.errorBoundary).toBe('ReactAdapter');
                expect(errorInfo.timestamp).toBeDefined();
                expect(errorInfo.userAgent).toBe('test-agent');
                expect(errorInfo.url).toBe('http://localhost:3000');
            });
        });

        describe('formatReactError', () => {
            it('应该格式化 React 错误', () => {
                const error = new Error('Test error');
                const errorInfo = { componentStack: 'stack', errorBoundary: 'boundary' };
                const formatted = formatReactError(error, errorInfo);

                expect(formatted).toContain('Test error');
            });
        });
    });

    describe('配置合并', () => {
        describe('mergeReactConfigs', () => {
            it('应该合并 React 配置', () => {
                const base = {
                    name: 'base-app',
                    react: { reactVersion: '17' }
                };
                const override = {
                    name: 'override-app',
                    react: { enableDevTools: true }
                };

                const merged = mergeReactConfigs(base, override);
                expect(merged.name).toBe('override-app');
                expect(merged.react?.reactVersion).toBe('17');
                expect(merged.react?.enableDevTools).toBe(true);
            });
        });
    });

    describe('ReactAdapterUtils', () => {
        it('应该导出所有工具函数', () => {
            expect(ReactAdapterUtils.extractReactComponent).toBe(extractReactComponent);
            expect(ReactAdapterUtils.isReactComponent).toBe(isReactComponent);
            expect(ReactAdapterUtils.isReactComponentEnhanced).toBe(isReactComponentEnhanced);
            expect(ReactAdapterUtils.isReactApp).toBe(isReactApp);
            expect(ReactAdapterUtils.isReactEntry).toBe(isReactEntry);
            expect(ReactAdapterUtils.getReactVersion).toBe(getReactVersion);
            expect(ReactAdapterUtils.isReactVersionCompatible).toBe(isReactVersionCompatible);
            expect(ReactAdapterUtils.validateReactConfig).toBe(validateReactConfig);
            expect(ReactAdapterUtils.createDefaultReactConfig).toBe(createDefaultReactConfig);
            expect(ReactAdapterUtils.mergeReactConfigs).toBe(mergeReactConfigs);
            expect(ReactAdapterUtils.createReactContainer).toBe(createReactContainer);
            expect(ReactAdapterUtils.cleanupReactContainer).toBe(cleanupReactContainer);
            expect(ReactAdapterUtils.getReactContainer).toBe(getReactContainer);
            expect(ReactAdapterUtils.formatReactError).toBe(formatReactError);
            expect(ReactAdapterUtils.createReactErrorInfo).toBe(createReactErrorInfo);
            expect(ReactAdapterUtils.isReactDevToolsAvailable).toBe(isReactDevToolsAvailable);
            expect(ReactAdapterUtils.enableReactDevTools).toBe(enableReactDevTools);
            expect(ReactAdapterUtils.createReactAdapter).toBe(createReactAdapter);
        });
    });
});