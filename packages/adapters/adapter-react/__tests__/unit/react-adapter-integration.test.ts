/**
 * @fileoverview React 适配器类测试
 * @description 测试 React 适配器的核心功能
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

import { AdapterDependencies, AdapterStatus } from '@micro-core/shared/utils';
import React from 'react';
import { ReactAdapter, createReactAdapter } from '../../src/react-adapter';
import type { ReactAppConfig } from '../../src/types';

// Mock React DOM
jest.mock('react-dom/client', () => ({
    createRoot: jest.fn(() => ({
        render: jest.fn(),
        unmount: jest.fn()
    }))
}));

// Mock React components
const TestComponent = (props: any) => React.createElement('div', null, 'Test Component');
const TestClassComponent = class extends React.Component {
    render() {
        return React.createElement('div', null, 'Test Class Component');
    }
};

// Mock dependencies
const mockDependencies: AdapterDependencies = {
    lifecycleManager: {
        beforeLoad: jest.fn(),
        beforeMount: jest.fn(),
        afterMount: jest.fn(),
        beforeUnmount: jest.fn(),
        afterUnmount: jest.fn()
    },
    sandboxManager: {
        createSandbox: jest.fn().mockResolvedValue({}),
        destroySandbox: jest.fn().mockResolvedValue(undefined),
        getSandbox: jest.fn().mockReturnValue({})
    },
    communicationManager: {
        emit: jest.fn(),
        on: jest.fn(),
        off: jest.fn(),
        broadcast: jest.fn()
    }
};

// Mock DOM
Object.defineProperty(global, 'document', {
    value: {
        createElement: jest.fn(() => ({
            id: '',
            className: '',
            style: {},
            setAttribute: jest.fn(),
            appendChild: jest.fn(),
            removeChild: jest.fn(),
            innerHTML: ''
        })),
        body: {
            appendChild: jest.fn(),
            removeChild: jest.fn(),
            contains: jest.fn().mockReturnValue(true)
        },
        getElementById: jest.fn()
    },
    writable: true
});

describe('ReactAdapter', () => {
    let adapter: ReactAdapter;
    let config: ReactAppConfig;

    beforeEach(() => {
        config = {
            name: 'test-react-app',
            framework: 'react',
            component: TestComponent,
            props: { test: 'value' }
        };

        adapter = new ReactAdapter(config, mockDependencies);

        // 清理 mock 调用记录
        jest.clearAllMocks();
    });

    describe('构造函数', () => {
        test('应该正确初始化适配器', () => {
            expect(adapter.getStatus()).toBe(AdapterStatus.NOT_LOADED);
            expect(adapter.getConfig()).toEqual(config);
            expect(adapter.getAppInstance()).toBeNull();
            expect(adapter.getContainer()).toBeUndefined();
            expect(adapter.getError()).toBeUndefined();
        });
    });

    describe('canHandle', () => {
        test('应该识别 React 应用配置', () => {
            expect(adapter.canHandle({ framework: 'react' })).toBe(true);
            expect(adapter.canHandle({ react: {} })).toBe(true);
            expect(adapter.canHandle({ component: TestComponent })).toBe(true);
            expect(adapter.canHandle({ entry: 'https://example.com/react-app.js' })).toBe(true);
        });

        test('应该拒绝非 React 应用配置', () => {
            expect(adapter.canHandle({ framework: 'vue' })).toBe(false);
            expect(adapter.canHandle({ framework: 'angular' })).toBe(false);
            expect(adapter.canHandle({})).toBe(false);
        });
    });

    describe('load', () => {
        test('应该成功加载 React 应用', async () => {
            await adapter.load(config);

            expect(adapter.getStatus()).toBe(AdapterStatus.LOADED);
            expect(mockDependencies.lifecycleManager.beforeLoad).toHaveBeenCalledWith(config);
            expect(mockDependencies.sandboxManager.createSandbox).not.toHaveBeenCalled(); // 没有沙箱配置
        });

        test('应该在有沙箱配置时创建沙箱', async () => {
            const configWithSandbox = {
                ...config,
                sandbox: { strictStyleIsolation: true }
            };

            const adapterWithSandbox = new ReactAdapter(configWithSandbox, mockDependencies);
            await adapterWithSandbox.load(configWithSandbox);

            expect(mockDependencies.sandboxManager.createSandbox).toHaveBeenCalledWith(
                configWithSandbox.name,
                configWithSandbox
            );
        });

        test('应该在配置无效时抛出错误', async () => {
            const invalidConfig = {
                framework: 'react'
            } as ReactAppConfig;

            const invalidAdapter = new ReactAdapter(invalidConfig, mockDependencies);

            await expect(invalidAdapter.load(invalidConfig)).rejects.toThrow();
            expect(invalidAdapter.getStatus()).toBe(AdapterStatus.ERROR);
        });

        test('应该处理组件准备失败', async () => {
            const configWithoutComponent = {
                name: 'test-app',
                framework: 'react'
            } as ReactAppConfig;

            const adapterWithoutComponent = new ReactAdapter(configWithoutComponent, mockDependencies);

            await expect(adapterWithoutComponent.load(configWithoutComponent)).rejects.toThrow(
                'Either component or entry must be provided'
            );
        });
    });

    describe('mount', () => {
        beforeEach(async () => {
            await adapter.load(config);
        });

        test('应该成功挂载 React 应用', async () => {
            await adapter.mount();

            expect(adapter.getStatus()).toBe(AdapterStatus.MOUNTED);
            expect(mockDependencies.lifecycleManager.beforeMount).toHaveBeenCalledWith(config);
            expect(mockDependencies.lifecycleManager.afterMount).toHaveBeenCalledWith(config);
            expect(adapter.getAppInstance()).not.toBeNull();
        });

        test('应该在未加载时拒绝挂载', async () => {
            const freshAdapter = new ReactAdapter(config, mockDependencies);

            await expect(freshAdapter.mount()).rejects.toThrow(
                'App must be loaded before mounting'
            );
        });

        test('应该创建 React root 并渲染组件', async () => {
            const { createRoot } = require('react-dom/client');
            const mockRoot = { render: jest.fn(), unmount: jest.fn() };
            createRoot.mockReturnValue(mockRoot);

            await adapter.mount();

            expect(createRoot).toHaveBeenCalled();
            expect(mockRoot.render).toHaveBeenCalled();
        });

        test('应该触发通信事件', async () => {
            await adapter.mount();

            expect(mockDependencies.communicationManager.emit).toHaveBeenCalledWith(
                'adapter:status-change',
                expect.objectContaining({
                    appName: config.name,
                    newStatus: AdapterStatus.MOUNTED
                })
            );
        });
    });

    describe('unmount', () => {
        beforeEach(async () => {
            await adapter.load(config);
            await adapter.mount();
        });

        test('应该成功卸载 React 应用', async () => {
            await adapter.unmount();

            expect(adapter.getStatus()).toBe(AdapterStatus.UNMOUNTED);
            expect(mockDependencies.lifecycleManager.beforeUnmount).toHaveBeenCalledWith(config);
            expect(mockDependencies.lifecycleManager.afterUnmount).toHaveBeenCalledWith(config);
            expect(adapter.getAppInstance()).toBeNull();
        });

        test('应该卸载 React root', async () => {
            const { createRoot } = require('react-dom/client');
            const mockRoot = { render: jest.fn(), unmount: jest.fn() };
            createRoot.mockReturnValue(mockRoot);

            // 重新挂载以获取 root
            await adapter.unmount();
            await adapter.mount();
            await adapter.unmount();

            expect(mockRoot.unmount).toHaveBeenCalled();
        });

        test('应该在有沙箱时销毁沙箱', async () => {
            const configWithSandbox = {
                ...config,
                sandbox: { strictStyleIsolation: true }
            };

            const adapterWithSandbox = new ReactAdapter(configWithSandbox, mockDependencies);
            await adapterWithSandbox.load(configWithSandbox);
            await adapterWithSandbox.mount();
            await adapterWithSandbox.unmount();

            expect(mockDependencies.sandboxManager.destroySandbox).toHaveBeenCalledWith(
                configWithSandbox.name
            );
        });

        test('应该在未挂载时安全返回', async () => {
            const freshAdapter = new ReactAdapter(config, mockDependencies);
            await freshAdapter.load(config);

            await expect(freshAdapter.unmount()).resolves.toBeUndefined();
            expect(freshAdapter.getStatus()).toBe(AdapterStatus.LOADED); // 状态不变
        });
    });

    describe('update', () => {
        beforeEach(async () => {
            await adapter.load(config);
            await adapter.mount();
        });

        test('应该成功更新应用属性', async () => {
            const newProps = { updated: true };

            await adapter.update(newProps);

            const updatedConfig = adapter.getConfig();
            expect(updatedConfig.props).toEqual({ test: 'value', updated: true });
        });

        test('应该重新渲染组件', async () => {
            const { createRoot } = require('react-dom/client');
            const mockRoot = { render: jest.fn(), unmount: jest.fn() };
            createRoot.mockReturnValue(mockRoot);

            // 重新挂载以获取 root
            await adapter.unmount();
            await adapter.mount();

            const renderCallsBefore = mockRoot.render.mock.calls.length;
            await adapter.update({ updated: true });
            const renderCallsAfter = mockRoot.render.mock.calls.length;

            expect(renderCallsAfter).toBeGreaterThan(renderCallsBefore);
        });

        test('应该触发属性更新事件', async () => {
            const newProps = { updated: true };

            await adapter.update(newProps);

            expect(mockDependencies.communicationManager.emit).toHaveBeenCalledWith(
                'app:props-updated',
                {
                    appName: config.name,
                    props: { test: 'value', updated: true }
                }
            );
        });

        test('应该在未挂载时拒绝更新', async () => {
            await adapter.unmount();

            await expect(adapter.update({ updated: true })).rejects.toThrow(
                'App must be mounted before updating'
            );
        });
    });

    describe('destroy', () => {
        test('应该完全销毁适配器', async () => {
            await adapter.load(config);
            await adapter.mount();
            await adapter.destroy();

            expect(adapter.getStatus()).toBe(AdapterStatus.UNMOUNTED);
            expect(adapter.getAppInstance()).toBeNull();
        });

        test('应该在未挂载时安全销毁', async () => {
            await adapter.load(config);
            await adapter.destroy();

            expect(adapter.getStatus()).toBe(AdapterStatus.UNMOUNTED);
        });
    });

    describe('错误处理', () => {
        test('应该在加载失败时设置错误状态', async () => {
            const invalidConfig = {
                name: 'test-app',
                framework: 'react'
            } as ReactAppConfig;

            const invalidAdapter = new ReactAdapter(invalidConfig, mockDependencies);

            await expect(invalidAdapter.load(invalidConfig)).rejects.toThrow();
            expect(invalidAdapter.getStatus()).toBe(AdapterStatus.ERROR);
            expect(invalidAdapter.getError()).toBeDefined();
        });

        test('应该在挂载失败时设置错误状态', async () => {
            await adapter.load(config);

            // 模拟挂载失败
            const { createRoot } = require('react-dom/client');
            createRoot.mockImplementation(() => {
                throw new Error('Mount failed');
            });

            await expect(adapter.mount()).rejects.toThrow('Mount failed');
            expect(adapter.getStatus()).toBe(AdapterStatus.ERROR);
        });

        test('应该触发错误事件', async () => {
            const invalidConfig = {
                name: 'test-app',
                framework: 'react'
            } as ReactAppConfig;

            const invalidAdapter = new ReactAdapter(invalidConfig, mockDependencies);

            await expect(invalidAdapter.load(invalidConfig)).rejects.toThrow();

            expect(mockDependencies.communicationManager.emit).toHaveBeenCalledWith(
                'adapter:error',
                expect.objectContaining({
                    appName: invalidConfig.name
                })
            );
        });
    });

    describe('生命周期钩子', () => {
        test('应该执行所有生命周期钩子', async () => {
            await adapter.load(config);
            await adapter.mount();
            await adapter.unmount();

            expect(mockDependencies.lifecycleManager.beforeLoad).toHaveBeenCalledWith(config);
            expect(mockDependencies.lifecycleManager.beforeMount).toHaveBeenCalledWith(config);
            expect(mockDependencies.lifecycleManager.afterMount).toHaveBeenCalledWith(config);
            expect(mockDependencies.lifecycleManager.beforeUnmount).toHaveBeenCalledWith(config);
            expect(mockDependencies.lifecycleManager.afterUnmount).toHaveBeenCalledWith(config);
        });

        test('应该在生命周期钩子失败时继续执行', async () => {
            // 模拟钩子失败
            mockDependencies.lifecycleManager.beforeMount = jest.fn().mockRejectedValue(
                new Error('Hook failed')
            );

            await adapter.load(config);
            await expect(adapter.mount()).resolves.toBeUndefined(); // 不应该抛出错误
            expect(adapter.getStatus()).toBe(AdapterStatus.MOUNTED);
        });
    });

    describe('容器管理', () => {
        test('应该创建带有 React 特定属性的容器', async () => {
            await adapter.load(config);

            const container = adapter.getContainer();
            expect(container).toBeDefined();
            expect(container?.className).toContain('react-app-container');
            expect(container?.setAttribute).toHaveBeenCalledWith('data-framework', 'react');
        });

        test('应该支持自定义容器', async () => {
            const customContainer = document.createElement('div');
            const configWithContainer = {
                ...config,
                container: customContainer
            };

            const adapterWithContainer = new ReactAdapter(configWithContainer, mockDependencies);
            await adapterWithContainer.load(configWithContainer);

            expect(adapterWithContainer.getContainer()).toBe(customContainer);
        });
    });

    describe('工厂函数', () => {
        test('createReactAdapter 应该创建适配器实例', () => {
            const createdAdapter = createReactAdapter(config, mockDependencies);
            expect(createdAdapter).toBeInstanceOf(ReactAdapter);
            expect(createdAdapter.getConfig()).toEqual(config);
        });
    });

    describe('性能测试', () => {
        test('加载和挂载应该在合理时间内完成', async () => {
            const start = Date.now();

            await adapter.load(config);
            await adapter.mount();

            const duration = Date.now() - start;
            expect(duration).toBeLessThan(1000); // 应该在1秒内完成
        });

        test('多次更新应该保持性能', async () => {
            await adapter.load(config);
            await adapter.mount();

            const start = Date.now();

            for (let i = 0; i < 100; i++) {
                await adapter.update({ counter: i });
            }

            const duration = Date.now() - start;
            expect(duration).toBeLessThan(1000); // 100次更新应该在1秒内完成
        });
    });

    describe('内存管理', () => {
        test('卸载后应该清理所有引用', async () => {
            await adapter.load(config);
            await adapter.mount();
            await adapter.unmount();

            expect(adapter.getAppInstance()).toBeNull();
            // 私有属性无法直接测试，但可以通过行为验证
        });

        test('销毁后应该完全清理', async () => {
            await adapter.load(config);
            await adapter.mount();
            await adapter.destroy();

            expect(adapter.getStatus()).toBe(AdapterStatus.UNMOUNTED);
            expect(adapter.getAppInstance()).toBeNull();
            expect(adapter.getError()).toBeUndefined();
        });
    });
});