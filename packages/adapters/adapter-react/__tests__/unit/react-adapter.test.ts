/**
 * React Adapter Tests
 */

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { ReactAdapter } from '../src/react-adapter';
import type { ReactAppConfig } from '../src/types';

// Mock React and ReactDOM
const mockReact = {
  createElement: vi.fn((type, props, ...children) => ({ type, props, children })),
  StrictMode: 'StrictMode',
  Component: class MockComponent { },
  Fragment: 'Fragment'
};

const mockReactDOM = {
  createRoot: vi.fn(() => ({
    render: vi.fn(),
    unmount: vi.fn()
  })),
  render: vi.fn(),
  unmountComponentAtNode: vi.fn()
};

// Mock dependencies
const mockLifecycleManager = {
  notifyMounted: vi.fn(),
  notifyUnmounted: vi.fn(),
  registerHook: vi.fn(),
  executeHook: vi.fn()
};

const mockSandboxManager = {
  createSandbox: vi.fn().mockResolvedValue({}),
  destroySandbox: vi.fn(),
  getSandbox: vi.fn()
};

const mockCommunicationManager = {
  emit: vi.fn(),
  on: vi.fn(),
  off: vi.fn(),
  subscribe: vi.fn(),
  unsubscribe: vi.fn()
};

const mockErrorHandler = {
  handleError: vi.fn(),
  captureError: vi.fn(),
  reportError: vi.fn()
};

describe('ReactAdapter', () => {
  let adapter: ReactAdapter;
  let mockConfig: ReactAppConfig;
  let mockContainer: HTMLElement;

  beforeEach(() => {
    // Setup DOM
    mockContainer = document.createElement('div');
    mockContainer.id = 'test-app';
    document.body.appendChild(mockContainer);

    adapter = new ReactAdapter(
      {
        reactVersion: '18',
        enableDevTools: true,
        strictMode: false
      },
      mockLifecycleManager as any,
      mockSandboxManager as any,
      mockCommunicationManager as any,
      mockErrorHandler as any
    );

    mockConfig = {
      name: 'test-react-app',
      entry: 'src/App.tsx',
      container: '#test-app',
      react: {
        component: () => mockReact.createElement('div', null, 'Test App')
      }
    };

    // Reset mocks
    vi.clearAllMocks();
  });

  afterEach(() => {
    // Cleanup DOM
    if (mockContainer.parentNode) {
      document.body.removeChild(mockContainer);
    }
    vi.restoreAllMocks();
  });

  describe('初始化和配置', () => {
    it('应该正确初始化适配器', () => {
      expect(adapter.name).toBe('react');
      expect(adapter.config.reactVersion).toBe('18');
      expect(adapter.config.enableDevTools).toBe(true);
      expect(adapter.config.strictMode).toBe(false);
    });

    it('应该接受自定义配置', () => {
      const customConfig = {
        reactVersion: '17',
        enableDevTools: false,
        strictMode: true
      };
      const customAdapter = new ReactAdapter(
        customConfig,
        mockLifecycleManager as any,
        mockSandboxManager as any,
        mockCommunicationManager as any,
        mockErrorHandler as any
      );

      expect(customAdapter.config.reactVersion).toBe('17');
      expect(customAdapter.config.enableDevTools).toBe(false);
      expect(customAdapter.config.strictMode).toBe(true);
    });
  });

  describe('canHandle', () => {
    it('应该能够处理 React 应用配置', () => {
      expect(adapter.canHandle({
        name: 'test',
        react: {}
      } as any)).toBe(true);

      expect(adapter.canHandle({
        name: 'test',
        component: () => null
      } as any)).toBe(true);

      expect(adapter.canHandle({
        name: 'test',
        entry: 'src/react-app.tsx'
      } as any)).toBe(true);
    });

    it('应该拒绝非 React 应用配置', () => {
      expect(adapter.canHandle({
        name: 'test',
        entry: 'src/vue-app.vue'
      } as any)).toBe(false);

      expect(adapter.canHandle({
        name: 'test',
        vue: {}
      } as any)).toBe(false);

      expect(adapter.canHandle({
        name: 'test'
      } as any)).toBe(false);
    });

    it('应该处理边界情况', () => {
      expect(adapter.canHandle(null as any)).toBe(false);
      expect(adapter.canHandle(undefined as any)).toBe(false);
      expect(adapter.canHandle({} as any)).toBe(false);
    });
  });

  describe('load', () => {
    it('应该成功加载 React 应用', async () => {
      const appInstance = await adapter.load(mockConfig);

      expect(appInstance).toBeDefined();
      expect(appInstance.config).toEqual(mockConfig);
      expect(appInstance.status).toBe('loaded');
      expect(mockSandboxManager.createSandbox).toHaveBeenCalledWith(
        mockConfig.name,
        expect.any(Object)
      );
      expect(mockLifecycleManager.executeHook).toHaveBeenCalledWith(
        'beforeLoad',
        expect.any(Object)
      );
    });

    it('应该处理加载错误', async () => {
      const invalidConfig = { name: 'test' } as ReactAppConfig;

      await expect(adapter.load(invalidConfig)).rejects.toThrow();
      expect(mockErrorHandler.handleError).toHaveBeenCalled();
    });

    it('应该验证配置完整性', async () => {
      const incompleteConfig = {
        name: 'test-app'
        // 缺少必要的配置
      } as ReactAppConfig;

      await expect(adapter.load(incompleteConfig)).rejects.toThrow();
    });

    it('应该支持组件配置', async () => {
      const componentConfig = {
        ...mockConfig,
        react: {
          component: () => mockReact.createElement('div', null, 'Component App')
        }
      };

      const appInstance = await adapter.load(componentConfig);
      expect(appInstance).toBeDefined();
      expect(appInstance.config.react?.component).toBeDefined();
    });

    it('应该支持入口文件配置', async () => {
      const entryConfig = {
        ...mockConfig,
        entry: 'src/main.tsx'
      };

      const appInstance = await adapter.load(entryConfig);
      expect(appInstance).toBeDefined();
      expect(appInstance.config.entry).toBe('src/main.tsx');
    });
  });

  describe('mount', () => {
    it('应该成功挂载 React 应用', async () => {
      const appInstance = await adapter.load(mockConfig);
      await adapter.mount();

      expect(appInstance.status).toBe('mounted');
      expect(mockLifecycleManager.notifyMounted).toHaveBeenCalledWith(mockConfig.name);
      expect(mockLifecycleManager.executeHook).toHaveBeenCalledWith(
        'beforeMount',
        expect.any(Object)
      );
    });

    it('应该处理挂载错误', async () => {
      const appInstance = await adapter.load(mockConfig);

      // 模拟挂载错误
      mockReactDOM.createRoot.mockImplementationOnce(() => {
        throw new Error('Mount failed');
      });

      await expect(adapter.mount()).rejects.toThrow('Mount failed');
      expect(mockErrorHandler.handleError).toHaveBeenCalled();
    });

    it('应该支持严格模式', async () => {
      const strictAdapter = new ReactAdapter(
        { strictMode: true },
        mockLifecycleManager as any,
        mockSandboxManager as any,
        mockCommunicationManager as any,
        mockErrorHandler as any
      );

      const appInstance = await strictAdapter.load(mockConfig);
      await strictAdapter.mount();

      expect(mockReact.createElement).toHaveBeenCalledWith(
        'StrictMode',
        expect.any(Object),
        expect.any(Object)
      );
    });

    it('应该处理容器不存在的情况', async () => {
      const invalidConfig = {
        ...mockConfig,
        container: '#non-existent'
      };

      const appInstance = await adapter.load(invalidConfig);
      await expect(adapter.mount()).rejects.toThrow();
    });
  });

  describe('unmount', () => {
    it('应该成功卸载 React 应用', async () => {
      const appInstance = await adapter.load(mockConfig);
      await adapter.mount();
      await adapter.unmount();

      expect(appInstance.status).toBe('unmounted');
      expect(mockLifecycleManager.notifyUnmounted).toHaveBeenCalledWith(mockConfig.name);
      expect(mockLifecycleManager.executeHook).toHaveBeenCalledWith(
        'beforeUnmount',
        expect.any(Object)
      );
    });

    it('应该处理重复卸载', async () => {
      const appInstance = await adapter.load(mockConfig);
      await adapter.mount();
      await adapter.unmount();

      // 第二次卸载不应该抛出错误
      await expect(adapter.unmount()).resolves.not.toThrow();
    });

    it('应该清理 React 根节点', async () => {
      const mockRoot = {
        render: vi.fn(),
        unmount: vi.fn()
      };
      mockReactDOM.createRoot.mockReturnValue(mockRoot);

      const appInstance = await adapter.load(mockConfig);
      await adapter.mount();
      await adapter.unmount();

      expect(mockRoot.unmount).toHaveBeenCalled();
    });
  });

  describe('错误处理', () => {
    it('应该捕获渲染错误', async () => {
      const errorConfig = {
        ...mockConfig,
        react: {
          component: () => {
            throw new Error('Render error');
          }
        }
      };

      const appInstance = await adapter.load(errorConfig);
      await expect(adapter.mount()).rejects.toThrow('Render error');
      expect(mockErrorHandler.handleError).toHaveBeenCalled();
    });

    it('应该使用错误边界', async () => {
      const errorBoundaryAdapter = new ReactAdapter(
        { errorBoundary: true },
        mockLifecycleManager as any,
        mockSandboxManager as any,
        mockCommunicationManager as any,
        mockErrorHandler as any
      );

      const appInstance = await errorBoundaryAdapter.load(mockConfig);
      await errorBoundaryAdapter.mount();

      // 验证错误边界被使用
      expect(mockReact.createElement).toHaveBeenCalledWith(
        expect.any(Function), // ReactErrorBoundary
        expect.any(Object),
        expect.any(Object)
      );
    });
  });

  describe('生命周期钩子', () => {
    it('应该执行所有生命周期钩子', async () => {
      const appInstance = await adapter.load(mockConfig);
      await adapter.mount();
      await adapter.unmount();

      expect(mockLifecycleManager.executeHook).toHaveBeenCalledWith('beforeLoad', expect.any(Object));
      expect(mockLifecycleManager.executeHook).toHaveBeenCalledWith('afterLoad', expect.any(Object));
      expect(mockLifecycleManager.executeHook).toHaveBeenCalledWith('beforeMount', expect.any(Object));
      expect(mockLifecycleManager.executeHook).toHaveBeenCalledWith('afterMount', expect.any(Object));
      expect(mockLifecycleManager.executeHook).toHaveBeenCalledWith('beforeUnmount', expect.any(Object));
      expect(mockLifecycleManager.executeHook).toHaveBeenCalledWith('afterUnmount', expect.any(Object));
    });
  });

  describe('性能测试', () => {
    it('应该在合理时间内完成加载', async () => {
      const startTime = Date.now();
      await adapter.load(mockConfig);
      const endTime = Date.now();

      expect(endTime - startTime).toBeLessThan(1000); // 1秒内完成
    });

    it('应该在合理时间内完成挂载', async () => {
      const appInstance = await adapter.load(mockConfig);

      const startTime = Date.now();
      await adapter.mount();
      const endTime = Date.now();

      expect(endTime - startTime).toBeLessThan(500); // 500ms内完成
    });
  });
});
