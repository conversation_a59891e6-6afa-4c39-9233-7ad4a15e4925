/**
 * Svelte 适配器测试用例
 */

import { beforeEach, describe, expect, it, vi } from 'vitest';
import { SvelteAdapter } from '../src';

// 模拟 Svelte 组件
const mockSvelteComponent = {
    $$render: vi.fn(),
    $set: vi.fn(),
    $destroy: vi.fn()
};

// 模拟 Svelte 构造函数
const MockSvelteConstructor = vi.fn().mockImplementation(() => mockSvelteComponent);

describe('SvelteAdapter', () => {
    let adapter: SvelteAdapter;
    let mockContainer: HTMLElement;
    let mockComponent: any;
    let mockProps: Record<string, any>;

    beforeEach(() => {
        // 重置模拟
        vi.resetAllMocks();

        // 创建模拟 DOM 元素
        mockContainer = document.createElement('div');
        document.body.appendChild(mockContainer);

        // 创建模拟组件和属性
        mockComponent = MockSvelteConstructor;
        mockProps = { testProp: 'value' };

        // 创建适配器实例
        adapter = new SvelteAdapter();
    });

    afterEach(() => {
        // 清理 DOM
        document.body.removeChild(mockContainer);
    });

    describe('挂载', () => {
        it('应该能够挂载 Svelte 组件', async () => {
            await adapter.mount({
                component: mockComponent,
                container: mockContainer,
                props: mockProps
            });

            expect(MockSvelteConstructor).toHaveBeenCalledWith({
                target: mockContainer,
                props: mockProps
            });
        });

        it('应该能够处理挂载选项', async () => {
            const options = {
                intro: true,
                anchor: document.createElement('div')
            };

            await adapter.mount({
                component: mockComponent,
                container: mockContainer,
                props: mockProps,
                options
            });

            expect(MockSvelteConstructor).toHaveBeenCalledWith({
                target: mockContainer,
                props: mockProps,
                intro: true,
                anchor: options.anchor
            });
        });

        it('应该在挂载失败时抛出错误', async () => {
            MockSvelteConstructor.mockImplementationOnce(() => {
                throw new Error('挂载失败');
            });

            await expect(adapter.mount({
                component: mockComponent,
                container: mockContainer,
                props: mockProps
            })).rejects.toThrow('挂载失败');
        });
    });

    describe('卸载', () => {
        it('应该能够卸载 Svelte 组件', async () => {
            // 先挂载组件
            await adapter.mount({
                component: mockComponent,
                container: mockContainer,
                props: mockProps
            });

            // 然后卸载
            await adapter.unmount({ container: mockContainer });

            expect(mockSvelteComponent.$destroy).toHaveBeenCalled();
        });

        it('应该在卸载失败时抛出错误', async () => {
            // 先挂载组件
            await adapter.mount({
                component: mockComponent,
                container: mockContainer,
                props: mockProps
            });

            // 模拟卸载失败
            mockSvelteComponent.$destroy.mockImplementationOnce(() => {
                throw new Error('卸载失败');
            });

            await expect(adapter.unmount({ container: mockContainer })).rejects.toThrow('卸载失败');
        });

        it('应该在容器不存在时不执行卸载', async () => {
            await adapter.unmount({ container: null as any });
            expect(mockSvelteComponent.$destroy).not.toHaveBeenCalled();
        });
    });

    describe('更新', () => {
        it('应该能够更新 Svelte 组件的属性', async () => {
            // 先挂载组件
            await adapter.mount({
                component: mockComponent,
                container: mockContainer,
                props: mockProps
            });

            // 然后更新属性
            const newProps = { testProp: 'newValue' };
            await adapter.update({
                container: mockContainer,
                props: newProps
            });

            expect(mockSvelteComponent.$set).toHaveBeenCalledWith(newProps);
        });

        it('应该在更新失败时抛出错误', async () => {
            // 先挂载组件
            await adapter.mount({
                component: mockComponent,
                container: mockContainer,
                props: mockProps
            });

            // 模拟更新失败
            mockSvelteComponent.$set.mockImplementationOnce(() => {
                throw new Error('更新失败');
            });

            // 然后更新属性
            const newProps = { testProp: 'newValue' };
            await expect(adapter.update({
                container: mockContainer,
                props: newProps
            })).rejects.toThrow('更新失败');
        });
    });

    describe('工具函数', () => {
        it('应该能够检查是否是 Svelte 组件', () => {
            const svelteComponent = { $$render: () => { } };
            const nonSvelteComponent = { render: () => { } };

            expect(adapter.isValidComponent(svelteComponent)).toBe(true);
            expect(adapter.isValidComponent(nonSvelteComponent)).toBe(false);
        });

        it('应该能够获取适配器名称', () => {
            expect(adapter.getName()).toBe('svelte');
        });
    });

    describe('组件实例管理', () => {
        it('应该能够存储和检索组件实例', async () => {
            // 先挂载组件
            await adapter.mount({
                component: mockComponent,
                container: mockContainer,
                props: mockProps
            });

            // 检查是否存储了组件实例
            const instance = (adapter as any).getComponentInstance(mockContainer);
            expect(instance).toBe(mockSvelteComponent);
        });

        it('应该能够在卸载时移除组件实例', async () => {
            // 先挂载组件
            await adapter.mount({
                component: mockComponent,
                container: mockContainer,
                props: mockProps
            });

            // 然后卸载
            await adapter.unmount({ container: mockContainer });

            // 检查是否移除了组件实例
            const instance = (adapter as any).getComponentInstance(mockContainer);
            expect(instance).toBeUndefined();
        });
    });
});