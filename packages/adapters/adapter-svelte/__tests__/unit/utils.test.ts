/**
 * Svelte 适配器工具函数测试
 */

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import {
    SvelteMicroAppIntegration,
    createDefaultSvelteConfig,
    createSvelteAdapter,
    createSvelteContainer,
    createSvelteErrorInfo,
    extractSvelteComponent,
    formatSvelteError,
    getSvelteVersion,
    isSvelteApp,
    isSvelteComponent,
    isSvelteComponentEnhanced,
    isSvelteEntry,
    isSvelteVersionCompatible,
    mergeSvelteConfigs,
    setupSvelteDevTools,
    validateSvelteConfig
} from '../src/utils';

// Mock shared utils
vi.mock('@micro-core/shared/utils', () => ({
    mergeConfigs: vi.fn((base, override) => ({ ...base, ...override })),
    createEnhancedContainer: vi.fn((name, framework, parent, options) => {
        const div = document.createElement('div');
        div.id = `micro-app-${name}`;
        div.className = options?.className || '';
        return div;
    }),
    cleanupContainer: vi.fn(),
    formatAdapterError: vi.fn((error, framework, operation, appName) =>
        new Error(`${framework} ${operation} error in ${appName}: ${error.message}`)
    ),
    createAdapterErrorInfo: vi.fn((error, framework, context) => ({
        framework,
        error: error.message,
        context
    })),
    isObject: vi.fn((value) => value !== null && typeof value === 'object'),
    isFunction: vi.fn((value) => typeof value === 'function'),
    isString: vi.fn((value) => typeof value === 'string')
}));

describe('Svelte 适配器工具函数', () => {
    beforeEach(() => {
        vi.clearAllMocks();
        // 设置 DOM 环境
        Object.defineProperty(global, 'window', {
            value: {
                svelte: { VERSION: '4.0.0' }
            },
            writable: true
        });
    });

    afterEach(() => {
        vi.restoreAllMocks();
    });

    describe('createSvelteAdapter', () => {
        it('应该创建 Svelte 适配器', () => {
            const mockComponent = vi.fn();
            mockComponent.prototype = { $destroy: vi.fn() };

            const config = {
                name: 'test-app',
                component: mockComponent
            };

            const adapter = createSvelteAdapter(config);

            expect(adapter).toHaveProperty('name', 'test-app');
            expect(adapter).toHaveProperty('config');
            expect(adapter).toHaveProperty('mount');
            expect(adapter).toHaveProperty('unmount');
            expect(typeof adapter.mount).toBe('function');
            expect(typeof adapter.unmount).toBe('function');
        });

        it('应该使用默认配置', () => {
            const mockComponent = vi.fn();
            mockComponent.prototype = { $destroy: vi.fn() };

            const config = { name: 'test-app', component: mockComponent };
            const adapter = createSvelteAdapter(config);

            expect(adapter.config).toMatchObject({
                name: 'test-app',
                svelte: expect.objectContaining({
                    hydratable: false,
                    immutable: false,
                    accessors: false
                })
            });
        });
    });

    describe('isSvelteApp', () => {
        it('应该识别 Svelte 应用', () => {
            expect(isSvelteApp({ $$: {} })).toBe(true);
            expect(isSvelteApp({ $$set: vi.fn() })).toBe(true);
            expect(isSvelteApp({ $destroy: vi.fn() })).toBe(true);
            expect(isSvelteApp({ svelteConfig: {} })).toBe(true);
        });

        it('应该识别 Svelte 构造函数', () => {
            const mockConstructor = vi.fn();
            mockConstructor.prototype = { $destroy: vi.fn() };
            expect(isSvelteApp(mockConstructor)).toBe(true);
        });

        it('应该拒绝非 Svelte 应用', () => {
            expect(isSvelteApp(null)).toBe(false);
            expect(isSvelteApp(undefined)).toBe(false);
            expect(isSvelteApp('string')).toBe(false);
            expect(isSvelteApp({})).toBe(false);
            expect(isSvelteApp({ randomProp: 'value' })).toBe(false);
        });
    });

    describe('isSvelteEntry', () => {
        it('应该识别 Svelte 入口', () => {
            const mockComponent = vi.fn();
            mockComponent.prototype = { $destroy: vi.fn() };

            expect(isSvelteEntry({ App: mockComponent })).toBe(true);
            expect(isSvelteEntry({ Component: mockComponent })).toBe(true);
            expect(isSvelteEntry({ svelteConfig: {} })).toBe(true);
            expect(isSvelteEntry({ default: mockComponent })).toBe(true);
        });

        it('应该拒绝非 Svelte 入口', () => {
            expect(isSvelteEntry(null)).toBe(false);
            expect(isSvelteEntry({})).toBe(false);
            expect(isSvelteEntry({ randomExport: {} })).toBe(false);
        });
    });

    describe('getSvelteVersion', () => {
        it('应该从全局对象获取版本', () => {
            expect(getSvelteVersion()).toBe('4.0.0');
        });

        it('应该处理版本获取失败', () => {
            global.window = undefined as any;
            vi.doMock('svelte/package.json', () => {
                throw new Error('Module not found');
            });

            expect(getSvelteVersion()).toBeNull();
        });
    });

    describe('isSvelteVersionCompatible', () => {
        it('应该检查版本兼容性', () => {
            expect(isSvelteVersionCompatible('4.0.0', '3.0.0')).toBe(true);
            expect(isSvelteVersionCompatible('2.0.0', '3.0.0')).toBe(false);
            expect(isSvelteVersionCompatible('3.0.0', '3.0.0')).toBe(true);
            expect(isSvelteVersionCompatible('3.1.0', '3.0.0')).toBe(true);
        });

        it('应该处理无效版本', () => {
            expect(isSvelteVersionCompatible('', '3.0.0')).toBe(false);
            expect(isSvelteVersionCompatible(null as any, '3.0.0')).toBe(false);
        });
    });

    describe('validateSvelteConfig', () => {
        it('应该验证有效配置', () => {
            const mockComponent = vi.fn();
            const config = {
                name: 'test-app',
                component: mockComponent,
                svelte: {
                    dev: true,
                    hydratable: false
                }
            };

            const errors = validateSvelteConfig(config);
            expect(errors).toHaveLength(0);
        });

        it('应该检测配置错误', () => {
            const config = {
                name: '',
                svelte: {
                    dev: 'invalid' as any,
                    hydratable: 'invalid' as any
                }
            };

            const errors = validateSvelteConfig(config);
            expect(errors.length).toBeGreaterThan(0);
            expect(errors).toContain('配置中缺少有效的应用名称');
            expect(errors).toContain('配置中缺少 Svelte 组件');
        });
    });

    describe('createDefaultSvelteConfig', () => {
        it('应该创建默认配置', () => {
            const mockComponent = vi.fn();
            const config = createDefaultSvelteConfig({
                name: 'test-app',
                component: mockComponent
            });

            expect(config).toMatchObject({
                name: 'test-app',
                component: mockComponent,
                props: {},
                svelte: {
                    hydratable: false,
                    immutable: false,
                    accessors: false
                },
                container: {
                    className: 'svelte-app-container'
                }
            });
        });

        it('应该合并用户配置', () => {
            const mockComponent = vi.fn();
            const userConfig = {
                name: 'custom-app',
                component: mockComponent,
                svelte: {
                    dev: true
                }
            };

            const config = createDefaultSvelteConfig(userConfig);
            expect(config.svelte?.dev).toBe(true);
        });
    });

    describe('extractSvelteComponent', () => {
        it('应该提取首选组件', () => {
            const preferredComponent = vi.fn();
            preferredComponent.prototype = { $destroy: vi.fn() };

            const otherComponent = vi.fn();
            otherComponent.prototype = { $destroy: vi.fn() };

            const moduleExports = {
                PreferredComponent: preferredComponent,
                OtherComponent: otherComponent
            };

            const component = extractSvelteComponent(moduleExports, 'PreferredComponent');
            expect(component).toBe(preferredComponent);
        });

        it('应该提取默认导出', () => {
            const defaultComponent = vi.fn();
            defaultComponent.prototype = { $destroy: vi.fn() };

            const otherComponent = vi.fn();
            otherComponent.prototype = { $destroy: vi.fn() };

            const moduleExports = {
                default: defaultComponent,
                OtherComponent: otherComponent
            };

            const component = extractSvelteComponent(moduleExports);
            expect(component).toBe(defaultComponent);
        });

        it('应该提取命名导出', () => {
            const namedComponent = vi.fn();
            namedComponent.prototype = { $destroy: vi.fn() };
            namedComponent.name = 'NamedComponent';

            const unnamedComponent = vi.fn();
            unnamedComponent.prototype = { $destroy: vi.fn() };

            const moduleExports = {
                NamedComponent: namedComponent,
                UnnamedComponent: unnamedComponent
            };

            const component = extractSvelteComponent(moduleExports);
            expect(component).toBe(namedComponent); // 优先选择有名称的组件
        });

        it('应该抛出错误当没有找到组件时', () => {
            expect(() => extractSvelteComponent({})).toThrow('未找到有效的 Svelte 组件');
            expect(() => extractSvelteComponent(null)).toThrow('模块导出必须是对象');
        });
    });

    describe('isSvelteComponent', () => {
        it('应该识别 Svelte 组件构造函数', () => {
            const mockComponent = vi.fn();
            mockComponent.prototype = { $destroy: vi.fn() };
            expect(isSvelteComponent(mockComponent)).toBe(true);
        });

        it('应该识别 Svelte 组件静态属性', () => {
            const mockComponent = vi.fn();
            mockComponent.$$render = vi.fn();
            expect(isSvelteComponent(mockComponent)).toBe(true);

            const mockComponent2 = vi.fn();
            mockComponent2.render = vi.fn();
            expect(isSvelteComponent(mockComponent2)).toBe(true);
        });

        it('应该拒绝非 Svelte 组件', () => {
            expect(isSvelteComponent(null)).toBe(false);
            expect(isSvelteComponent({})).toBe(false);
            expect(isSvelteComponent('string')).toBe(false);
            expect(isSvelteComponent(vi.fn())).toBe(false); // 普通函数
        });
    });

    describe('isSvelteComponentEnhanced', () => {
        it('应该识别增强的 Svelte 组件', () => {
            const enhancedComponent = vi.fn();
            enhancedComponent.prototype = { $destroy: vi.fn() };
            enhancedComponent.microAppConfig = {};
            enhancedComponent.microAppHooks = {};

            expect(isSvelteComponentEnhanced(enhancedComponent)).toBe(true);
        });

        it('应该拒绝普通组件', () => {
            const normalComponent = vi.fn();
            normalComponent.prototype = { $destroy: vi.fn() };
            expect(isSvelteComponentEnhanced(normalComponent)).toBe(false);
        });
    });

    describe('createSvelteContainer', () => {
        it('应该创建 Svelte 容器', () => {
            const container = createSvelteContainer('test-app');

            expect(container).toBeDefined();
            expect(container.id).toBe('micro-app-test-app');
        });
    });

    describe('setupSvelteDevTools', () => {
        it('应该设置开发工具', () => {
            const mockInstance = {
                $$: { props: { test: 'value' } }
            };

            setupSvelteDevTools('test-app', mockInstance as any);

            expect((global.window as any).svelte).toBeDefined();
            expect((global.window as any).svelte['test-app']).toBeDefined();
        });

        it('应该处理设置失败', () => {
            const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => { });
            global.window = undefined as any;

            setupSvelteDevTools('test-app', {} as any);

            expect(consoleSpy).not.toHaveBeenCalled(); // 因为 window 检查
            consoleSpy.mockRestore();
        });
    });

    describe('formatSvelteError', () => {
        it('应该格式化错误', () => {
            const error = new Error('Test error');
            const formattedError = formatSvelteError(error, 'mount', 'test-app');

            expect(formattedError.message).toContain('Svelte');
            expect(formattedError.message).toContain('mount');
            expect(formattedError.message).toContain('test-app');
        });
    });

    describe('createSvelteErrorInfo', () => {
        it('应该创建错误信息', () => {
            const error = new Error('Test error');
            const context = { operation: 'mount' };
            const errorInfo = createSvelteErrorInfo(error, context);

            expect(errorInfo).toMatchObject({
                framework: 'Svelte',
                error: 'Test error',
                context
            });
        });
    });

    describe('mergeSvelteConfigs', () => {
        it('应该合并配置', () => {
            const base = { name: 'base', svelte: { dev: false } };
            const override = { svelte: { dev: true } };

            const merged = mergeSvelteConfigs(base, override);

            expect(merged).toMatchObject({
                name: 'base',
                svelte: { dev: true }
            });
        });
    });

    describe('SvelteMicroAppIntegration', () => {
        let integration: SvelteMicroAppIntegration;
        let mockElement: HTMLElement;
        let mockComponent: any;

        beforeEach(() => {
            mockComponent = vi.fn().mockImplementation(function (options: any) {
                return {
                    $destroy: vi.fn(),
                    $set: vi.fn(),
                    $$: { props: options.props || {} }
                };
            });

            const config = {
                name: 'test-app',
                component: mockComponent,
                svelte: { dev: true },
                lifecycle: {
                    mounted: vi.fn(),
                    beforeUnmount: vi.fn(),
                    unmounted: vi.fn(),
                    updated: vi.fn()
                }
            };
            integration = new SvelteMicroAppIntegration(config);
            mockElement = document.createElement('div') as any;
        });

        describe('mount', () => {
            it('应该挂载应用', async () => {
                await integration.mount(mockElement);

                expect(integration.getContainer()).toBeDefined();
                expect(integration.getInstance()).toBeDefined();
                expect(mockComponent).toHaveBeenCalled();
            });
        });

        describe('unmount', () => {
            it('应该卸载应用', async () => {
                await integration.mount(mockElement);
                const instance = integration.getInstance();

                await integration.unmount();

                expect(instance?.$destroy).toHaveBeenCalled();
                expect(integration.getContainer()).toBeNull();
                expect(integration.getInstance()).toBeNull();
            });
        });

        describe('updateProps', () => {
            it('应该更新属性', async () => {
                await integration.mount(mockElement);
                const instance = integration.getInstance();

                await integration.updateProps({ newProp: 'value' });

                expect(instance?.$set).toHaveBeenCalledWith({ newProp: 'value' });
            });

            it('应该处理未挂载时的更新', async () => {
                await expect(integration.updateProps({ prop: 'value' }))
                    .rejects.toThrow('组件未挂载，无法更新属性');
            });
        });
    });
});