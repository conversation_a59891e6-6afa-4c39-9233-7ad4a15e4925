/**
 * @fileoverview Shared Types for Micro-Core Builders
 * <AUTHOR> <<EMAIL>>
 * @copyright 2025 Micro-Core. All rights reserved.
 */

import type { MicroCoreKernel, Plugin } from '@micro-core/core';

/**
 * 基础构建器配置接口
 */
export interface BaseBuilderConfig {
  /** 应用ID */
  id: string;
  /** 应用名称 */
  name: string;
  /** 入口文件路径 */
  entry: string;
  /** 构建输出目录 */
  outDir?: string;
  /** 公共路径 */
  publicPath?: string;
  /** 是否为开发模式 */
  dev?: boolean;
  /** 环境变量 */
  env?: Record<string, string>;
  /** 自定义配置 */
  customConfig?: Record<string, any>;
}

/**
 * 构建器选项接口
 */
export interface BaseBuilderOptions {
  /** 是否启用开发模式 */
  dev?: boolean;
  /** 构建输出目录 */
  outDir?: string;
  /** 公共路径 */
  publicPath?: string;
  /** 是否启用模块联邦 */
  enableModuleFederation?: boolean;
  /** 是否启用热更新 */
  enableHMR?: boolean;
  /** 微前端配置 */
  microApp?: {
    /** 应用名称 */
    name: string;
    /** 应用入口 */
    entry: string;
    /** 应用路由 */
    routes?: string[];
    /** 应用权限 */
    permissions?: string[];
  };
  /** 插件配置 */
  plugins?: Array<{
    name: string;
    options?: Record<string, any>;
  }>;
  /** 外部依赖 */
  externals?: Record<string, string>;
  /** 别名配置 */
  alias?: Record<string, string>;
  /** 代理配置 */
  proxy?: Record<string, any>;
}

/**
 * 构建结果接口
 */
export interface BuildResult {
  /** 构建是否成功 */
  success: boolean;
  /** 构建输出文件 */
  outputs?: Array<{
    type: 'chunk' | 'asset';
    fileName: string;
    size: number;
    code?: string;
    source?: string | Uint8Array;
  }>;
  /** 构建统计信息 */
  stats?: {
    /** 构建时长 */
    duration: number;
    /** 文件数量 */
    fileCount: number;
    /** 总大小 */
    totalSize: number;
    /** 错误数量 */
    errors: number;
    /** 警告数量 */
    warnings: number;
  };
  /** 错误信息 */
  errors?: Array<{
    message: string;
    stack?: string;
    file?: string;
    line?: number;
    column?: number;
  }>;
  /** 警告信息 */
  warnings?: Array<{
    message: string;
    file?: string;
    line?: number;
    column?: number;
  }>;
}

/**
 * 开发服务器配置接口
 */
export interface DevServerConfig {
  /** 端口号 */
  port?: number;
  /** 主机地址 */
  host?: string;
  /** 是否启用HTTPS */
  https?: boolean;
  /** 是否启用热更新 */
  hot?: boolean;
  /** 是否启用CORS */
  cors?: boolean;
  /** 自定义头部 */
  headers?: Record<string, string>;
  /** 代理配置 */
  proxy?: Record<string, any>;
  /** 中间件 */
  middlewares?: Array<(req: any, res: any, next: any) => void>;
}

/**
 * 构建器接口
 */
export interface Builder extends Plugin {
  /** 构建器名称 */
  readonly name: string;
  /** 构建器版本 */
  readonly version: string;
  
  /**
   * 创建构建配置
   */
  createConfig(config: BaseBuilderConfig): any;
  
  /**
   * 执行构建
   */
  build(config: BaseBuilderConfig): Promise<BuildResult>;
  
  /**
   * 启动开发服务器
   */
  serve?(config: BaseBuilderConfig & { devServer?: DevServerConfig }): Promise<any>;
  
  /**
   * 停止开发服务器
   */
  stop?(): Promise<void>;
  
  /**
   * 获取构建器状态
   */
  getStatus(): {
    name: string;
    version: string;
    isRunning: boolean;
    config?: any;
  };
}

/**
 * 构建器工厂函数类型
 */
export type BuilderFactory<T extends BaseBuilderOptions = BaseBuilderOptions> = (
  options?: T
) => Builder;

/**
 * 插件配置验证结果
 */
export interface ValidationResult {
  /** 验证是否通过 */
  valid: boolean;
  /** 错误信息 */
  errors: string[];
  /** 警告信息 */
  warnings: string[];
}

/**
 * 构建器事件类型
 */
export type BuilderEvent = 
  | 'build:start'
  | 'build:end'
  | 'build:error'
  | 'serve:start'
  | 'serve:stop'
  | 'serve:error'
  | 'config:change'
  | 'file:change';

/**
 * 构建器事件监听器
 */
export type BuilderEventListener = (event: {
  type: BuilderEvent;
  data?: any;
  timestamp: number;
}) => void;
