/**
 * @fileoverview 应用检测工具函数
 * @description 提供应用检测、验证和处理的通用工具函数
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

/**
 * 应用类型枚举
 */
export enum AppType {
    REACT = 'react',
    VUE2 = 'vue2',
    VUE3 = 'vue3',
    ANGULAR = 'angular',
    SVELTE = 'svelte',
    SOLID = 'solid',
    HTML = 'html',
    UNKNOWN = 'unknown'
}

/**
 * 应用信息接口
 */
export interface AppInfo {
    type: AppType;
    name: string;
    version?: string;
    entry: any;
    container?: Element | string;
    props?: Record<string, any>;
    metadata?: Record<string, any>;
}

/**
 * 检查是否为有效的应用
 */
export function isApp(app: any): boolean {
    if (!app) {
        return false;
    }

    // 检查是否有必要的应用属性
    if (typeof app === 'object') {
        // 微前端应用通常有这些属性
        if (app.mount || app.unmount || app.bootstrap) {
            return true;
        }

        // 组件式应用
        if (app.component || app.render) {
            return true;
        }

        // 配置式应用
        if (app.name && (app.entry || app.url)) {
            return true;
        }
    }

    // 函数式应用
    if (typeof app === 'function') {
        return true;
    }

    return false;
}

/**
 * 检查是否为应用入口
 */
export function isEntry(entry: any): boolean {
    if (!entry) {
        return false;
    }

    // URL 入口
    if (typeof entry === 'string') {
        try {
            new URL(entry);
            return true;
        } catch {
            // 可能是相对路径
            return entry.includes('.js') || entry.includes('.html');
        }
    }

    // 组件入口
    if (typeof entry === 'function' || typeof entry === 'object') {
        return true;
    }

    return false;
}

/**
 * 获取应用类型
 */
export function getAppType(app: any): AppType {
    if (!app) {
        return AppType.UNKNOWN;
    }

    // 检查全局变量
    if (typeof window !== 'undefined') {
        // React 检测
        if (window.React || app.$$typeof) {
            return AppType.REACT;
        }

        // Vue 检测
        if (window.Vue) {
            const vue = window.Vue;
            if (vue.version && vue.version.startsWith('3')) {
                return AppType.VUE3;
            } else {
                return AppType.VUE2;
            }
        }

        // Angular 检测
        if (window.ng || app.ɵmod) {
            return AppType.ANGULAR;
        }
    }

    // 检查应用对象属性
    if (typeof app === 'object') {
        // Vue 3 应用
        if (app.mount && app.unmount && app.use) {
            return AppType.VUE3;
        }

        // Vue 2 应用
        if (app.$mount || app.$el) {
            return AppType.VUE2;
        }

        // React 应用
        if (app.render && app.setState) {
            return AppType.REACT;
        }

        // Angular 应用
        if (app.ɵmod || app.ɵinj) {
            return AppType.ANGULAR;
        }

        // Svelte 应用
        if (app.$set || app.$$render) {
            return AppType.SVELTE;
        }

        // Solid 应用
        if (app.$$component) {
            return AppType.SOLID;
        }

        // HTML 应用
        if (app.innerHTML !== undefined || app.tagName) {
            return AppType.HTML;
        }
    }

    return AppType.UNKNOWN;
}

/**
 * 提取应用信息
 */
export function extractAppInfo(app: any, options: {
    name?: string;
    container?: Element | string;
    props?: Record<string, any>;
} = {}): AppInfo {
    if (!isApp(app)) {
        throw new Error('提供的对象不是有效的应用');
    }

    const type = getAppType(app);
    const name = options.name || getAppName(app) || 'UnknownApp';

    return {
        type,
        name,
        version: getAppVersion(app),
        entry: app,
        container: options.container,
        props: options.props || {},
        metadata: extractAppMetadata(app)
    };
}

/**
 * 获取应用名称
 */
export function getAppName(app: any): string {
    if (!app) {
        return 'Unknown';
    }

    // 显式名称
    if (app.name) {
        return app.name;
    }

    // 显示名称
    if (app.displayName) {
        return app.displayName;
    }

    // 包名称
    if (app.packageName) {
        return app.packageName;
    }

    // 构造函数名称
    if (app.constructor && app.constructor.name) {
        return app.constructor.name;
    }

    return 'AnonymousApp';
}

/**
 * 获取应用版本
 */
export function getAppVersion(app: any): string | undefined {
    if (!app) {
        return undefined;
    }

    // 显式版本
    if (app.version) {
        return app.version;
    }

    // 包版本
    if (app.packageVersion) {
        return app.packageVersion;
    }

    // 从全局变量获取版本
    if (typeof window !== 'undefined') {
        const type = getAppType(app);
        switch (type) {
            case AppType.REACT:
                return window.React?.version;
            case AppType.VUE2:
            case AppType.VUE3:
                return window.Vue?.version;
            case AppType.ANGULAR:
                return window.ng?.version?.full;
        }
    }

    return undefined;
}

/**
 * 提取应用元数据
 */
export function extractAppMetadata(app: any): Record<string, any> {
    const metadata: Record<string, any> = {};

    if (!app) {
        return metadata;
    }

    // 基本信息
    metadata.type = getAppType(app);
    metadata.hasLifecycle = !!(app.mount || app.unmount || app.bootstrap);
    metadata.hasProps = !!(app.props || app.defaultProps);

    // 框架特定信息
    const type = getAppType(app);
    switch (type) {
        case AppType.REACT:
            metadata.isClassComponent = !!(app.prototype && app.prototype.render);
            metadata.isFunctionComponent = typeof app === 'function' && !metadata.isClassComponent;
            metadata.hasHooks = metadata.isFunctionComponent;
            break;

        case AppType.VUE2:
        case AppType.VUE3:
            metadata.hasTemplate = !!(app.template);
            metadata.hasRender = !!(app.render);
            metadata.hasSetup = !!(app.setup);
            metadata.isComposition = type === AppType.VUE3 && metadata.hasSetup;
            break;

        case AppType.ANGULAR:
            metadata.hasSelector = !!(app.ɵcmp && app.ɵcmp.selectors);
            metadata.hasInputs = !!(app.ɵcmp && app.ɵcmp.inputs);
            metadata.hasOutputs = !!(app.ɵcmp && app.ɵcmp.outputs);
            break;

        case AppType.SVELTE:
            metadata.hasSlots = !!(app.$$slots);
            metadata.hasProps = !!(app.$$props);
            break;

        case AppType.SOLID:
            metadata.isSignal = !!(app.$$signal);
            metadata.hasEffects = !!(app.$$effects);
            break;
    }

    return metadata;
}

/**
 * 验证应用配置
 */
export function validateAppConfig(config: any): {
    valid: boolean;
    errors: string[];
    warnings: string[];
} {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (!config) {
        errors.push('应用配置不能为空');
        return { valid: false, errors, warnings };
    }

    // 检查必需字段
    if (!config.name) {
        errors.push('应用名称不能为空');
    }

    if (!config.entry && !config.component) {
        errors.push('必须提供应用入口或组件');
    }

    // 检查入口有效性
    if (config.entry && !isEntry(config.entry)) {
        errors.push('无效的应用入口');
    }

    // 检查容器
    if (config.container) {
        if (typeof config.container === 'string') {
            if (typeof document !== 'undefined') {
                const element = document.querySelector(config.container);
                if (!element) {
                    warnings.push(`找不到容器元素: ${config.container}`);
                }
            }
        } else if (!(config.container instanceof Element)) {
            errors.push('容器必须是有效的DOM元素或选择器');
        }
    }

    // 检查属性
    if (config.props && typeof config.props !== 'object') {
        errors.push('应用属性必须是对象');
    }

    return {
        valid: errors.length === 0,
        errors,
        warnings
    };
}

/**
 * 创建应用包装器
 */
export function createAppWrapper(app: any, config: {
    name?: string;
    container?: Element | string;
    props?: Record<string, any>;
    beforeMount?: () => void;
    afterMount?: () => void;
    beforeUnmount?: () => void;
    afterUnmount?: () => void;
    onError?: (error: Error) => void;
} = {}) {
    const appInfo = extractAppInfo(app, config);

    return {
        ...appInfo,
        config,
        hooks: {
            beforeMount: config.beforeMount,
            afterMount: config.afterMount,
            beforeUnmount: config.beforeUnmount,
            afterUnmount: config.afterUnmount,
            onError: config.onError
        },
        mount: async function (container?: Element | string) {
            try {
                if (this.hooks.beforeMount) {
                    this.hooks.beforeMount();
                }

                const targetContainer = container || this.container;
                if (!targetContainer) {
                    throw new Error('未指定挂载容器');
                }

                // 这里需要根据不同框架实现具体的挂载逻辑
                // 由各个适配器实现具体的挂载方法

                if (this.hooks.afterMount) {
                    this.hooks.afterMount();
                }
            } catch (error) {
                if (this.hooks.onError) {
                    this.hooks.onError(error as Error);
                } else {
                    throw error;
                }
            }
        },
        unmount: async function () {
            try {
                if (this.hooks.beforeUnmount) {
                    this.hooks.beforeUnmount();
                }

                // 这里需要根据不同框架实现具体的卸载逻辑
                // 由各个适配器实现具体的卸载方法

                if (this.hooks.afterUnmount) {
                    this.hooks.afterUnmount();
                }
            } catch (error) {
                if (this.hooks.onError) {
                    this.hooks.onError(error as Error);
                } else {
                    throw error;
                }
            }
        }
    };
}