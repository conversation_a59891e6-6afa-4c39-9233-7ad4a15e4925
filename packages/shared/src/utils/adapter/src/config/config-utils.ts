/**
 * @fileoverview 配置管理工具函数
 * @description 提供配置验证、合并和处理的通用工具函数
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

/**
 * 基础配置接口
 */
export interface BaseConfig {
    name: string;
    version?: string;
    enabled?: boolean;
    [key: string]: any;
}

/**
 * 应用配置接口
 */
export interface AppConfig extends BaseConfig {
    entry: string | any;
    container?: string | Element;
    props?: Record<string, any>;
    activeWhen?: string | ((location: Location) => boolean);
    customProps?: Record<string, any>;
    loader?: {
        fetch?: typeof fetch;
        getPublicPath?: () => string;
    };
}

/**
 * 适配器配置接口
 */
export interface AdapterConfig extends BaseConfig {
    framework: string;
    frameworkVersion?: string;
    mountParcel?: boolean;
    domElementGetter?: () => Element;
    errorBoundary?: (error: Error, errorInfo: any) => void;
}

/**
 * 配置验证结果
 */
export interface ConfigValidationResult {
    valid: boolean;
    errors: string[];
    warnings: string[];
    config?: any;
}

/**
 * 验证基础配置
 */
export function validateBaseConfig(config: any): ConfigValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (!config) {
        errors.push('配置不能为空');
        return { valid: false, errors, warnings };
    }

    if (typeof config !== 'object') {
        errors.push('配置必须是对象');
        return { valid: false, errors, warnings };
    }

    // 检查必需字段
    if (!config.name) {
        errors.push('配置名称不能为空');
    } else if (typeof config.name !== 'string') {
        errors.push('配置名称必须是字符串');
    }

    // 检查可选字段
    if (config.version && typeof config.version !== 'string') {
        warnings.push('版本号应该是字符串');
    }

    if (config.enabled !== undefined && typeof config.enabled !== 'boolean') {
        warnings.push('enabled 字段应该是布尔值');
    }

    return {
        valid: errors.length === 0,
        errors,
        warnings,
        config
    };
}

/**
 * 验证应用配置
 */
export function validateAppConfig(config: any): ConfigValidationResult {
    const baseResult = validateBaseConfig(config);
    if (!baseResult.valid) {
        return baseResult;
    }

    const errors = [...baseResult.errors];
    const warnings = [...baseResult.warnings];

    // 检查应用特定字段
    if (!config.entry) {
        errors.push('应用入口不能为空');
    }

    // 检查容器
    if (config.container) {
        if (typeof config.container === 'string') {
            if (typeof document !== 'undefined') {
                const element = document.querySelector(config.container);
                if (!element) {
                    warnings.push(`找不到容器元素: ${config.container}`);
                }
            }
        } else if (!(config.container instanceof Element)) {
            errors.push('容器必须是有效的DOM元素或选择器');
        }
    }

    // 检查属性
    if (config.props && typeof config.props !== 'object') {
        errors.push('应用属性必须是对象');
    }

    // 检查激活条件
    if (config.activeWhen) {
        if (typeof config.activeWhen !== 'string' && typeof config.activeWhen !== 'function') {
            errors.push('activeWhen 必须是字符串或函数');
        }
    }

    return {
        valid: errors.length === 0,
        errors,
        warnings,
        config
    };
}

/**
 * 验证适配器配置
 */
export function validateAdapterConfig(config: any): ConfigValidationResult {
    const baseResult = validateBaseConfig(config);
    if (!baseResult.valid) {
        return baseResult;
    }

    const errors = [...baseResult.errors];
    const warnings = [...baseResult.warnings];

    // 检查框架字段
    if (!config.framework) {
        errors.push('框架名称不能为空');
    } else if (typeof config.framework !== 'string') {
        errors.push('框架名称必须是字符串');
    }

    // 检查框架版本
    if (config.frameworkVersion && typeof config.frameworkVersion !== 'string') {
        warnings.push('框架版本应该是字符串');
    }

    // 检查DOM元素获取器
    if (config.domElementGetter && typeof config.domElementGetter !== 'function') {
        errors.push('domElementGetter 必须是函数');
    }

    // 检查错误边界
    if (config.errorBoundary && typeof config.errorBoundary !== 'function') {
        errors.push('errorBoundary 必须是函数');
    }

    return {
        valid: errors.length === 0,
        errors,
        warnings,
        config
    };
}

/**
 * 创建默认配置
 */
export function createDefaultConfig<T extends BaseConfig>(
    type: 'app' | 'adapter',
    overrides: Partial<T> = {}
): T {
    const baseDefaults = {
        enabled: true,
        version: '1.0.0'
    };

    let typeDefaults: any = {};

    switch (type) {
        case 'app':
            typeDefaults = {
                props: {},
                customProps: {},
                activeWhen: () => true
            };
            break;

        case 'adapter':
            typeDefaults = {
                mountParcel: false,
                domElementGetter: () => document.body
            };
            break;
    }

    return {
        ...baseDefaults,
        ...typeDefaults,
        ...overrides
    } as T;
}

/**
 * 合并配置
 */
export function mergeConfigs<T extends BaseConfig>(
    baseConfig: T,
    ...overrideConfigs: Partial<T>[]
): T {
    let merged = { ...baseConfig };

    for (const override of overrideConfigs) {
        merged = deepMerge(merged, override);
    }

    return merged;
}

/**
 * 深度合并对象
 */
function deepMerge<T>(target: T, source: Partial<T>): T {
    const result = { ...target };

    for (const key in source) {
        if (source.hasOwnProperty(key)) {
            const sourceValue = source[key];
            const targetValue = result[key];

            if (isObject(sourceValue) && isObject(targetValue)) {
                result[key] = deepMerge(targetValue, sourceValue);
            } else {
                result[key] = sourceValue as any;
            }
        }
    }

    return result;
}

/**
 * 检查是否为对象
 */
function isObject(value: any): value is Record<string, any> {
    return value !== null && typeof value === 'object' && !Array.isArray(value);
}

/**
 * 标准化配置
 */
export function normalizeConfig<T extends BaseConfig>(config: T): T {
    const normalized = { ...config };

    // 标准化名称
    if (normalized.name) {
        normalized.name = normalized.name.trim();
    }

    // 标准化版本
    if (normalized.version) {
        normalized.version = normalized.version.trim().replace(/^v/, '');
    }

    // 标准化启用状态
    if (normalized.enabled === undefined) {
        normalized.enabled = true;
    }

    return normalized;
}

/**
 * 配置转换器
 */
export function transformConfig<T extends BaseConfig, U extends BaseConfig>(
    config: T,
    transformer: (config: T) => U
): U {
    try {
        return transformer(config);
    } catch (error) {
        throw new Error(`配置转换失败: ${error instanceof Error ? error.message : String(error)}`);
    }
}

/**
 * 配置序列化
 */
export function serializeConfig(config: BaseConfig): string {
    try {
        return JSON.stringify(config, null, 2);
    } catch (error) {
        throw new Error(`配置序列化失败: ${error instanceof Error ? error.message : String(error)}`);
    }
}

/**
 * 配置反序列化
 */
export function deserializeConfig<T extends BaseConfig>(configString: string): T {
    try {
        const config = JSON.parse(configString);
        return normalizeConfig(config);
    } catch (error) {
        throw new Error(`配置反序列化失败: ${error instanceof Error ? error.message : String(error)}`);
    }
}

/**
 * 配置比较
 */
export function compareConfigs<T extends BaseConfig>(config1: T, config2: T): {
    equal: boolean;
    differences: string[];
} {
    const differences: string[] = [];

    // 比较基本字段
    const keys = new Set([...Object.keys(config1), ...Object.keys(config2)]);

    keys.forEach(key => {
        const value1 = (config1 as any)[key];
        const value2 = (config2 as any)[key];

        if (!deepEqual(value1, value2)) {
            differences.push(`${key}: ${JSON.stringify(value1)} !== ${JSON.stringify(value2)}`);
        }
    });

    return {
        equal: differences.length === 0,
        differences
    };
}

/**
 * 深度比较
 */
function deepEqual(a: any, b: any): boolean {
    if (a === b) {
        return true;
    }

    if (a == null || b == null) {
        return a === b;
    }

    if (typeof a !== typeof b) {
        return false;
    }

    if (typeof a !== 'object') {
        return a === b;
    }

    if (Array.isArray(a) !== Array.isArray(b)) {
        return false;
    }

    const keysA = Object.keys(a);
    const keysB = Object.keys(b);

    if (keysA.length !== keysB.length) {
        return false;
    }

    for (const key of keysA) {
        if (!keysB.includes(key)) {
            return false;
        }

        if (!deepEqual(a[key], b[key])) {
            return false;
        }
    }

    return true;
}

/**
 * 创建配置管理器
 */
export function createConfigManager<T extends BaseConfig>(initialConfig?: T) {
    let config = initialConfig ? normalizeConfig(initialConfig) : null;

    return {
        getConfig(): T | null {
            return config;
        },

        setConfig(newConfig: T): void {
            const validation = validateBaseConfig(newConfig);
            if (!validation.valid) {
                throw new Error(`配置验证失败: ${validation.errors.join(', ')}`);
            }
            config = normalizeConfig(newConfig);
        },

        updateConfig(updates: Partial<T>): void {
            if (!config) {
                throw new Error('未设置初始配置');
            }
            config = mergeConfigs(config, updates);
        },

        validateConfig(): ConfigValidationResult {
            if (!config) {
                return {
                    valid: false,
                    errors: ['未设置配置'],
                    warnings: []
                };
            }
            return validateBaseConfig(config);
        },

        resetConfig(): void {
            config = null;
        },

        cloneConfig(): T | null {
            return config ? { ...config } : null;
        },

        serializeConfig(): string {
            if (!config) {
                throw new Error('未设置配置');
            }
            return serializeConfig(config);
        },

        loadConfig(configString: string): void {
            const newConfig = deserializeConfig<T>(configString);
            this.setConfig(newConfig);
        }
    };
}