/**
 * @fileoverview 版本管理工具函数
 * @description 提供版本检测、比较和兼容性检查的通用工具函数
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

/**
 * 版本信息接口
 */
export interface VersionInfo {
    major: number;
    minor: number;
    patch: number;
    prerelease?: string;
    build?: string;
    raw: string;
}

/**
 * 版本比较结果
 */
export enum VersionComparison {
    LESS = -1,
    EQUAL = 0,
    GREATER = 1
}

/**
 * 解析版本字符串
 */
export function parseVersion(version: string): VersionInfo {
    if (!version || typeof version !== 'string') {
        throw new Error('版本字符串不能为空');
    }

    // 清理版本字符串
    const cleanVersion = version.trim().replace(/^v/, '');

    // 匹配语义化版本格式
    const versionRegex = /^(\d+)\.(\d+)\.(\d+)(?:-([0-9A-Za-z-]+(?:\.[0-9A-Za-z-]+)*))?(?:\+([0-9A-Za-z-]+(?:\.[0-9A-Za-z-]+)*))?$/;
    const match = cleanVersion.match(versionRegex);

    if (!match) {
        throw new Error(`无效的版本格式: ${version}`);
    }

    return {
        major: parseInt(match[1], 10),
        minor: parseInt(match[2], 10),
        patch: parseInt(match[3], 10),
        prerelease: match[4],
        build: match[5],
        raw: version
    };
}

/**
 * 比较两个版本
 */
export function compareVersions(version1: string, version2: string): VersionComparison {
    const v1 = parseVersion(version1);
    const v2 = parseVersion(version2);

    // 比较主版本号
    if (v1.major !== v2.major) {
        return v1.major > v2.major ? VersionComparison.GREATER : VersionComparison.LESS;
    }

    // 比较次版本号
    if (v1.minor !== v2.minor) {
        return v1.minor > v2.minor ? VersionComparison.GREATER : VersionComparison.LESS;
    }

    // 比较修订版本号
    if (v1.patch !== v2.patch) {
        return v1.patch > v2.patch ? VersionComparison.GREATER : VersionComparison.LESS;
    }

    // 比较预发布版本
    if (v1.prerelease && v2.prerelease) {
        return comparePrerelease(v1.prerelease, v2.prerelease);
    } else if (v1.prerelease && !v2.prerelease) {
        return VersionComparison.LESS; // 预发布版本小于正式版本
    } else if (!v1.prerelease && v2.prerelease) {
        return VersionComparison.GREATER; // 正式版本大于预发布版本
    }

    return VersionComparison.EQUAL;
}

/**
 * 比较预发布版本
 */
function comparePrerelease(pre1: string, pre2: string): VersionComparison {
    const parts1 = pre1.split('.');
    const parts2 = pre2.split('.');
    const maxLength = Math.max(parts1.length, parts2.length);

    for (let i = 0; i < maxLength; i++) {
        const part1 = parts1[i];
        const part2 = parts2[i];

        if (part1 === undefined) {
            return VersionComparison.LESS;
        }
        if (part2 === undefined) {
            return VersionComparison.GREATER;
        }

        // 数字比较
        const num1 = parseInt(part1, 10);
        const num2 = parseInt(part2, 10);

        if (!isNaN(num1) && !isNaN(num2)) {
            if (num1 !== num2) {
                return num1 > num2 ? VersionComparison.GREATER : VersionComparison.LESS;
            }
        } else {
            // 字符串比较
            if (part1 !== part2) {
                return part1 > part2 ? VersionComparison.GREATER : VersionComparison.LESS;
            }
        }
    }

    return VersionComparison.EQUAL;
}

/**
 * 检查版本是否兼容
 */
export function isVersionCompatible(
    currentVersion: string,
    requiredVersion: string,
    compatibilityMode: 'exact' | 'major' | 'minor' | 'patch' = 'minor'
): boolean {
    try {
        const current = parseVersion(currentVersion);
        const required = parseVersion(requiredVersion);

        switch (compatibilityMode) {
            case 'exact':
                return compareVersions(currentVersion, requiredVersion) === VersionComparison.EQUAL;

            case 'major':
                return current.major === required.major &&
                    compareVersions(currentVersion, requiredVersion) >= VersionComparison.EQUAL;

            case 'minor':
                return current.major === required.major &&
                    current.minor === required.minor &&
                    compareVersions(currentVersion, requiredVersion) >= VersionComparison.EQUAL;

            case 'patch':
                return compareVersions(currentVersion, requiredVersion) >= VersionComparison.EQUAL;

            default:
                return false;
        }
    } catch {
        return false;
    }
}

/**
 * 获取框架版本
 */
export function getFrameworkVersion(framework: string): string | undefined {
    if (typeof window === 'undefined') {
        return undefined;
    }

    const frameworkName = framework.toLowerCase();

    switch (frameworkName) {
        case 'react':
            return (window as any).React?.version;

        case 'vue':
        case 'vue2':
        case 'vue3':
            return (window as any).Vue?.version;

        case 'angular':
            return (window as any).ng?.version?.full;

        case 'svelte':
            // Svelte 通常不在全局暴露版本信息
            return undefined;

        case 'solid':
            return (window as any).Solid?.version;

        default:
            // 尝试从全局对象获取
            const globalObj = (window as any)[framework];
            return globalObj?.version || globalObj?.VERSION;
    }
}

/**
 * 检查框架版本兼容性
 */
export function checkFrameworkCompatibility(
    framework: string,
    requiredVersion: string,
    compatibilityMode: 'exact' | 'major' | 'minor' | 'patch' = 'major'
): {
    compatible: boolean;
    currentVersion?: string;
    requiredVersion: string;
    message: string;
} {
    const currentVersion = getFrameworkVersion(framework);

    if (!currentVersion) {
        return {
            compatible: false,
            requiredVersion,
            message: `无法检测到 ${framework} 的版本信息`
        };
    }

    const compatible = isVersionCompatible(currentVersion, requiredVersion, compatibilityMode);

    return {
        compatible,
        currentVersion,
        requiredVersion,
        message: compatible
            ? `${framework} 版本兼容 (当前: ${currentVersion}, 要求: ${requiredVersion})`
            : `${framework} 版本不兼容 (当前: ${currentVersion}, 要求: ${requiredVersion})`
    };
}

/**
 * 获取版本范围内的最新版本
 */
export function getLatestVersionInRange(
    versions: string[],
    range: string
): string | undefined {
    if (!versions || versions.length === 0) {
        return undefined;
    }

    try {
        const rangeVersion = parseVersion(range);
        const compatibleVersions = versions.filter(version => {
            try {
                return isVersionCompatible(version, range, 'minor');
            } catch {
                return false;
            }
        });

        if (compatibleVersions.length === 0) {
            return undefined;
        }

        // 排序并返回最新版本
        return compatibleVersions.sort((a, b) => {
            return compareVersions(b, a); // 降序排列
        })[0];
    } catch {
        return undefined;
    }
}

/**
 * 验证版本字符串格式
 */
export function isValidVersion(version: string): boolean {
    try {
        parseVersion(version);
        return true;
    } catch {
        return false;
    }
}

/**
 * 格式化版本信息
 */
export function formatVersion(version: VersionInfo): string {
    let formatted = `${version.major}.${version.minor}.${version.patch}`;

    if (version.prerelease) {
        formatted += `-${version.prerelease}`;
    }

    if (version.build) {
        formatted += `+${version.build}`;
    }

    return formatted;
}

/**
 * 获取版本的下一个版本号
 */
export function getNextVersion(
    currentVersion: string,
    type: 'major' | 'minor' | 'patch' = 'patch'
): string {
    const version = parseVersion(currentVersion);

    switch (type) {
        case 'major':
            return `${version.major + 1}.0.0`;
        case 'minor':
            return `${version.major}.${version.minor + 1}.0`;
        case 'patch':
            return `${version.major}.${version.minor}.${version.patch + 1}`;
        default:
            throw new Error(`无效的版本类型: ${type}`);
    }
}

/**
 * 创建版本管理器
 */
export function createVersionManager(options: {
    framework?: string;
    requiredVersion?: string;
    compatibilityMode?: 'exact' | 'major' | 'minor' | 'patch';
    onIncompatible?: (info: any) => void;
} = {}) {
    return {
        framework: options.framework,
        requiredVersion: options.requiredVersion,
        compatibilityMode: options.compatibilityMode || 'major',

        checkCompatibility(): boolean {
            if (!this.framework || !this.requiredVersion) {
                return true; // 没有要求则认为兼容
            }

            const result = checkFrameworkCompatibility(
                this.framework,
                this.requiredVersion,
                this.compatibilityMode
            );

            if (!result.compatible && options.onIncompatible) {
                options.onIncompatible(result);
            }

            return result.compatible;
        },

        getCurrentVersion(): string | undefined {
            return this.framework ? getFrameworkVersion(this.framework) : undefined;
        },

        isVersionSupported(version: string): boolean {
            if (!this.requiredVersion) {
                return true;
            }

            return isVersionCompatible(version, this.requiredVersion, this.compatibilityMode);
        },

        compareWith(otherVersion: string): VersionComparison {
            const currentVersion = this.getCurrentVersion();
            if (!currentVersion) {
                throw new Error('无法获取当前版本');
            }

            return compareVersions(currentVersion, otherVersion);
        }
    };
}