/**
 * @fileoverview 组件相关工具函数
 * @description 提供组件检测、提取和处理的通用工具函数
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

/**
 * 检查是否为有效的组件
 */
export function isComponent(component: any): boolean {
    if (!component) {
        return false;
    }

    // React 组件检测
    if (typeof component === 'function') {
        // 函数组件或类组件
        return true;
    }

    // Vue 组件检测
    if (typeof component === 'object') {
        // Vue 2/3 组件对象
        if (component.render || component.template || component.setup) {
            return true;
        }
        // Vue SFC
        if (component.__file || component._compiled) {
            return true;
        }
    }

    // Angular 组件检测
    if (component.ɵcmp || component.ɵfac) {
        return true;
    }

    // Svelte 组件检测
    if (component.$$render || component.$set) {
        return true;
    }

    // Solid 组件检测
    if (component.$$component || typeof component === 'function') {
        return true;
    }

    return false;
}

/**
 * 提取组件信息
 */
export function extractComponent(component: any): {
    name: string;
    type: string;
    props?: string[];
    methods?: string[];
} {
    if (!isComponent(component)) {
        throw new Error('提供的对象不是有效的组件');
    }

    const info = {
        name: getComponentName(component),
        type: getComponentType(component),
        props: getComponentProps(component),
        methods: getComponentMethods(component)
    };

    return info;
}

/**
 * 获取组件名称
 */
export function getComponentName(component: any): string {
    if (!component) {
        return 'Unknown';
    }

    // 函数/类名称
    if (component.name) {
        return component.name;
    }

    // Vue 组件名称
    if (component.__name || component.name) {
        return component.__name || component.name;
    }

    // 显式设置的名称
    if (component.displayName) {
        return component.displayName;
    }

    // Angular 组件
    if (component.ɵcmp && component.ɵcmp.selectors) {
        return component.ɵcmp.selectors[0][0] || 'AngularComponent';
    }

    // 默认名称
    return 'AnonymousComponent';
}

/**
 * 获取组件类型
 */
export function getComponentType(component: any): string {
    if (!component) {
        return 'unknown';
    }

    // React 检测
    if (typeof component === 'function') {
        // 检查是否为 React 类组件
        if (component.prototype && component.prototype.render) {
            return 'react-class';
        }
        // React 函数组件
        return 'react-function';
    }

    // Vue 检测
    if (typeof component === 'object') {
        if (component.setup) {
            return 'vue3-composition';
        }
        if (component.render || component.template) {
            return 'vue2-options';
        }
        if (component.__file) {
            return 'vue-sfc';
        }
    }

    // Angular 检测
    if (component.ɵcmp) {
        return 'angular';
    }

    // Svelte 检测
    if (component.$$render) {
        return 'svelte';
    }

    // Solid 检测
    if (component.$$component) {
        return 'solid';
    }

    return 'unknown';
}

/**
 * 获取组件属性
 */
export function getComponentProps(component: any): string[] {
    if (!component) {
        return [];
    }

    const props: string[] = [];

    // React 类组件
    if (component.propTypes) {
        props.push(...Object.keys(component.propTypes));
    }

    // Vue 组件
    if (component.props) {
        if (Array.isArray(component.props)) {
            props.push(...component.props);
        } else if (typeof component.props === 'object') {
            props.push(...Object.keys(component.props));
        }
    }

    // Angular 组件
    if (component.ɵcmp && component.ɵcmp.inputs) {
        props.push(...Object.keys(component.ɵcmp.inputs));
    }

    return props;
}

/**
 * 获取组件方法
 */
export function getComponentMethods(component: any): string[] {
    if (!component) {
        return [];
    }

    const methods: string[] = [];

    // 获取原型方法
    if (component.prototype) {
        const protoMethods = Object.getOwnPropertyNames(component.prototype)
            .filter(name =>
                name !== 'constructor' &&
                typeof component.prototype[name] === 'function'
            );
        methods.push(...protoMethods);
    }

    // Vue 组件方法
    if (typeof component === 'object' && component.methods) {
        methods.push(...Object.keys(component.methods));
    }

    return methods;
}

/**
 * 验证组件属性
 */
export function validateComponentProps(component: any, props: Record<string, any>): {
    valid: boolean;
    errors: string[];
} {
    const errors: string[] = [];

    if (!isComponent(component)) {
        errors.push('无效的组件');
        return { valid: false, errors };
    }

    const componentProps = getComponentProps(component);

    // 检查必需属性
    if (component.propTypes) {
        Object.entries(component.propTypes).forEach(([propName, propType]: [string, any]) => {
            if (propType.isRequired && !(propName in props)) {
                errors.push(`缺少必需属性: ${propName}`);
            }
        });
    }

    // 检查未知属性
    Object.keys(props).forEach(propName => {
        if (componentProps.length > 0 && !componentProps.includes(propName)) {
            errors.push(`未知属性: ${propName}`);
        }
    });

    return {
        valid: errors.length === 0,
        errors
    };
}

/**
 * 创建组件包装器
 */
export function createComponentWrapper(component: any, options: {
    name?: string;
    props?: Record<string, any>;
    beforeMount?: () => void;
    afterMount?: () => void;
    beforeUnmount?: () => void;
    afterUnmount?: () => void;
} = {}) {
    if (!isComponent(component)) {
        throw new Error('无效的组件');
    }

    return {
        component,
        name: options.name || getComponentName(component),
        type: getComponentType(component),
        props: options.props || {},
        hooks: {
            beforeMount: options.beforeMount,
            afterMount: options.afterMount,
            beforeUnmount: options.beforeUnmount,
            afterUnmount: options.afterUnmount
        },
        mount: function (container: Element) {
            if (this.hooks.beforeMount) {
                this.hooks.beforeMount();
            }

            // 这里需要根据不同框架实现具体的挂载逻辑
            // 由各个适配器实现具体的挂载方法

            if (this.hooks.afterMount) {
                this.hooks.afterMount();
            }
        },
        unmount: function () {
            if (this.hooks.beforeUnmount) {
                this.hooks.beforeUnmount();
            }

            // 这里需要根据不同框架实现具体的卸载逻辑
            // 由各个适配器实现具体的卸载方法

            if (this.hooks.afterUnmount) {
                this.hooks.afterUnmount();
            }
        }
    };
}