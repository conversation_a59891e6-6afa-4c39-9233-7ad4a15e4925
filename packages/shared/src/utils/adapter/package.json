{"name": "@micro-core/shared-adapter-utils", "version": "1.0.0", "description": "共享适配器工具库 - 解决适配器间的代码重复问题", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"build": "vite build", "dev": "vite build --watch", "type-check": "tsc --noEmit"}, "keywords": ["micro-core", "adapter", "utils", "shared"], "author": "Echo <<EMAIL>>", "license": "MIT", "dependencies": {"@micro-core/shared-types": "workspace:*"}, "devDependencies": {"typescript": "^5.7.2", "vite": "^7.0.6"}, "exports": {".": {"import": "./dist/index.esm.js", "require": "./dist/index.js", "types": "./dist/index.d.ts"}}}