/**
 * @fileoverview 插件相关类型定义
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

import type { AppInstance } from './app';


/**
 * 资源类型
 */
export type ResourceType = 'script' | 'style' | 'html' | 'json' | 'text';

/**
 * 插件钩子映射
 */
export interface PluginHooks {
    // 内核钩子
    beforeStart: (kernel: unknown) => void | Promise<void>;
    afterStart: (kernel: unknown) => void | Promise<void>;
    beforeStop: (kernel: unknown) => void | Promise<void>;
    afterStop: (kernel: unknown) => void | Promise<void>;
    beforeDestroy: (kernel: unknown) => void | Promise<void>;

    // 应用钩子
    beforeRegister: (config: AppConfig) => AppConfig | Promise<AppConfig>;
    afterRegister: (app: AppInstance) => void | Promise<void>;
    beforeUnregister: (app: AppInstance) => void | Promise<void>;
    afterUnregister: (name: string) => void | Promise<void>;

    // 生命周期钩子
    beforeBootstrap: (app: AppInstance) => void | Promise<void>;
    afterBootstrap: (app: AppInstance) => void | Promise<void>;
    beforeMount: (app: AppInstance) => void | Promise<void>;
    afterMount: (app: AppInstance) => void | Promise<void>;
    beforeUnmount: (app: AppInstance) => void | Promise<void>;
    afterUnmount: (app: AppInstance) => void | Promise<void>;
    beforeUpdate: (app: AppInstance, props?: Record<string, unknown>) => void | Promise<void>;
    afterUpdate: (app: AppInstance, props?: Record<string, unknown>) => void | Promise<void>;

    // 资源钩子
    beforeLoadResource: (url: string, type: ResourceType) => string | Promise<string>;
    afterLoadResource: (url: string, type: ResourceType, content: string) => string | Promise<string>;

    // 路由钩子
    beforeRouteChange: (from: string, to: string) => boolean | Promise<boolean>;
    afterRouteChange: (from: string, to: string) => void | Promise<void>;
}

/**
 * 钩子回调函数
 */
export interface HookCallback<T = unknown, R = unknown> {
    (...args: T[]): R | Promise<R>;
}

/**
 * 插件接口
 */
export interface Plugin {
    /** 插件名称 */
    name: string;
    /** 插件版本 */
    version: string;
    /** 插件描述 */
    description?: string;
    /** 插件依赖 */
    dependencies?: string[];
    /** 安装插件 */
    install: (kernel: unknown) => void | Promise<void>;
    /** 卸载插件 */
    uninstall?: (kernel: unknown) => void | Promise<void>;
    /** 插件钩子 */
    hooks?: Partial<PluginHooks>;
}