/**
 * @fileoverview 应用相关类型定义
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

import type { AppStatusType, SandboxTypeType } from '../constants';
import type { ActiveWhen, LifecycleFn } from './common';

/**
 * 应用属性值类型
 */
export type AppPropValue = string | number | boolean | object | null | undefined;

/**
 * 应用属性映射
 */
export type AppProps = Record<string, AppPropValue>;

/**
 * 应用实例类型
 */
export interface AppInstanceType {
    /** 挂载方法 */
    mount?: (props?: AppProps) => void | Promise<void>;
    /** 卸载方法 */
    unmount?: () => void | Promise<void>;
    /** 更新方法 */
    update?: (props?: AppProps) => void | Promise<void>;
    /** 其他属性 */
    [key: string]: unknown;
}

/**
 * 沙箱实例类型
 */
export interface SandboxInstanceType {
    /** 激活沙箱 */
    active: () => void;
    /** 停用沙箱 */
    inactive: () => void;
    /** 销毁沙箱 */
    destroy: () => void;
    /** 获取沙箱状态 */
    isActive: () => boolean;
    /** 其他属性 */
    [key: string]: unknown;
}

/**
 * 应用配置
 */
export interface AppConfig {
    /** 应用名称 */
    name: string;
    /** 应用入口 */
    entry: string | AppEntry;
    /** 容器选择器或DOM元素 */
    container: string | HTMLElement;
    /** 激活条件 */
    activeWhen?: ActiveWhen;
    /** 自定义属性 */
    props?: AppProps;
    /** 沙箱配置 */
    sandbox?: SandboxConfig;
    /** 加载器配置 */
    loader?: LoaderConfig;
    /** 生命周期钩子 */
    lifecycle?: LifecycleHooks;
}

/**
 * 应用入口配置
 */
export interface AppEntry {
    /** 脚本列表 */
    scripts?: string[];
    /** 样式列表 */
    styles?: string[];
    /** HTML 模板 */
    html?: string;
}

/**
 * 应用信息
 */
export interface AppInfo {
    /** 应用名称 */
    name: string;
    /** 应用入口 */
    entry: string | AppEntry;
    /** 容器 */
    container: string | HTMLElement;
    /** 激活条件 */
    activeWhen?: ActiveWhen;
    /** 自定义属性 */
    props: AppProps;
    /** 沙箱配置 */
    sandbox?: SandboxConfig;
    /** 加载器配置 */
    loader?: LoaderConfig;
    /** 生命周期钩子 */
    lifecycle?: LifecycleHooks;
    /** 应用状态 */
    status: AppStatusType;
    /** 加载时间 */
    loadTime: number;
    /** 挂载时间 */
    mountTime: number;
    /** 卸载时间 */
    unmountTime: number;
    /** 错误信息 */
    error: Error | null;
    /** 应用实例 */
    instance: AppInstanceType | null;
    /** 沙箱实例 */
    sandboxInstance: SandboxInstanceType | null;
    /** 资源列表 */
    resources: ResourceInfo[];
    /** 创建时间 */
    createdAt: number;
    /** 更新时间 */
    updatedAt: number;
}

/**
 * 沙箱配置
 */
export interface SandboxConfig {
    /** 沙箱类型 */
    type?: SandboxTypeType;
    /** 是否启用样式隔离 */
    styleIsolation?: boolean;
    /** 是否启用脚本隔离 */
    scriptIsolation?: boolean;
    /** 白名单 */
    allowList?: string[];
    /** 黑名单 */
    denyList?: string[];
    /** 自定义配置 */
    options?: Record<string, unknown>;
}

/**
 * 加载器配置
 */
export interface LoaderConfig {
    /** 超时时间 */
    timeout?: number;
    /** 重试次数 */
    retries?: number;
    /** 是否启用缓存 */
    cache?: boolean;
    /** 预加载策略 */
    prefetch?: boolean;
    /** 自定义加载器 */
    customLoader?: (entry: string | AppEntry) => Promise<AppInstanceType>;
}

/**
 * 生命周期钩子
 */
export interface LifecycleHooks {
    /** 启动前 */
    beforeBootstrap?: LifecycleFn;
    /** 启动 */
    bootstrap?: LifecycleFn;
    /** 启动后 */
    afterBootstrap?: LifecycleFn;
    /** 挂载前 */
    beforeMount?: LifecycleFn;
    /** 挂载 */
    mount?: LifecycleFn;
    /** 挂载后 */
    afterMount?: LifecycleFn;
    /** 卸载前 */
    beforeUnmount?: LifecycleFn;
    /** 卸载 */
    unmount?: LifecycleFn;
    /** 卸载后 */
    afterUnmount?: LifecycleFn;
    /** 更新前 */
    beforeUpdate?: LifecycleFn;
    /** 更新 */
    update?: LifecycleFn;
    /** 更新后 */
    afterUpdate?: LifecycleFn;
}

/**
 * 资源信息
 */
export interface ResourceInfo {
    /** 资源URL */
    url: string | null;
    /** 资源类型 */
    type: 'script' | 'style' | 'html' | 'image' | 'font' | 'other';
    /** 资源状态 */
    status: 'loading' | 'loaded' | 'error';
    /** 资源大小 */
    size?: number;
    /** 加载时间 */
    loadTime?: number;
    /** 错误信息 */
    error?: Error;
    /** 资源内容 */
    content?: string | null;
    /** 是否为内联资源 */
    inline?: boolean;
}

/**
 * 应用实例（向后兼容）
 */
export interface AppInstance extends AppInfo { }

/**
 * 应用属性（向后兼容）
 */
export interface AppPropsCompat extends AppProps { }

/**
 * 应用生命周期（向后兼容）
 */
export interface AppLifecycles extends LifecycleHooks { }

/**
 * 微应用配置（向后兼容）
 */
export interface MicroAppConfig extends AppConfig { }

/**
 * 已加载的应用
 */
export interface LoadedApp extends AppInfo {
    /** 加载状态 */
    loaded: boolean;
    /** 挂载状态 */
    mounted: boolean;
}