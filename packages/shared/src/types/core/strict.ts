/**
 * @fileoverview 严格类型定义
 * @description 提供更严格的类型定义，消除any类型的使用
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

/**
 * 严格的JSON值类型
 */
export type StrictJsonValue = 
    | string 
    | number 
    | boolean 
    | null 
    | StrictJsonObject 
    | StrictJsonArray;

/**
 * 严格的JSON对象类型
 */
export interface StrictJsonObject {
    [key: string]: StrictJsonValue;
}

/**
 * 严格的JSON数组类型
 */
export interface StrictJsonArray extends Array<StrictJsonValue> {}

/**
 * 严格的配置对象类型
 */
export interface StrictConfig {
    [key: string]: StrictJsonValue;
}

/**
 * 严格的事件数据类型
 */
export interface StrictEventData {
    /** 事件类型 */
    type: string;
    /** 事件时间戳 */
    timestamp: number;
    /** 事件来源 */
    source?: string;
    /** 事件数据 */
    data?: StrictJsonValue;
    /** 事件元数据 */
    meta?: StrictJsonObject;
}

/**
 * 严格的错误信息类型
 */
export interface StrictErrorInfo {
    /** 错误代码 */
    code: string;
    /** 错误消息 */
    message: string;
    /** 错误详情 */
    details?: StrictJsonObject;
    /** 错误堆栈 */
    stack?: string;
    /** 错误时间戳 */
    timestamp: number;
}

/**
 * 严格的性能指标类型
 */
export interface StrictPerformanceMetrics {
    /** 开始时间 */
    startTime: number;
    /** 结束时间 */
    endTime: number;
    /** 持续时间 */
    duration: number;
    /** 内存使用 */
    memoryUsage?: {
        used: number;
        total: number;
        percentage: number;
    };
    /** 自定义指标 */
    customMetrics?: StrictJsonObject;
}

/**
 * 严格的生命周期状态类型
 */
export type StrictLifecycleState = 
    | 'NOT_LOADED'
    | 'LOADING_SOURCE_CODE'
    | 'NOT_BOOTSTRAPPED'
    | 'BOOTSTRAPPING'
    | 'NOT_MOUNTED'
    | 'MOUNTING'
    | 'MOUNTED'
    | 'UPDATING'
    | 'UNMOUNTING'
    | 'UNLOADING'
    | 'LOAD_ERROR'
    | 'SKIP_BECAUSE_BROKEN';

/**
 * 严格的沙箱类型
 */
export type StrictSandboxType = 
    | 'NAMESPACE'
    | 'PROXY'
    | 'IFRAME'
    | 'SNAPSHOT'
    | 'AUTO';

/**
 * 严格的资源类型
 */
export type StrictResourceType = 
    | 'script'
    | 'style'
    | 'html'
    | 'json'
    | 'text'
    | 'image'
    | 'font'
    | 'other';

/**
 * 严格的HTTP方法类型
 */
export type StrictHttpMethod = 
    | 'GET'
    | 'POST'
    | 'PUT'
    | 'DELETE'
    | 'PATCH'
    | 'HEAD'
    | 'OPTIONS';

/**
 * 严格的日志级别类型
 */
export type StrictLogLevel = 
    | 'DEBUG'
    | 'INFO'
    | 'WARN'
    | 'ERROR'
    | 'FATAL';

/**
 * 严格的回调函数类型
 */
export interface StrictCallback<TArgs extends readonly unknown[] = [], TReturn = void> {
    (...args: TArgs): TReturn | Promise<TReturn>;
}

/**
 * 严格的事件监听器类型
 */
export interface StrictEventListener<TData = StrictEventData> {
    (data: TData): void | Promise<void>;
}

/**
 * 严格的中间件类型
 */
export interface StrictMiddleware<TContext = StrictJsonObject, TNext = () => void | Promise<void>> {
    (context: TContext, next: TNext): void | Promise<void>;
}

/**
 * 严格的插件接口
 */
export interface StrictPlugin {
    /** 插件名称 */
    readonly name: string;
    /** 插件版本 */
    readonly version: string;
    /** 插件描述 */
    readonly description?: string;
    /** 插件依赖 */
    readonly dependencies?: readonly string[];
    /** 插件配置 */
    readonly config?: StrictConfig;
    /** 安装插件 */
    install(context: StrictJsonObject): void | Promise<void>;
    /** 卸载插件 */
    uninstall?(): void | Promise<void>;
}

/**
 * 严格的应用配置类型
 */
export interface StrictAppConfig {
    /** 应用名称 */
    readonly name: string;
    /** 应用入口 */
    readonly entry: string | StrictAppEntry;
    /** 容器选择器 */
    readonly container: string;
    /** 激活条件 */
    readonly activeWhen?: string | string[] | StrictCallback<[Location], boolean>;
    /** 应用属性 */
    readonly props?: StrictJsonObject;
    /** 沙箱配置 */
    readonly sandbox?: StrictSandboxConfig;
    /** 加载器配置 */
    readonly loader?: StrictLoaderConfig;
}

/**
 * 严格的应用入口配置
 */
export interface StrictAppEntry {
    /** 脚本列表 */
    readonly scripts?: readonly string[];
    /** 样式列表 */
    readonly styles?: readonly string[];
    /** HTML模板 */
    readonly html?: string;
}

/**
 * 严格的沙箱配置
 */
export interface StrictSandboxConfig {
    /** 沙箱类型 */
    readonly type: StrictSandboxType;
    /** 是否启用CSS隔离 */
    readonly cssIsolation?: boolean;
    /** 是否启用JS隔离 */
    readonly jsIsolation?: boolean;
    /** 白名单 */
    readonly allowList?: readonly string[];
    /** 黑名单 */
    readonly denyList?: readonly string[];
    /** 自定义配置 */
    readonly options?: StrictConfig;
}

/**
 * 严格的加载器配置
 */
export interface StrictLoaderConfig {
    /** 超时时间 */
    readonly timeout?: number;
    /** 重试次数 */
    readonly retry?: number;
    /** 是否启用缓存 */
    readonly cache?: boolean;
    /** 预加载策略 */
    readonly prefetch?: boolean;
    /** 自定义加载器 */
    readonly customLoader?: StrictCallback<[string | StrictAppEntry], unknown>;
}

/**
 * 严格的资源信息类型
 */
export interface StrictResourceInfo {
    /** 资源URL */
    readonly url: string;
    /** 资源类型 */
    readonly type: StrictResourceType;
    /** 资源大小 */
    readonly size?: number;
    /** 加载时间 */
    readonly loadTime?: number;
    /** 是否已缓存 */
    readonly cached?: boolean;
    /** 资源内容 */
    readonly content?: string;
}

/**
 * 严格的应用实例类型
 */
export interface StrictAppInstance {
    /** 应用名称 */
    readonly name: string;
    /** 应用状态 */
    readonly status: StrictLifecycleState;
    /** 应用配置 */
    readonly config: StrictAppConfig;
    /** 应用属性 */
    readonly props: StrictJsonObject;
    /** 挂载方法 */
    mount?(props?: StrictJsonObject): void | Promise<void>;
    /** 卸载方法 */
    unmount?(): void | Promise<void>;
    /** 更新方法 */
    update?(props?: StrictJsonObject): void | Promise<void>;
    /** 引导方法 */
    bootstrap?(): void | Promise<void>;
}

/**
 * 严格的沙箱实例类型
 */
export interface StrictSandboxInstance {
    /** 沙箱名称 */
    readonly name: string;
    /** 沙箱类型 */
    readonly type: StrictSandboxType;
    /** 是否激活 */
    readonly isActive: boolean;
    /** 激活沙箱 */
    active(): void;
    /** 停用沙箱 */
    inactive(): void;
    /** 销毁沙箱 */
    destroy(): void;
    /** 获取代理对象 */
    getProxy?(): object;
}

/**
 * 类型守卫工具函数
 */
export namespace StrictTypeGuards {
    export function isString(value: unknown): value is string {
        return typeof value === 'string';
    }

    export function isNumber(value: unknown): value is number {
        return typeof value === 'number' && !isNaN(value);
    }

    export function isBoolean(value: unknown): value is boolean {
        return typeof value === 'boolean';
    }

    export function isObject(value: unknown): value is StrictJsonObject {
        return value !== null && typeof value === 'object' && !Array.isArray(value);
    }

    export function isArray(value: unknown): value is StrictJsonArray {
        return Array.isArray(value);
    }

    export function isFunction(value: unknown): value is Function {
        return typeof value === 'function';
    }

    export function isStrictJsonValue(value: unknown): value is StrictJsonValue {
        if (value === null) return true;
        if (isString(value) || isNumber(value) || isBoolean(value)) return true;
        if (isArray(value)) return value.every(isStrictJsonValue);
        if (isObject(value)) return Object.values(value).every(isStrictJsonValue);
        return false;
    }
}

/**
 * 类型断言工具函数
 */
export namespace StrictAssertions {
    export function assertString(value: unknown, message?: string): asserts value is string {
        if (!StrictTypeGuards.isString(value)) {
            throw new TypeError(message || `Expected string, got ${typeof value}`);
        }
    }

    export function assertNumber(value: unknown, message?: string): asserts value is number {
        if (!StrictTypeGuards.isNumber(value)) {
            throw new TypeError(message || `Expected number, got ${typeof value}`);
        }
    }

    export function assertObject(value: unknown, message?: string): asserts value is StrictJsonObject {
        if (!StrictTypeGuards.isObject(value)) {
            throw new TypeError(message || `Expected object, got ${typeof value}`);
        }
    }

    export function assertFunction(value: unknown, message?: string): asserts value is Function {
        if (!StrictTypeGuards.isFunction(value)) {
            throw new TypeError(message || `Expected function, got ${typeof value}`);
        }
    }
}
