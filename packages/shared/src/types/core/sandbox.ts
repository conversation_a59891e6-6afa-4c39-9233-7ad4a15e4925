/**
 * @fileoverview 沙箱相关类型定义
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

import type { SandboxTypeType } from '../constants';

/**
 * 沙箱实例接口
 */
export interface SandboxInstance {
    /** 沙箱名称 */
    name: string;
    /** 沙箱类型 */
    type: SandboxTypeType;
    /** 是否激活 */
    active: boolean;
    /** 激活沙箱 */
    activate(): void;
    /** 停用沙箱 */
    deactivate(): void;
    /** 执行脚本 */
    execScript(script: string): any;
    /** 销毁沙箱 */
    destroy(): void;
}

/**
 * 沙箱选项
 */
export interface SandboxOptions {
    /** 是否启用样式隔离 */
    styleIsolation?: boolean;
    /** 是否启用脚本隔离 */
    scriptIsolation?: boolean;
    /** 白名单 */
    allowList?: string[];
    /** 黑名单 */
    denyList?: string[];
    /** 自定义配置 */
    [key: string]: any;
}

/**
 * 代理沙箱配置
 */
export interface ProxySandboxOptions extends SandboxOptions {
    /** 是否启用严格模式 */
    strictMode?: boolean;
    /** 全局变量白名单 */
    globalWhitelist?: string[];
}

/**
 * Iframe 沙箱配置
 */
export interface IframeSandboxOptions extends SandboxOptions {
    /** iframe 属性 */
    iframeAttrs?: Record<string, string>;
    /** 是否允许脚本 */
    allowScripts?: boolean;
    /** 是否允许表单 */
    allowForms?: boolean;
}

/**
 * WebComponent 沙箱配置
 */
export interface WebComponentSandboxOptions extends SandboxOptions {
    /** 自定义元素标签名 */
    tagName?: string;
    /** Shadow DOM 模式 */
    shadowMode?: 'open' | 'closed';
}