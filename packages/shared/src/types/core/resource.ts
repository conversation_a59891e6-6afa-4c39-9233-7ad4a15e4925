/**
 * @fileoverview 资源相关类型定义
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

import type { ResourceStatusType, ResourceTypeType } from '../constants';

/**
 * 资源信息
 */
export interface ResourceInfo {
    /** 资源URL */
    url: string;
    /** 资源类型 */
    type: ResourceTypeType;
    /** 资源状态 */
    status: ResourceStatusType;
    /** 资源内容 */
    content?: string;
    /** 资源大小 */
    size?: number;
    /** 加载时间 */
    loadTime?: number;
    /** 错误信息 */
    error?: Error;
    /** 缓存时间 */
    cacheTime?: number;
    /** 是否来自缓存 */
    fromCache?: boolean;
}

/**
 * 资源加载选项
 */
export interface ResourceLoadOptions {
    /** 超时时间 */
    timeout?: number;
    /** 重试次数 */
    retries?: number;
    /** 是否启用缓存 */
    cache?: boolean;
    /** 缓存时间 */
    cacheTime?: number;
    /** 自定义请求头 */
    headers?: Record<string, string>;
    /** 跨域模式 */
    mode?: 'cors' | 'no-cors' | 'same-origin';
    /** 凭据模式 */
    credentials?: 'omit' | 'same-origin' | 'include';
}

/**
 * 资源缓存项
 */
export interface ResourceCacheItem {
    /** 资源URL */
    url: string;
    /** 资源内容 */
    content: string;
    /** 资源类型 */
    type: ResourceTypeType;
    /** 缓存时间 */
    cacheTime: number;
    /** 过期时间 */
    expireTime: number;
    /** 资源大小 */
    size: number;
}

/**
 * 资源管理器接口
 */
export interface ResourceManager {
    /** 加载资源 */
    loadResource(url: string, options?: ResourceLoadOptions): Promise<ResourceInfo>;
    /** 预加载资源 */
    preloadResource(url: string, options?: ResourceLoadOptions): Promise<void>;
    /** 获取资源 */
    getResource(url: string): ResourceInfo | null;
    /** 缓存资源 */
    cacheResource(url: string, content: string, type: ResourceTypeType): void;
    /** 清除缓存 */
    clearCache(url?: string): void;
    /** 获取缓存统计 */
    getCacheStats(): { size: number; count: number };
}