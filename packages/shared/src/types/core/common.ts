/**
 * @fileoverview 核心通用类型定义 - 仅包含核心特有的类型
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 * @description 通用类型已迁移到 @micro-core/shared/types
 */

import type {
    AppStatusType,
    LogLevelType,
    SandboxTypeType
} from '../constants';

/**
 * 通用回调函数类型
 */
export type CallbackFn<T = unknown, R = unknown> = (...args: T[]) => R | Promise<R>;

/**
 * 事件监听器类型
 */
export type EventListener<T = unknown> = (...args: T[]) => void | Promise<void>;

/**
 * 插件系统接口 - 核心特有
 */
export interface PluginSystem {
    /** 注册插件 */
    register: (plugin: Plugin) => void;
    /** 卸载插件 */
    unregister: (name: string) => void;
    /** 获取插件 */
    get: (name: string) => Plugin | undefined;
    /** 其他属性 */
    [key: string]: unknown;
}

/**
 * 微前端核心配置 - 核心特有
 */
export interface MicroCoreOptions {
    /** 是否为开发模式 */
    development?: boolean;
    /** 日志级别 */
    logLevel?: LogLevelType;
    /** 默认沙箱类型 */
    defaultSandbox?: SandboxTypeType;
    /** 错误处理函数 */
    errorHandler?: (error: Error) => void;
    /** 插件列表 */
    plugins?: Plugin[];
}

/**
 * 插件接口
 */
export interface Plugin {
    /** 插件名称 */
    name: string;
    /** 插件版本 */
    version: string;
    /** 插件描述 */
    description?: string;
    /** 插件安装函数 */
    install?: (pluginSystem: PluginSystem, options?: Record<string, unknown>) => void;
    /** 插件卸载函数 */
    uninstall?: (pluginSystem: PluginSystem) => void;
}

/**
 * 钩子函数类型
 */
export type HookFn<T = unknown, R = unknown> = (...args: T[]) => R | Promise<R>;

/**
 * 生命周期函数类型
 */
export type LifecycleFn = (props?: Record<string, unknown>) => void | Promise<void>;

/**
 * 激活条件类型
 */
export type ActiveWhen =
    | string
    | string[]
    | ((location: Location) => boolean);

/**
 * 状态变化回调
 */
export type StateChangeCallback<T = unknown> = (change: {
    key: string;
    oldValue: T;
    newValue: T;
    timestamp: number;
}) => void;

/**
 * 加载选项
 */
export interface LoadOptions {
    /** 超时时间 */
    timeout?: number;
    /** 重试次数 */
    retries?: number;
    /** 缓存策略 */
    cache?: boolean;
}

/**
 * 微应用基本信息
 */
export interface MicroApp {
    /** 应用名称 */
    name: string;
    /** 应用状态 */
    status: AppStatusType;
    /** 应用入口 */
    entry: string | AppEntry;
    /** 容器 */
    container: string | HTMLElement;
    /** 激活条件 */
    activeWhen?: ActiveWhen;
}

/**
 * 应用入口配置
 */
export interface AppEntry {
    /** 脚本列表 */
    scripts?: string[];
    /** 样式列表 */
    styles?: string[];
    /** HTML 模板 */
    html?: string;
}

/**
 * 事件总线接口
 */
export interface EventBus {
    on(event: string, listener: EventListener): void;
    off(event: string, listener: EventListener): void;
    emit(event: string, ...args: unknown[]): void;
    once(event: string, listener: EventListener): void;
    clear(): void;
}

/**
 * 全局状态接口
 */
export interface GlobalState {
    get<T = unknown>(key: string): T | undefined;
    set<T = unknown>(key: string, value: T): void;
    remove(key: string): void;
    clear(): void;
    onChange(callback: StateChangeCallback): () => void;
    onKeyChange<T = unknown>(key: string, callback: StateChangeCallback<T>): () => void;
}

/**
 * 沙箱实例接口
 */
export interface SandboxInstance {
    /** 激活沙箱 */
    active(): void;
    /** 停用沙箱 */
    inactive(): void;
    /** 销毁沙箱 */
    destroy(): void;
    /** 获取沙箱状态 */
    isActive(): boolean;
    /** 其他属性 */
    [key: string]: unknown;
}

/**
 * 沙箱管理器接口
 */
export interface SandboxManager {
    createSandbox(name: string, type?: SandboxTypeType): SandboxInstance;
    getSandbox(name: string): SandboxInstance | null;
    destroySandbox(name: string): void;
    destroyAll(): void;
}