/**
 * @fileoverview 测试工具集主入口
 * @description 提供统一的测试工具函数和辅助类
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

// 核心测试工具
export * from './core/assertions';
export * from './core/data-generator';
export * from './core/performance';
export * from './core/test-utils';

// 模拟工具
export * from './mocks/network-mock';
export * from './mocks/storage-mock';

// 测试套件工厂
export * from './factories/test-suite-factory';

// 重新导出主要类
export { TestAssertions as Assertions } from './core/assertions';
export { TestDataGenerator as DataGenerator } from './core/data-generator';
export { PerformanceTestUtils as Performance } from './core/performance';
export { MicroCoreTestUtils as TestUtils } from './core/test-utils';
export { TestSuiteFactory as SuiteFactory } from './factories/test-suite-factory';
export { NetworkMockUtils as NetworkMock } from './mocks/network-mock';
export { StorageMockUtils as StorageMock } from './mocks/storage-mock';

// 导入类以创建实例
import { TestAssertions } from './core/assertions';
import { TestDataGenerator } from './core/data-generator';
import { PerformanceTestUtils } from './core/performance';
import { MicroCoreTestUtils } from './core/test-utils';
import { TestSuiteFactory } from './factories/test-suite-factory';
import { NetworkMockUtils } from './mocks/network-mock';
import { StorageMockUtils } from './mocks/storage-mock';

/**
 * 全局测试工具实例
 */
export const testUtils = new MicroCoreTestUtils();

/**
 * 测试生命周期钩子
 */
export const testLifecycle = {
    beforeAll: (fn: () => void | Promise<void>) => {
        if (typeof beforeAll !== 'undefined') {
            beforeAll(fn);
        }
    },
    afterAll: (fn: () => void | Promise<void>) => {
        if (typeof afterAll !== 'undefined') {
            afterAll(fn);
        }
    },
    beforeEach: (fn: () => void | Promise<void>) => {
        if (typeof beforeEach !== 'undefined') {
            beforeEach(fn);
        }
    },
    afterEach: (fn: () => void | Promise<void>) => {
        if (typeof afterEach !== 'undefined') {
            afterEach(fn);
        }
    }
};

/**
 * 便捷的测试函数
 */
export const testHelpers = {
    /**
     * 异步测试
     */
    async: (name: string, fn: () => Promise<void>, timeout?: number) => {
        if (typeof it !== 'undefined') {
            it(name, fn, timeout);
        }
    },

    /**
     * 跳过测试
     */
    skip: (name: string, fn: () => void | Promise<void>) => {
        if (typeof it !== 'undefined' && it.skip) {
            it.skip(name, fn);
        }
    },

    /**
     * 仅运行此测试
     */
    only: (name: string, fn: () => void | Promise<void>) => {
        if (typeof it !== 'undefined' && it.only) {
            it.only(name, fn);
        }
    },

    /**
     * 参数化测试
     */
    each: <T>(cases: T[]) => (name: string, fn: (testCase: T) => void | Promise<void>) => {
        if (typeof test !== 'undefined' && test.each) {
            test.each(cases)(name, fn);
        }
    }
};

/**
 * 导出所有工具的集合对象
 */
export const MicroCoreTestingUtils = {
    TestUtils: MicroCoreTestUtils,
    DataGenerator: TestDataGenerator,
    Assertions: TestAssertions,
    Performance: PerformanceTestUtils,
    NetworkMock: NetworkMockUtils,
    StorageMock: StorageMockUtils,
    SuiteFactory: TestSuiteFactory,
    testUtils,
    testLifecycle,
    testHelpers
};

/**
 * 默认导出
 */
export default MicroCoreTestingUtils;