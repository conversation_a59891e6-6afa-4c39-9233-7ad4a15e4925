/**
 * @fileoverview 网络模拟工具
 * @description 提供网络请求模拟和测试功能
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

/**
 * 网络模拟工具
 */
export class NetworkMockUtils {
    private static originalFetch: typeof fetch;

    /**
     * 模拟网络请求
     */
    static mockFetch(responses: Record<string, any>): void {
        if (!this.originalFetch) {
            this.originalFetch = global.fetch;
        }

        global.fetch = jest.fn((url: string) => {
            const response = responses[url];
            if (response) {
                return Promise.resolve({
                    ok: true,
                    status: 200,
                    json: () => Promise.resolve(response),
                    text: () => Promise.resolve(JSON.stringify(response)),
                    blob: () => Promise.resolve(new Blob([JSON.stringify(response)])),
                    arrayBuffer: () => Promise.resolve(new ArrayBuffer(0))
                } as Response);
            }
            return Promise.reject(new Error(`未找到模拟响应: ${url}`));
        });
    }

    /**
     * 模拟网络延迟
     */
    static mockNetworkDelay(delay: number): void {
        if (!this.originalFetch) {
            this.originalFetch = global.fetch;
        }

        global.fetch = jest.fn(async (...args) => {
            await new Promise(resolve => setTimeout(resolve, delay));
            return this.originalFetch(...args);
        });
    }

    /**
     * 模拟网络错误
     */
    static mockNetworkError(errorMessage: string = '网络错误'): void {
        global.fetch = jest.fn(() => Promise.reject(new Error(errorMessage)));
    }

    /**
     * 模拟HTTP状态码
     */
    static mockHttpStatus(url: string, status: number, statusText?: string): void {
        if (!this.originalFetch) {
            this.originalFetch = global.fetch;
        }

        global.fetch = jest.fn((requestUrl: string) => {
            if (requestUrl === url) {
                return Promise.resolve({
                    ok: status >= 200 && status < 300,
                    status,
                    statusText: statusText || this.getStatusText(status),
                    json: () => Promise.resolve({}),
                    text: () => Promise.resolve(''),
                    blob: () => Promise.resolve(new Blob()),
                    arrayBuffer: () => Promise.resolve(new ArrayBuffer(0))
                } as Response);
            }
            return this.originalFetch(requestUrl);
        });
    }

    /**
     * 模拟超时
     */
    static mockTimeout(timeout: number): void {
        global.fetch = jest.fn(() => {
            return new Promise((_, reject) => {
                setTimeout(() => {
                    reject(new Error('请求超时'));
                }, timeout);
            });
        });
    }

    /**
     * 模拟间歇性网络故障
     */
    static mockIntermittentFailure(failureRate: number = 0.3): void {
        if (!this.originalFetch) {
            this.originalFetch = global.fetch;
        }

        global.fetch = jest.fn(async (...args) => {
            if (Math.random() < failureRate) {
                throw new Error('间歇性网络故障');
            }
            return this.originalFetch(...args);
        });
    }

    /**
     * 模拟慢网络
     */
    static mockSlowNetwork(minDelay: number = 1000, maxDelay: number = 3000): void {
        if (!this.originalFetch) {
            this.originalFetch = global.fetch;
        }

        global.fetch = jest.fn(async (...args) => {
            const delay = Math.random() * (maxDelay - minDelay) + minDelay;
            await new Promise(resolve => setTimeout(resolve, delay));
            return this.originalFetch(...args);
        });
    }

    /**
     * 模拟RESTful API
     */
    static mockRestApi(baseUrl: string, resources: Record<string, any[]>): void {
        if (!this.originalFetch) {
            this.originalFetch = global.fetch;
        }

        global.fetch = jest.fn((url: string, options?: RequestInit) => {
            const method = options?.method || 'GET';
            const urlObj = new URL(url);
            const path = urlObj.pathname.replace(baseUrl, '');
            const segments = path.split('/').filter(Boolean);

            if (segments.length === 0) {
                return Promise.resolve({
                    ok: true,
                    status: 200,
                    json: () => Promise.resolve({ message: 'API Root' })
                } as Response);
            }

            const resourceName = segments[0];
            const resourceId = segments[1];
            const resource = resources[resourceName];

            if (!resource) {
                return Promise.resolve({
                    ok: false,
                    status: 404,
                    json: () => Promise.resolve({ error: 'Resource not found' })
                } as Response);
            }

            switch (method.toUpperCase()) {
                case 'GET':
                    if (resourceId) {
                        const item = resource.find(r => r.id === resourceId);
                        return Promise.resolve({
                            ok: !!item,
                            status: item ? 200 : 404,
                            json: () => Promise.resolve(item || { error: 'Not found' })
                        } as Response);
                    } else {
                        return Promise.resolve({
                            ok: true,
                            status: 200,
                            json: () => Promise.resolve(resource)
                        } as Response);
                    }

                case 'POST':
                    const newItem = { id: Date.now().toString(), ...JSON.parse(options?.body as string || '{}') };
                    resource.push(newItem);
                    return Promise.resolve({
                        ok: true,
                        status: 201,
                        json: () => Promise.resolve(newItem)
                    } as Response);

                case 'PUT':
                    if (resourceId) {
                        const index = resource.findIndex(r => r.id === resourceId);
                        if (index !== -1) {
                            resource[index] = { id: resourceId, ...JSON.parse(options?.body as string || '{}') };
                            return Promise.resolve({
                                ok: true,
                                status: 200,
                                json: () => Promise.resolve(resource[index])
                            } as Response);
                        }
                    }
                    return Promise.resolve({
                        ok: false,
                        status: 404,
                        json: () => Promise.resolve({ error: 'Not found' })
                    } as Response);

                case 'DELETE':
                    if (resourceId) {
                        const index = resource.findIndex(r => r.id === resourceId);
                        if (index !== -1) {
                            resource.splice(index, 1);
                            return Promise.resolve({
                                ok: true,
                                status: 204,
                                json: () => Promise.resolve({})
                            } as Response);
                        }
                    }
                    return Promise.resolve({
                        ok: false,
                        status: 404,
                        json: () => Promise.resolve({ error: 'Not found' })
                    } as Response);

                default:
                    return Promise.resolve({
                        ok: false,
                        status: 405,
                        json: () => Promise.resolve({ error: 'Method not allowed' })
                    } as Response);
            }
        });
    }

    /**
     * 模拟GraphQL API
     */
    static mockGraphQL(endpoint: string, schema: Record<string, any>): void {
        if (!this.originalFetch) {
            this.originalFetch = global.fetch;
        }

        global.fetch = jest.fn((url: string, options?: RequestInit) => {
            if (url === endpoint && options?.method === 'POST') {
                try {
                    const body = JSON.parse(options.body as string);
                    const { query, variables } = body;

                    // 简单的GraphQL查询解析（实际应用中需要更复杂的解析器）
                    const result = this.parseGraphQLQuery(query, variables, schema);

                    return Promise.resolve({
                        ok: true,
                        status: 200,
                        json: () => Promise.resolve({ data: result })
                    } as Response);
                } catch (error) {
                    return Promise.resolve({
                        ok: false,
                        status: 400,
                        json: () => Promise.resolve({
                            errors: [{ message: error instanceof Error ? error.message : String(error) }]
                        })
                    } as Response);
                }
            }
            return this.originalFetch(url, options);
        });
    }

    /**
     * 恢复原始 fetch
     */
    static restoreFetch(): void {
        if (this.originalFetch) {
            global.fetch = this.originalFetch;
        }
    }

    /**
     * 获取HTTP状态码对应的状态文本
     */
    private static getStatusText(status: number): string {
        const statusTexts: Record<number, string> = {
            200: 'OK',
            201: 'Created',
            204: 'No Content',
            400: 'Bad Request',
            401: 'Unauthorized',
            403: 'Forbidden',
            404: 'Not Found',
            405: 'Method Not Allowed',
            500: 'Internal Server Error',
            502: 'Bad Gateway',
            503: 'Service Unavailable'
        };
        return statusTexts[status] || 'Unknown';
    }

    /**
     * 简单的GraphQL查询解析器
     */
    private static parseGraphQLQuery(query: string, variables: any, schema: Record<string, any>): any {
        // 这是一个非常简化的GraphQL解析器，仅用于测试
        // 实际应用中应该使用专业的GraphQL解析库

        // 提取查询字段
        const fieldMatch = query.match(/{\s*(\w+)/);
        if (fieldMatch) {
            const fieldName = fieldMatch[1];
            return schema[fieldName] || null;
        }

        return null;
    }

    /**
     * 创建网络请求拦截器
     */
    static createInterceptor(): {
        requests: Array<{ url: string; options?: RequestInit; timestamp: number }>;
        clear: () => void;
        getLastRequest: () => { url: string; options?: RequestInit; timestamp: number } | null;
        getRequestCount: () => number;
    } {
        const requests: Array<{ url: string; options?: RequestInit; timestamp: number }> = [];

        if (!this.originalFetch) {
            this.originalFetch = global.fetch;
        }

        global.fetch = jest.fn(async (url: string, options?: RequestInit) => {
            requests.push({ url, options, timestamp: Date.now() });
            return this.originalFetch(url, options);
        });

        return {
            requests,
            clear: () => requests.length = 0,
            getLastRequest: () => requests[requests.length - 1] || null,
            getRequestCount: () => requests.length
        };
    }

    /**
     * 模拟WebSocket连接
     */
    static mockWebSocket(): {
        mockWebSocket: jest.MockedClass<typeof WebSocket>;
        simulateMessage: (data: string) => void;
        simulateError: (error: Event) => void;
        simulateClose: (code?: number, reason?: string) => void;
    } {
        const mockWebSocket = jest.fn().mockImplementation(() => {
            const ws = {
                send: jest.fn(),
                close: jest.fn(),
                addEventListener: jest.fn(),
                removeEventListener: jest.fn(),
                onopen: null,
                onmessage: null,
                onerror: null,
                onclose: null,
                readyState: WebSocket.CONNECTING
            };

            // 模拟连接成功
            setTimeout(() => {
                ws.readyState = WebSocket.OPEN;
                if (ws.onopen) {
                    ws.onopen(new Event('open'));
                }
            }, 100);

            return ws;
        });

        (global as any).WebSocket = mockWebSocket;

        return {
            mockWebSocket,
            simulateMessage: (data: string) => {
                const instances = mockWebSocket.mock.instances;
                instances.forEach((ws: any) => {
                    if (ws.onmessage) {
                        ws.onmessage({ data });
                    }
                });
            },
            simulateError: (error: Event) => {
                const instances = mockWebSocket.mock.instances;
                instances.forEach((ws: any) => {
                    if (ws.onerror) {
                        ws.onerror(error);
                    }
                });
            },
            simulateClose: (code = 1000, reason = '') => {
                const instances = mockWebSocket.mock.instances;
                instances.forEach((ws: any) => {
                    ws.readyState = WebSocket.CLOSED;
                    if (ws.onclose) {
                        ws.onclose({ code, reason });
                    }
                });
            }
        };
    }
}