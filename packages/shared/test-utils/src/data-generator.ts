/**
 * @fileoverview 测试数据生成器
 * @description 提供各种类型的测试数据生成功能
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

/**
 * 数据生成器配置
 */
export interface DataGeneratorConfig {
    /** 随机种子 */
    seed?: number;
    /** 语言环境 */
    locale?: string;
    /** 自定义数据源 */
    customData?: Record<string, any[]>;
}

/**
 * 测试数据生成器
 */
export class DataGenerator {
    private seed: number;
    private locale: string;
    private customData: Record<string, any[]>;

    constructor(config: DataGeneratorConfig = {}) {
        this.seed = config.seed || Date.now();
        this.locale = config.locale || 'zh-CN';
        this.customData = config.customData || {};
    }

    /**
     * 生成随机数
     */
    random(): number {
        this.seed = (this.seed * 9301 + 49297) % 233280;
        return this.seed / 233280;
    }

    /**
     * 生成指定范围内的随机整数
     */
    randomInt(min: number, max: number): number {
        return Math.floor(this.random() * (max - min + 1)) + min;
    }

    /**
     * 生成随机字符串
     */
    randomString(length: number = 10, charset: string = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'): string {
        let result = '';
        for (let i = 0; i < length; i++) {
            result += charset.charAt(Math.floor(this.random() * charset.length));
        }
        return result;
    }

    /**
     * 生成随机布尔值
     */
    randomBoolean(): boolean {
        return this.random() < 0.5;
    }

    /**
     * 从数组中随机选择一个元素
     */
    randomChoice<T>(array: T[]): T {
        return array[Math.floor(this.random() * array.length)];
    }

    /**
     * 生成随机姓名
     */
    randomName(): string {
        const firstNames = this.locale === 'zh-CN'
            ? ['张', '王', '李', '赵', '刘', '陈', '杨', '黄', '周', '吴']
            : ['John', 'Jane', 'Bob', 'Alice', 'Charlie', 'Diana', 'Eve', 'Frank', 'Grace', 'Henry'];

        const lastNames = this.locale === 'zh-CN'
            ? ['伟', '芳', '娜', '敏', '静', '丽', '强', '磊', '军', '洋']
            : ['Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Garcia', 'Miller', 'Davis', 'Rodriguez', 'Martinez'];

        return this.randomChoice(firstNames) + this.randomChoice(lastNames);
    }

    /**
     * 生成随机邮箱
     */
    randomEmail(): string {
        const domains = ['gmail.com', 'yahoo.com', 'hotmail.com', '163.com', 'qq.com'];
        const username = this.randomString(8, 'abcdefghijklmnopqrstuvwxyz0123456789');
        return `${username}@${this.randomChoice(domains)}`;
    }

    /**
     * 生成随机电话号码
     */
    randomPhone(): string {
        if (this.locale === 'zh-CN') {
            const prefixes = ['130', '131', '132', '133', '134', '135', '136', '137', '138', '139'];
            return this.randomChoice(prefixes) + this.randomString(8, '0123456789');
        } else {
            return `+1-${this.randomInt(100, 999)}-${this.randomInt(100, 999)}-${this.randomInt(1000, 9999)}`;
        }
    }

    /**
     * 生成随机地址
     */
    randomAddress(): string {
        if (this.locale === 'zh-CN') {
            const provinces = ['北京市', '上海市', '广东省', '浙江省', '江苏省', '山东省'];
            const cities = ['海淀区', '朝阳区', '浦东新区', '天河区', '西湖区', '鼓楼区'];
            const streets = ['中山路', '人民路', '解放路', '建设路', '和平路', '友谊路'];

            return `${this.randomChoice(provinces)}${this.randomChoice(cities)}${this.randomChoice(streets)}${this.randomInt(1, 999)}号`;
        } else {
            const streets = ['Main St', 'Oak Ave', 'Pine Rd', 'Elm Dr', 'Maple Ln', 'Cedar Blvd'];
            const cities = ['New York', 'Los Angeles', 'Chicago', 'Houston', 'Phoenix', 'Philadelphia'];
            const states = ['NY', 'CA', 'IL', 'TX', 'AZ', 'PA'];

            return `${this.randomInt(1, 9999)} ${this.randomChoice(streets)}, ${this.randomChoice(cities)}, ${this.randomChoice(states)} ${this.randomInt(10000, 99999)}`;
        }
    }

    /**
     * 生成随机日期
     */
    randomDate(start?: Date, end?: Date): Date {
        const startTime = start ? start.getTime() : new Date(2020, 0, 1).getTime();
        const endTime = end ? end.getTime() : new Date().getTime();

        return new Date(startTime + this.random() * (endTime - startTime));
    }

    /**
     * 生成随机URL
     */
    randomUrl(): string {
        const protocols = ['http', 'https'];
        const domains = ['example.com', 'test.org', 'demo.net', 'sample.io', 'mock.dev'];
        const paths = ['', '/api', '/users', '/products', '/admin', '/dashboard'];

        return `${this.randomChoice(protocols)}://${this.randomChoice(domains)}${this.randomChoice(paths)}`;
    }

    /**
     * 生成随机颜色
     */
    randomColor(): string {
        const r = this.randomInt(0, 255);
        const g = this.randomInt(0, 255);
        const b = this.randomInt(0, 255);
        return `rgb(${r}, ${g}, ${b})`;
    }

    /**
     * 生成随机十六进制颜色
     */
    randomHexColor(): string {
        return '#' + this.randomString(6, '0123456789ABCDEF');
    }

    /**
     * 生成随机用户对象
     */
    randomUser(): {
        id: string;
        name: string;
        email: string;
        phone: string;
        address: string;
        birthDate: Date;
        isActive: boolean;
        avatar: string;
    } {
        return {
            id: this.randomString(12, '0123456789abcdef'),
            name: this.randomName(),
            email: this.randomEmail(),
            phone: this.randomPhone(),
            address: this.randomAddress(),
            birthDate: this.randomDate(new Date(1950, 0, 1), new Date(2005, 11, 31)),
            isActive: this.randomBoolean(),
            avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${this.randomString(8)}`
        };
    }

    /**
     * 生成随机产品对象
     */
    randomProduct(): {
        id: string;
        name: string;
        description: string;
        price: number;
        category: string;
        inStock: boolean;
        tags: string[];
        createdAt: Date;
    } {
        const categories = ['电子产品', '服装', '家居', '图书', '运动', '美妆'];
        const tags = ['热销', '新品', '限时优惠', '包邮', '精选', '推荐'];

        return {
            id: this.randomString(8, '0123456789ABCDEF'),
            name: `产品${this.randomString(4)}`,
            description: `这是一个优质的${this.randomChoice(categories)}产品，具有出色的性能和设计。`,
            price: parseFloat((this.random() * 1000 + 10).toFixed(2)),
            category: this.randomChoice(categories),
            inStock: this.randomBoolean(),
            tags: Array.from({ length: this.randomInt(1, 3) }, () => this.randomChoice(tags)),
            createdAt: this.randomDate(new Date(2023, 0, 1), new Date())
        };
    }

    /**
     * 生成随机文章对象
     */
    randomArticle(): {
        id: string;
        title: string;
        content: string;
        author: string;
        publishDate: Date;
        tags: string[];
        viewCount: number;
        likeCount: number;
    } {
        const titles = [
            '如何提高工作效率',
            '前端开发最佳实践',
            '微服务架构设计',
            '数据库优化技巧',
            '用户体验设计原则'
        ];

        const tags = ['技术', '教程', '经验分享', '最佳实践', '架构设计'];

        return {
            id: this.randomString(10, '0123456789abcdef'),
            title: this.randomChoice(titles),
            content: `这是一篇关于${this.randomChoice(titles)}的详细文章。文章内容丰富，包含了实用的技巧和经验分享。`,
            author: this.randomName(),
            publishDate: this.randomDate(new Date(2023, 0, 1), new Date()),
            tags: Array.from({ length: this.randomInt(2, 4) }, () => this.randomChoice(tags)),
            viewCount: this.randomInt(100, 10000),
            likeCount: this.randomInt(10, 1000)
        };
    }

    /**
     * 生成随机API响应
     */
    randomApiResponse<T>(data: T, success: boolean = true): {
        success: boolean;
        data: T | null;
        message: string;
        code: number;
        timestamp: number;
    } {
        return {
            success,
            data: success ? data : null,
            message: success ? '操作成功' : '操作失败',
            code: success ? 200 : this.randomChoice([400, 401, 403, 404, 500]),
            timestamp: Date.now()
        };
    }

    /**
     * 生成随机错误对象
     */
    randomError(): Error {
        const messages = [
            '网络连接失败',
            '服务器内部错误',
            '参数验证失败',
            '权限不足',
            '资源不存在',
            '请求超时'
        ];

        return new Error(this.randomChoice(messages));
    }

    /**
     * 生成随机数组
     */
    randomArray<T>(generator: () => T, length?: number): T[] {
        const arrayLength = length || this.randomInt(1, 10);
        return Array.from({ length: arrayLength }, generator);
    }

    /**
     * 生成随机对象
     */
    randomObject(schema: Record<string, () => any>): Record<string, any> {
        const result: Record<string, any> = {};

        for (const [key, generator] of Object.entries(schema)) {
            result[key] = generator();
        }

        return result;
    }

    /**
     * 生成随机树形数据
     */
    randomTree(maxDepth: number = 3, maxChildren: number = 5): any {
        const generateNode = (depth: number): any => {
            const node = {
                id: this.randomString(8),
                name: `节点${this.randomString(4)}`,
                value: this.randomInt(1, 100),
                children: [] as any[]
            };

            if (depth < maxDepth && this.randomBoolean()) {
                const childCount = this.randomInt(1, maxChildren);
                for (let i = 0; i < childCount; i++) {
                    node.children.push(generateNode(depth + 1));
                }
            }

            return node;
        };

        return generateNode(0);
    }

    /**
     * 生成随机图表数据
     */
    randomChartData(points: number = 10): {
        labels: string[];
        datasets: Array<{
            label: string;
            data: number[];
            backgroundColor: string;
            borderColor: string;
        }>;
    } {
        const labels = Array.from({ length: points }, (_, i) => `数据点${i + 1}`);
        const datasets = Array.from({ length: this.randomInt(1, 3) }, (_, i) => ({
            label: `数据集${i + 1}`,
            data: Array.from({ length: points }, () => this.randomInt(0, 100)),
            backgroundColor: this.randomHexColor(),
            borderColor: this.randomHexColor()
        }));

        return { labels, datasets };
    }

    /**
     * 生成随机表格数据
     */
    randomTableData(rows: number = 10): Array<Record<string, any>> {
        return Array.from({ length: rows }, () => ({
            id: this.randomString(8),
            name: this.randomName(),
            email: this.randomEmail(),
            status: this.randomChoice(['active', 'inactive', 'pending']),
            score: this.randomInt(0, 100),
            createdAt: this.randomDate().toISOString()
        }));
    }

    /**
     * 重置随机种子
     */
    setSeed(seed: number): void {
        this.seed = seed;
    }

    /**
     * 获取当前种子
     */
    getSeed(): number {
        return this.seed;
    }
}

/**
 * 创建数据生成器实例
 */
export function createDataGenerator(config?: DataGeneratorConfig): DataGenerator {
    return new DataGenerator(config);
}

/**
 * 默认数据生成器实例
 */
export const defaultDataGenerator = new DataGenerator();