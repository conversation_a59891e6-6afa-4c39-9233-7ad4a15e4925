/**
 * @fileoverview 性能测试工具
 * @description 提供性能测试和基准测试功能
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

/**
 * 性能测试结果
 */
export interface PerformanceResult {
    /** 结果值 */
    result: any;
    /** 执行时间（毫秒） */
    time: number;
}

/**
 * 基准测试结果
 */
export interface BenchmarkResult {
    /** 测试名称 */
    name: string;
    /** 迭代次数 */
    iterations: number;
    /** 总时间 */
    totalTime: number;
    /** 平均时间 */
    averageTime: number;
    /** 最小时间 */
    minTime: number;
    /** 最大时间 */
    maxTime: number;
}

/**
 * 内存使用信息
 */
export interface MemoryUsage {
    /** 已使用内存 */
    used: number;
    /** 总内存 */
    total: number;
}

/**
 * 性能测试工具
 */
export class PerformanceTestUtils {
    /**
     * 测量函数执行时间
     */
    static async measureExecutionTime<T>(fn: () => T | Promise<T>): Promise<PerformanceResult> {
        const startTime = performance.now();
        const result = await fn();
        const endTime = performance.now();
        return {
            result,
            time: endTime - startTime
        };
    }

    /**
     * 测量内存使用
     */
    static measureMemoryUsage(): MemoryUsage | null {
        if (typeof window !== 'undefined' && 'memory' in performance) {
            const memory = (performance as any).memory;
            return {
                used: memory.usedJSHeapSize,
                total: memory.totalJSHeapSize
            };
        }
        return null;
    }

    /**
     * 性能基准测试
     */
    static async benchmark<T>(
        name: string,
        fn: () => T | Promise<T>,
        iterations: number = 100
    ): Promise<BenchmarkResult> {
        const times: number[] = [];

        for (let i = 0; i < iterations; i++) {
            const { time } = await this.measureExecutionTime(fn);
            times.push(time);
        }

        const totalTime = times.reduce((sum, time) => sum + time, 0);
        const averageTime = totalTime / iterations;
        const minTime = Math.min(...times);
        const maxTime = Math.max(...times);

        return {
            name,
            iterations,
            totalTime,
            averageTime,
            minTime,
            maxTime
        };
    }

    /**
     * 比较两个函数的性能
     */
    static async compare<T>(
        name1: string,
        fn1: () => T | Promise<T>,
        name2: string,
        fn2: () => T | Promise<T>,
        iterations: number = 100
    ): Promise<{
        faster: string;
        slower: string;
        speedup: number;
        results: [BenchmarkResult, BenchmarkResult];
    }> {
        const result1 = await this.benchmark(name1, fn1, iterations);
        const result2 = await this.benchmark(name2, fn2, iterations);

        const faster = result1.averageTime < result2.averageTime ? name1 : name2;
        const slower = result1.averageTime < result2.averageTime ? name2 : name1;
        const speedup = Math.max(result1.averageTime, result2.averageTime) /
            Math.min(result1.averageTime, result2.averageTime);

        return {
            faster,
            slower,
            speedup,
            results: [result1, result2]
        };
    }

    /**
     * 测量渲染性能
     */
    static async measureRenderTime(renderFn: () => void | Promise<void>): Promise<number> {
        return new Promise((resolve) => {
            const startTime = performance.now();

            const observer = new PerformanceObserver((list) => {
                const entries = list.getEntries();
                const paintEntry = entries.find(entry => entry.entryType === 'paint');
                if (paintEntry) {
                    const renderTime = paintEntry.startTime - startTime;
                    observer.disconnect();
                    resolve(renderTime);
                }
            });

            observer.observe({ entryTypes: ['paint'] });

            // 执行渲染函数
            Promise.resolve(renderFn()).then(() => {
                // 如果没有paint事件，使用requestAnimationFrame作为fallback
                requestAnimationFrame(() => {
                    const endTime = performance.now();
                    observer.disconnect();
                    resolve(endTime - startTime);
                });
            });
        });
    }

    /**
     * 监控长任务
     */
    static monitorLongTasks(threshold: number = 50): Promise<PerformanceEntry[]> {
        return new Promise((resolve) => {
            const longTasks: PerformanceEntry[] = [];

            const observer = new PerformanceObserver((list) => {
                const entries = list.getEntries();
                entries.forEach(entry => {
                    if (entry.duration > threshold) {
                        longTasks.push(entry);
                    }
                });
            });

            observer.observe({ entryTypes: ['longtask'] });

            // 10秒后停止监控
            setTimeout(() => {
                observer.disconnect();
                resolve(longTasks);
            }, 10000);
        });
    }

    /**
     * 测量首次内容绘制时间
     */
    static measureFCP(): Promise<number> {
        return new Promise((resolve) => {
            const observer = new PerformanceObserver((list) => {
                const entries = list.getEntries();
                const fcpEntry = entries.find(entry => entry.name === 'first-contentful-paint');
                if (fcpEntry) {
                    observer.disconnect();
                    resolve(fcpEntry.startTime);
                }
            });

            observer.observe({ entryTypes: ['paint'] });
        });
    }

    /**
     * 测量最大内容绘制时间
     */
    static measureLCP(): Promise<number> {
        return new Promise((resolve) => {
            const observer = new PerformanceObserver((list) => {
                const entries = list.getEntries();
                const lcpEntry = entries[entries.length - 1];
                if (lcpEntry) {
                    resolve(lcpEntry.startTime);
                }
            });

            observer.observe({ entryTypes: ['largest-contentful-paint'] });

            // 10秒后超时
            setTimeout(() => {
                observer.disconnect();
                resolve(-1);
            }, 10000);
        });
    }

    /**
     * 格式化性能结果
     */
    static formatResult(result: BenchmarkResult): string {
        return `
性能测试结果: ${result.name}
迭代次数: ${result.iterations}
总时间: ${result.totalTime.toFixed(2)}ms
平均时间: ${result.averageTime.toFixed(2)}ms
最小时间: ${result.minTime.toFixed(2)}ms
最大时间: ${result.maxTime.toFixed(2)}ms
        `.trim();
    }
}