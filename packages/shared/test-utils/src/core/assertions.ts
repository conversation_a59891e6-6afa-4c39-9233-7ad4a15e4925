/**
 * @fileoverview 测试断言工具
 * @description 提供各种测试断言函数
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

/**
 * 测试断言工具
 */
export class TestAssertions {
    /**
     * 断言元素存在
     */
    static elementExists(selector: string, container?: Element): void {
        const element = (container || document).querySelector(selector);
        expect(element).toBeTruthy();
    }

    /**
     * 断言元素不存在
     */
    static elementNotExists(selector: string, container?: Element): void {
        const element = (container || document).querySelector(selector);
        expect(element).toBeFalsy();
    }

    /**
     * 断言元素文本
     */
    static elementHasText(selector: string, expectedText: string, container?: Element): void {
        const element = (container || document).querySelector(selector);
        expect(element).toBeTruthy();
        expect(element!.textContent).toContain(expectedText);
    }

    /**
     * 断言元素属性
     */
    static elementHasAttribute(selector: string, attribute: string, expectedValue?: string, container?: Element): void {
        const element = (container || document).querySelector(selector);
        expect(element).toBeTruthy();
        expect(element!.hasAttribute(attribute)).toBe(true);
        if (expectedValue !== undefined) {
            expect(element!.getAttribute(attribute)).toBe(expectedValue);
        }
    }

    /**
     * 断言元素类名
     */
    static elementHasClass(selector: string, className: string, container?: Element): void {
        const element = (container || document).querySelector(selector);
        expect(element).toBeTruthy();
        expect(element!.classList.contains(className)).toBe(true);
    }

    /**
     * 断言函数被调用
     */
    static functionCalled(mockFn: jest.MockedFunction<any>, times?: number): void {
        if (times !== undefined) {
            expect(mockFn).toHaveBeenCalledTimes(times);
        } else {
            expect(mockFn).toHaveBeenCalled();
        }
    }

    /**
     * 断言函数被调用时的参数
     */
    static functionCalledWith(mockFn: jest.MockedFunction<any>, ...args: any[]): void {
        expect(mockFn).toHaveBeenCalledWith(...args);
    }

    /**
     * 断言异步函数抛出错误
     */
    static async asyncThrows(fn: () => Promise<any>, expectedError?: string | RegExp): Promise<void> {
        await expect(fn()).rejects.toThrow(expectedError);
    }

    /**
     * 断言对象深度相等
     */
    static deepEqual(actual: any, expected: any): void {
        expect(actual).toEqual(expected);
    }

    /**
     * 断言数组包含元素
     */
    static arrayContains<T>(array: T[], element: T): void {
        expect(array).toContain(element);
    }

    /**
     * 断言数组长度
     */
    static arrayLength<T>(array: T[], expectedLength: number): void {
        expect(array).toHaveLength(expectedLength);
    }

    /**
     * 断言对象包含属性
     */
    static objectHasProperty(obj: any, property: string): void {
        expect(obj).toHaveProperty(property);
    }

    /**
     * 断言对象属性值
     */
    static objectPropertyEquals(obj: any, property: string, expectedValue: any): void {
        expect(obj).toHaveProperty(property, expectedValue);
    }

    /**
     * 断言字符串匹配正则
     */
    static stringMatches(str: string, pattern: RegExp): void {
        expect(str).toMatch(pattern);
    }

    /**
     * 断言数值范围
     */
    static numberInRange(value: number, min: number, max: number): void {
        expect(value).toBeGreaterThanOrEqual(min);
        expect(value).toBeLessThanOrEqual(max);
    }

    /**
     * 断言类型检查
     */
    static typeOf(value: any, expectedType: string): void {
        expect(typeof value).toBe(expectedType);
    }

    /**
     * 断言实例检查
     */
    static instanceOf(value: any, expectedClass: any): void {
        expect(value).toBeInstanceOf(expectedClass);
    }
}