/**
 * @fileoverview 测试数据生成器
 * @description 提供各种测试数据生成功能
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

/**
 * 测试数据生成器
 */
export class TestDataGenerator {
    /**
     * 生成随机字符串
     */
    static randomString(length: number = 10): string {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }

    /**
     * 生成随机数字
     */
    static randomNumber(min: number = 0, max: number = 100): number {
        return Math.floor(Math.random() * (max - min + 1)) + min;
    }

    /**
     * 生成随机布尔值
     */
    static randomBoolean(): boolean {
        return Math.random() < 0.5;
    }

    /**
     * 生成随机数组
     */
    static randomArray<T>(generator: () => T, length: number = 5): T[] {
        return Array.from({ length }, generator);
    }

    /**
     * 生成随机对象
     */
    static randomObject(schema: Record<string, () => any>): any {
        const result: any = {};
        Object.entries(schema).forEach(([key, generator]) => {
            result[key] = generator();
        });
        return result;
    }

    /**
     * 生成随机邮箱
     */
    static randomEmail(): string {
        const domains = ['example.com', 'test.com', 'demo.org', 'sample.net'];
        const username = this.randomString(8);
        const domain = domains[Math.floor(Math.random() * domains.length)];
        return `${username}@${domain}`;
    }

    /**
     * 生成随机URL
     */
    static randomUrl(): string {
        const protocols = ['http', 'https'];
        const domains = ['example.com', 'test.org', 'demo.net'];
        const paths = ['', '/api', '/app', '/admin', '/user'];

        const protocol = protocols[Math.floor(Math.random() * protocols.length)];
        const domain = domains[Math.floor(Math.random() * domains.length)];
        const path = paths[Math.floor(Math.random() * paths.length)];
        const port = this.randomNumber(3000, 9000);

        return `${protocol}://${domain}:${port}${path}`;
    }

    /**
     * 生成随机日期
     */
    static randomDate(start?: Date, end?: Date): Date {
        const startTime = start ? start.getTime() : new Date(2020, 0, 1).getTime();
        const endTime = end ? end.getTime() : new Date().getTime();
        const randomTime = startTime + Math.random() * (endTime - startTime);
        return new Date(randomTime);
    }

    /**
     * 生成模拟用户数据
     */
    static mockUserData(): any {
        return {
            id: this.randomNumber(1, 10000),
            name: this.randomString(8),
            email: this.randomEmail(),
            age: this.randomNumber(18, 80),
            active: this.randomBoolean(),
            createdAt: this.randomDate(),
            profile: {
                avatar: this.randomUrl(),
                bio: this.randomString(50),
                location: this.randomString(20)
            }
        };
    }

    /**
     * 生成模拟应用配置
     */
    static mockAppConfig(): any {
        return {
            name: this.randomString(8),
            entry: this.randomUrl(),
            container: `#app-${this.randomString(6)}`,
            props: {
                title: this.randomString(12),
                version: `${this.randomNumber(1, 9)}.${this.randomNumber(0, 9)}.${this.randomNumber(0, 9)}`,
                theme: this.randomString(10)
            },
            sandbox: {
                type: 'proxy',
                isolateGlobals: this.randomBoolean(),
                isolateStyles: this.randomBoolean()
            }
        };
    }

    /**
     * 生成模拟API响应
     */
    static mockApiResponse(data?: any): any {
        return {
            success: this.randomBoolean(),
            code: this.randomNumber(200, 500),
            message: this.randomString(20),
            data: data || this.randomObject({
                id: () => this.randomNumber(1, 1000),
                name: () => this.randomString(10),
                value: () => this.randomString(15)
            }),
            timestamp: this.randomDate().toISOString()
        };
    }

    /**
     * 生成模拟错误对象
     */
    static mockError(): Error {
        const messages = [
            '网络连接失败',
            '权限不足',
            '参数错误',
            '服务器内部错误',
            '资源未找到'
        ];
        const message = messages[Math.floor(Math.random() * messages.length)];
        const error = new Error(message);
        error.name = 'MockError';
        return error;
    }

    /**
     * 生成模拟事件数据
     */
    static mockEventData(): any {
        const eventTypes = ['click', 'change', 'submit', 'load', 'error'];
        return {
            type: eventTypes[Math.floor(Math.random() * eventTypes.length)],
            target: `#${this.randomString(8)}`,
            timestamp: Date.now(),
            data: this.randomObject({
                x: () => this.randomNumber(0, 1000),
                y: () => this.randomNumber(0, 1000),
                key: () => this.randomString(5)
            })
        };
    }

    /**
     * 生成模拟文件数据
     */
    static mockFileData(): File {
        const content = this.randomString(100);
        const blob = new Blob([content], { type: 'text/plain' });
        const fileName = `${this.randomString(8)}.txt`;
        return new File([blob], fileName, { type: 'text/plain' });
    }

    /**
     * 生成模拟表单数据
     */
    static mockFormData(): FormData {
        const formData = new FormData();
        formData.append('name', this.randomString(10));
        formData.append('email', this.randomEmail());
        formData.append('age', this.randomNumber(18, 80).toString());
        formData.append('active', this.randomBoolean().toString());
        return formData;
    }

    /**
     * 从数组中随机选择元素
     */
    static randomChoice<T>(array: T[]): T {
        return array[Math.floor(Math.random() * array.length)];
    }

    /**
     * 生成随机UUID
     */
    static randomUUID(): string {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
            const r = Math.random() * 16 | 0;
            const v = c === 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    }

    /**
     * 生成随机颜色
     */
    static randomColor(): string {
        const colors = [
            '#FF0000', '#00FF00', '#0000FF', '#FFFF00', '#FF00FF', '#00FFFF',
            '#800000', '#008000', '#000080', '#808000', '#800080', '#008080'
        ];
        return this.randomChoice(colors);
    }

    /**
     * 生成随机IP地址
     */
    static randomIP(): string {
        return `${this.randomNumber(1, 255)}.${this.randomNumber(0, 255)}.${this.randomNumber(0, 255)}.${this.randomNumber(1, 255)}`;
    }

    /**
     * 生成随机端口号
     */
    static randomPort(): number {
        return this.randomNumber(1024, 65535);
    }
}