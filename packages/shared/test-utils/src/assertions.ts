/**
 * @fileoverview 测试断言工具
 * @description 提供常用的测试断言方法
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

/**
 * 测试断言工具
 */
export class TestAssertions {
    /**
     * 断言元素存在
     */
    static elementExists(selector: string, container?: Element): void {
        const element = (container || document).querySelector(selector);
        expect(element).toBeTruthy();
    }

    /**
     * 断言元素不存在
     */
    static elementNotExists(selector: string, container?: Element): void {
        const element = (container || document).querySelector(selector);
        expect(element).toBeFalsy();
    }

    /**
     * 断言元素文本
     */
    static elementHasText(selector: string, expectedText: string, container?: Element): void {
        const element = (container || document).querySelector(selector);
        expect(element).toBeTruthy();
        expect(element!.textContent).toContain(expectedText);
    }

    /**
     * 断言元素属性
     */
    static elementHasAttribute(selector: string, attribute: string, expectedValue?: string, container?: Element): void {
        const element = (container || document).querySelector(selector);
        expect(element).toBeTruthy();
        expect(element!.hasAttribute(attribute)).toBe(true);
        if (expectedValue !== undefined) {
            expect(element!.getAttribute(attribute)).toBe(expectedValue);
        }
    }

    /**
     * 断言元素类名
     */
    static elementHasClass(selector: string, className: string, container?: Element): void {
        const element = (container || document).querySelector(selector);
        expect(element).toBeTruthy();
        expect(element!.classList.contains(className)).toBe(true);
    }

    /**
     * 断言函数被调用
     */
    static functionCalled(mockFn: jest.MockedFunction<any>, times?: number): void {
        if (times !== undefined) {
            expect(mockFn).toHaveBeenCalledTimes(times);
        } else {
            expect(mockFn).toHaveBeenCalled();
        }
    }

    /**
     * 断言函数被调用时的参数
     */
    static functionCalledWith(mockFn: jest.MockedFunction<any>, ...args: any[]): void {
        expect(mockFn).toHaveBeenCalledWith(...args);
    }

    /**
     * 断言异步函数抛出错误
     */
    static async asyncThrows(fn: () => Promise<any>, expectedError?: string | RegExp): Promise<void> {
        await expect(fn()).rejects.toThrow(expectedError);
    }

    /**
     * 断言对象深度相等
     */
    static deepEqual(actual: any, expected: any): void {
        expect(actual).toEqual(expected);
    }

    /**
     * 断言数组包含元素
     */
    static arrayContains<T>(array: T[], element: T): void {
        expect(array).toContain(element);
    }

    /**
     * 断言数组长度
     */
    static arrayLength<T>(array: T[], expectedLength: number): void {
        expect(array).toHaveLength(expectedLength);
    }

    /**
     * 断言字符串匹配正则表达式
     */
    static stringMatches(actual: string, pattern: RegExp): void {
        expect(actual).toMatch(pattern);
    }

    /**
     * 断言数字在范围内
     */
    static numberInRange(actual: number, min: number, max: number): void {
        expect(actual).toBeGreaterThanOrEqual(min);
        expect(actual).toBeLessThanOrEqual(max);
    }

    /**
     * 断言对象包含属性
     */
    static objectHasProperty(obj: any, property: string): void {
        expect(obj).toHaveProperty(property);
    }

    /**
     * 断言对象包含属性和值
     */
    static objectHasPropertyValue(obj: any, property: string, value: any): void {
        expect(obj).toHaveProperty(property, value);
    }

    /**
     * 断言Promise被解决
     */
    static async promiseResolves<T>(promise: Promise<T>): Promise<T> {
        return await expect(promise).resolves.toBeDefined();
    }

    /**
     * 断言Promise被拒绝
     */
    static async promiseRejects(promise: Promise<any>, expectedError?: string | RegExp): Promise<void> {
        await expect(promise).rejects.toThrow(expectedError);
    }

    /**
     * 断言值为真值
     */
    static truthy(value: any): void {
        expect(value).toBeTruthy();
    }

    /**
     * 断言值为假值
     */
    static falsy(value: any): void {
        expect(value).toBeFalsy();
    }

    /**
     * 断言值为null
     */
    static isNull(value: any): void {
        expect(value).toBeNull();
    }

    /**
     * 断言值为undefined
     */
    static isUndefined(value: any): void {
        expect(value).toBeUndefined();
    }

    /**
     * 断言值已定义
     */
    static isDefined(value: any): void {
        expect(value).toBeDefined();
    }

    /**
     * 断言类型匹配
     */
    static typeOf(value: any, expectedType: string): void {
        expect(typeof value).toBe(expectedType);
    }

    /**
     * 断言是数组
     */
    static isArray(value: any): void {
        expect(Array.isArray(value)).toBe(true);
    }

    /**
     * 断言是对象
     */
    static isObject(value: any): void {
        expect(typeof value).toBe('object');
        expect(value).not.toBeNull();
        expect(Array.isArray(value)).toBe(false);
    }

    /**
     * 断言是函数
     */
    static isFunction(value: any): void {
        expect(typeof value).toBe('function');
    }

    /**
     * 断言是字符串
     */
    static isString(value: any): void {
        expect(typeof value).toBe('string');
    }

    /**
     * 断言是数字
     */
    static isNumber(value: any): void {
        expect(typeof value).toBe('number');
        expect(isNaN(value)).toBe(false);
    }

    /**
     * 断言是布尔值
     */
    static isBoolean(value: any): void {
        expect(typeof value).toBe('boolean');
    }

    /**
     * 断言日期有效
     */
    static isValidDate(date: Date): void {
        expect(date instanceof Date).toBe(true);
        expect(isNaN(date.getTime())).toBe(false);
    }

    /**
     * 断言URL有效
     */
    static isValidUrl(url: string): void {
        expect(() => new URL(url)).not.toThrow();
    }

    /**
     * 断言邮箱格式有效
     */
    static isValidEmail(email: string): void {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        expect(email).toMatch(emailRegex);
    }

    /**
     * 断言JSON字符串有效
     */
    static isValidJson(jsonString: string): void {
        expect(() => JSON.parse(jsonString)).not.toThrow();
    }

    /**
     * 断言执行时间在范围内
     */
    static executionTimeInRange(actualTime: number, minTime: number, maxTime: number): void {
        expect(actualTime).toBeGreaterThanOrEqual(minTime);
        expect(actualTime).toBeLessThanOrEqual(maxTime);
    }

    /**
     * 断言内存使用在合理范围内
     */
    static memoryUsageReasonable(memoryUsage: number, maxUsage: number): void {
        expect(memoryUsage).toBeLessThanOrEqual(maxUsage);
        expect(memoryUsage).toBeGreaterThan(0);
    }
}