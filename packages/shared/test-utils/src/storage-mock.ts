/**
 * @fileoverview 存储模拟工具
 * @description 提供localStorage、sessionStorage等存储API的模拟功能
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

/**
 * 存储模拟工具
 */
export class StorageMockUtils {
    /**
     * 创建 localStorage 模拟
     */
    static createLocalStorageMock(): Storage {
        const store: Record<string, string> = {};

        return {
            getItem: jest.fn((key: string) => store[key] || null),
            setItem: jest.fn((key: string, value: string) => {
                store[key] = value;
            }),
            removeItem: jest.fn((key: string) => {
                delete store[key];
            }),
            clear: jest.fn(() => {
                Object.keys(store).forEach(key => delete store[key]);
            }),
            length: 0,
            key: jest.fn((index: number) => {
                const keys = Object.keys(store);
                return keys[index] || null;
            })
        };
    }

    /**
     * 设置 localStorage 模拟
     */
    static mockLocalStorage(): void {
        Object.defineProperty(window, 'localStorage', {
            value: this.createLocalStorageMock()
        });
    }

    /**
     * 设置 sessionStorage 模拟
     */
    static mockSessionStorage(): void {
        Object.defineProperty(window, 'sessionStorage', {
            value: this.createLocalStorageMock()
        });
    }

    /**
     * 创建带有初始数据的存储模拟
     */
    static createStorageMockWithData(initialData: Record<string, string>): Storage {
        const store: Record<string, string> = { ...initialData };

        return {
            getItem: jest.fn((key: string) => store[key] || null),
            setItem: jest.fn((key: string, value: string) => {
                store[key] = value;
            }),
            removeItem: jest.fn((key: string) => {
                delete store[key];
            }),
            clear: jest.fn(() => {
                Object.keys(store).forEach(key => delete store[key]);
            }),
            get length() {
                return Object.keys(store).length;
            },
            key: jest.fn((index: number) => {
                const keys = Object.keys(store);
                return keys[index] || null;
            })
        };
    }

    /**
     * 模拟存储配额限制
     */
    static mockStorageQuotaExceeded(): void {
        const originalSetItem = Storage.prototype.setItem;
        Storage.prototype.setItem = jest.fn((key: string, value: string) => {
            if (value.length > 1000) { // 模拟1KB限制
                throw new DOMException('QuotaExceededError');
            }
            originalSetItem.call(this, key, value);
        });
    }

    /**
     * 模拟存储访问被拒绝
     */
    static mockStorageAccessDenied(): void {
        Object.defineProperty(window, 'localStorage', {
            get: () => {
                throw new DOMException('SecurityError');
            }
        });

        Object.defineProperty(window, 'sessionStorage', {
            get: () => {
                throw new DOMException('SecurityError');
            }
        });
    }

    /**
     * 创建IndexedDB模拟
     */
    static mockIndexedDB(): {
        mockDB: any;
        simulateSuccess: (result: any) => void;
        simulateError: (error: DOMException) => void;
    } {
        const mockDB = {
            transaction: jest.fn(() => ({
                objectStore: jest.fn(() => ({
                    get: jest.fn(() => ({ onsuccess: null, onerror: null })),
                    put: jest.fn(() => ({ onsuccess: null, onerror: null })),
                    delete: jest.fn(() => ({ onsuccess: null, onerror: null })),
                    getAll: jest.fn(() => ({ onsuccess: null, onerror: null }))
                }))
            })),
            createObjectStore: jest.fn(),
            deleteObjectStore: jest.fn(),
            close: jest.fn()
        };

        const mockIDBFactory = {
            open: jest.fn(() => ({
                onsuccess: null,
                onerror: null,
                onupgradeneeded: null,
                result: mockDB
            })),
            deleteDatabase: jest.fn(() => ({
                onsuccess: null,
                onerror: null
            }))
        };

        Object.defineProperty(window, 'indexedDB', {
            value: mockIDBFactory
        });

        return {
            mockDB,
            simulateSuccess: (result: any) => {
                // 模拟成功回调
                setTimeout(() => {
                    const request = mockIDBFactory.open.mock.results[0]?.value;
                    if (request && request.onsuccess) {
                        request.result = result;
                        request.onsuccess({ target: { result } });
                    }
                }, 0);
            },
            simulateError: (error: DOMException) => {
                // 模拟错误回调
                setTimeout(() => {
                    const request = mockIDBFactory.open.mock.results[0]?.value;
                    if (request && request.onerror) {
                        request.onerror({ target: { error } });
                    }
                }, 0);
            }
        };
    }

    /**
     * 创建Cookie模拟
     */
    static mockCookies(initialCookies: string = ''): {
        getCookie: (name: string) => string | null;
        setCookie: (name: string, value: string, options?: any) => void;
        deleteCookie: (name: string) => void;
        getAllCookies: () => Record<string, string>;
    } {
        let cookieString = initialCookies;

        Object.defineProperty(document, 'cookie', {
            get: () => cookieString,
            set: (value: string) => {
                const [nameValue] = value.split(';');
                const [name, val] = nameValue.split('=');

                if (val === '' || val === 'deleted') {
                    // 删除cookie
                    const cookies = cookieString.split('; ').filter(c => !c.startsWith(name + '='));
                    cookieString = cookies.join('; ');
                } else {
                    // 添加或更新cookie
                    const cookies = cookieString.split('; ').filter(c => !c.startsWith(name + '='));
                    cookies.push(nameValue);
                    cookieString = cookies.filter(Boolean).join('; ');
                }
            }
        });

        return {
            getCookie: (name: string) => {
                const match = cookieString.match(new RegExp('(^| )' + name + '=([^;]+)'));
                return match ? match[2] : null;
            },
            setCookie: (name: string, value: string, options: any = {}) => {
                let cookie = `${name}=${value}`;
                if (options.expires) cookie += `; expires=${options.expires}`;
                if (options.path) cookie += `; path=${options.path}`;
                if (options.domain) cookie += `; domain=${options.domain}`;
                if (options.secure) cookie += '; secure';
                if (options.httpOnly) cookie += '; httpOnly';
                document.cookie = cookie;
            },
            deleteCookie: (name: string) => {
                document.cookie = `${name}=deleted; expires=Thu, 01 Jan 1970 00:00:00 GMT`;
            },
            getAllCookies: () => {
                const cookies: Record<string, string> = {};
                cookieString.split('; ').forEach(cookie => {
                    const [name, value] = cookie.split('=');
                    if (name && value) {
                        cookies[name] = value;
                    }
                });
                return cookies;
            }
        };
    }

    /**
     * 恢复原始存储API
     */
    static restoreStorage(): void {
        // 恢复localStorage
        delete (window as any).localStorage;
        Object.defineProperty(window, 'localStorage', {
            value: {
                getItem: () => null,
                setItem: () => { },
                removeItem: () => { },
                clear: () => { },
                length: 0,
                key: () => null
            },
            writable: true
        });

        // 恢复sessionStorage
        delete (window as any).sessionStorage;
        Object.defineProperty(window, 'sessionStorage', {
            value: {
                getItem: () => null,
                setItem: () => { },
                removeItem: () => { },
                clear: () => { },
                length: 0,
                key: () => null
            },
            writable: true
        });
    }

    /**
     * 创建存储事件模拟
     */
    static mockStorageEvents(): {
        triggerStorageEvent: (key: string, oldValue: string | null, newValue: string | null) => void;
        getStorageEventListeners: () => EventListener[];
    } {
        const listeners: EventListener[] = [];
        const originalAddEventListener = window.addEventListener;

        window.addEventListener = jest.fn((type: string, listener: EventListener) => {
            if (type === 'storage') {
                listeners.push(listener);
            }
            return originalAddEventListener.call(window, type, listener);
        });

        return {
            triggerStorageEvent: (key: string, oldValue: string | null, newValue: string | null) => {
                const event = new StorageEvent('storage', {
                    key,
                    oldValue,
                    newValue,
                    url: window.location.href,
                    storageArea: window.localStorage
                });

                listeners.forEach(listener => {
                    if (typeof listener === 'function') {
                        listener(event);
                    } else if (listener && typeof listener.handleEvent === 'function') {
                        listener.handleEvent(event);
                    }
                });
            },
            getStorageEventListeners: () => [...listeners]
        };
    }

    /**
     * 测试存储性能
     */
    static testStoragePerformance(storage: Storage, iterations: number = 1000): {
        writeTime: number;
        readTime: number;
        deleteTime: number;
    } {
        const testKey = 'performance-test-key';
        const testValue = 'performance-test-value';

        // 测试写入性能
        const writeStart = performance.now();
        for (let i = 0; i < iterations; i++) {
            storage.setItem(`${testKey}-${i}`, testValue);
        }
        const writeTime = performance.now() - writeStart;

        // 测试读取性能
        const readStart = performance.now();
        for (let i = 0; i < iterations; i++) {
            storage.getItem(`${testKey}-${i}`);
        }
        const readTime = performance.now() - readStart;

        // 测试删除性能
        const deleteStart = performance.now();
        for (let i = 0; i < iterations; i++) {
            storage.removeItem(`${testKey}-${i}`);
        }
        const deleteTime = performance.now() - deleteStart;

        return {
            writeTime,
            readTime,
            deleteTime
        };
    }
}