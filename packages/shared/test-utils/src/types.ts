/**
 * @fileoverview 测试工具类型定义
 * @description 提供测试工具相关的类型定义
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

/**
 * 测试环境类型
 */
export type TestEnvironment = 'jsdom' | 'node' | 'browser';

/**
 * 测试配置
 */
export interface TestConfig {
    /** 测试环境 */
    environment?: TestEnvironment;
    /** 超时时间 */
    timeout?: number;
    /** 是否启用调试 */
    debug?: boolean;
    /** 测试数据目录 */
    testDataDir?: string;
    /** 模拟数据 */
    mocks?: Record<string, any>;
    /** 全局设置 */
    globals?: Record<string, any>;
}

/**
 * 测试工具接口
 */
export interface TestUtils {
    /** 创建模拟函数 */
    createMock<T extends (...args: any[]) => any>(implementation?: T): jest.MockedFunction<T>;
    /** 创建模拟对象 */
    createMockObject<T>(obj: T): jest.Mocked<T>;
    /** 等待异步操作 */
    waitFor(condition: () => boolean | Promise<boolean>, timeout?: number): Promise<void>;
    /** 延迟执行 */
    delay(ms: number): Promise<void>;
    /** 创建测试容器 */
    createContainer(id?: string): HTMLElement;
    /** 清理测试容器 */
    cleanupContainer(container: HTMLElement): void;
    /** 触发事件 */
    fireEvent(element: Element, event: Event): void;
    /** 查找元素 */
    findElement(selector: string, container?: Element): Element | null;
    /** 查找所有元素 */
    findAllElements(selector: string, container?: Element): Element[];
    /** 获取元素文本 */
    getElementText(element: Element): string;
    /** 设置元素属性 */
    setElementAttribute(element: Element, name: string, value: string): void;
    /** 模拟用户输入 */
    simulateUserInput(element: HTMLInputElement, value: string): void;
    /** 模拟点击事件 */
    simulateClick(element: Element): void;
    /** 模拟键盘事件 */
    simulateKeyboard(element: Element, key: string, options?: KeyboardEventInit): void;
    /** 模拟鼠标事件 */
    simulateMouse(element: Element, type: string, options?: MouseEventInit): void;
}

/**
 * 性能测试结果
 */
export interface PerformanceTestResult {
    name: string;
    iterations: number;
    totalTime: number;
    averageTime: number;
    minTime: number;
    maxTime: number;
}

/**
 * 内存使用信息
 */
export interface MemoryUsage {
    used: number;
    total: number;
}

/**
 * 执行时间测量结果
 */
export interface ExecutionTimeResult<T> {
    result: T;
    time: number;
}