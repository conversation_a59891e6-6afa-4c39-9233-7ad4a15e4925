/**
 * @fileoverview 异步相关的测试匹配器
 * @description 提供异步操作相关的测试断言
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

/**
 * 检查函数在指定时间内被调用
 */
export async function toHaveBeenCalledWithin(this: jest.MatcherContext, received: jest.MockedFunction<any>, timeout: number) {
    const startTime = Date.now();

    while (Date.now() - startTime < timeout) {
        if (received.mock.calls.length > 0) {
            return {
                message: () => `期望函数在 ${timeout}ms 内不被调用，但它被调用了`,
                pass: true
            };
        }
        await new Promise(resolve => setTimeout(resolve, 10));
    }

    return {
        message: () => `期望函数在 ${timeout}ms 内被调用，但它没有被调用`,
        pass: false
    };
}

/**
 * 检查 Promise 在指定时间内解决
 */
export async function toResolveWithin(this: jest.MatcherContext, received: Promise<any>, timeout: number) {
    try {
        await Promise.race([
            received,
            new Promise((_, reject) =>
                setTimeout(() => reject(new Error('超时')), timeout)
            )
        ]);

        return {
            message: () => `期望 Promise 在 ${timeout}ms 内不解决，但它解决了`,
            pass: true
        };
    } catch (error) {
        if (error instanceof Error && error.message === '超时') {
            return {
                message: () => `期望 Promise 在 ${timeout}ms 内解决，但它超时了`,
                pass: false
            };
        }

        return {
            message: () => `期望 Promise 在 ${timeout}ms 内解决，但它被拒绝了: ${error}`,
            pass: false
        };
    }
}

/**
 * 检查 Promise 在指定时间内拒绝
 */
export async function toRejectWithin(this: jest.MatcherContext, received: Promise<any>, timeout: number) {
    try {
        await Promise.race([
            received.then(() => { throw new Error('Promise 解决了'); }),
            new Promise((_, reject) =>
                setTimeout(() => reject(new Error('超时')), timeout)
            )
        ]);

        return {
            message: () => `期望 Promise 在 ${timeout}ms 内不拒绝，但它拒绝了`,
            pass: true
        };
    } catch (error) {
        if (error instanceof Error && error.message === '超时') {
            return {
                message: () => `期望 Promise 在 ${timeout}ms 内拒绝，但它超时了`,
                pass: false
            };
        }

        if (error instanceof Error && error.message === 'Promise 解决了') {
            return {
                message: () => `期望 Promise 在 ${timeout}ms 内拒绝，但它解决了`,
                pass: false
            };
        }

        return {
            message: () => `期望 Promise 在 ${timeout}ms 内不拒绝，但它拒绝了`,
            pass: true
        };
    }
}