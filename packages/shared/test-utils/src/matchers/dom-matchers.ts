/**
 * @fileoverview DOM 相关的测试匹配器
 * @description 提供 DOM 元素相关的测试断言
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

/**
 * 检查元素是否在 DOM 中
 */
export function toBeInDOM(this: jest.MatcherContext, received: Element) {
    const pass = document.body.contains(received);

    if (pass) {
        return {
            message: () => `期望元素不在 DOM 中，但它在`,
            pass: true
        };
    } else {
        return {
            message: () => `期望元素在 DOM 中，但它不在`,
            pass: false
        };
    }
}

/**
 * 检查元素是否可见
 */
export function toBeVisible(this: jest.MatcherContext, received: Element) {
    const style = window.getComputedStyle(received);
    const pass = style.display !== 'none' &&
        style.visibility !== 'hidden' &&
        style.opacity !== '0';

    if (pass) {
        return {
            message: () => `期望元素不可见，但它可见`,
            pass: true
        };
    } else {
        return {
            message: () => `期望元素可见，但它不可见`,
            pass: false
        };
    }
}

/**
 * 检查元素是否隐藏
 */
export function toBeHidden(this: jest.MatcherContext, received: Element) {
    const style = window.getComputedStyle(received);
    const pass = style.display === 'none' ||
        style.visibility === 'hidden' ||
        style.opacity === '0';

    if (pass) {
        return {
            message: () => `期望元素不隐藏，但它隐藏`,
            pass: true
        };
    } else {
        return {
            message: () => `期望元素隐藏，但它不隐藏`,
            pass: false
        };
    }
}

/**
 * 检查元素文本内容
 */
export function toHaveTextContent(this: jest.MatcherContext, received: Element, expected: string) {
    const actualText = received.textContent || '';
    const pass = actualText.includes(expected);

    if (pass) {
        return {
            message: () => `期望元素不包含文本 "${expected}"，但它包含`,
            pass: true
        };
    } else {
        return {
            message: () => `期望元素包含文本 "${expected}"，但实际文本是 "${actualText}"`,
            pass: false
        };
    }
}

/**
 * 检查元素属性
 */
export function toHaveAttribute(this: jest.MatcherContext, received: Element, attribute: string, value?: string) {
    const hasAttribute = received.hasAttribute(attribute);

    if (!hasAttribute) {
        return {
            message: () => `期望元素有属性 "${attribute}"，但它没有`,
            pass: false
        };
    }

    if (value !== undefined) {
        const actualValue = received.getAttribute(attribute);
        const pass = actualValue === value;

        if (pass) {
            return {
                message: () => `期望元素属性 "${attribute}" 不等于 "${value}"，但它等于`,
                pass: true
            };
        } else {
            return {
                message: () => `期望元素属性 "${attribute}" 等于 "${value}"，但实际值是 "${actualValue}"`,
                pass: false
            };
        }
    }

    return {
        message: () => `期望元素没有属性 "${attribute}"，但它有`,
        pass: true
    };
}

/**
 * 检查元素类名
 */
export function toHaveClass(this: jest.MatcherContext, received: Element, className: string) {
    const pass = received.classList.contains(className);

    if (pass) {
        return {
            message: () => `期望元素不包含类名 "${className}"，但它包含`,
            pass: true
        };
    } else {
        return {
            message: () => `期望元素包含类名 "${className}"，但它不包含`,
            pass: false
        };
    }
}

/**
 * 检查元素样式
 */
export function toHaveStyle(this: jest.MatcherContext, received: Element, style: string | Record<string, any>) {
    const computedStyle = window.getComputedStyle(received);

    if (typeof style === 'string') {
        // 解析样式字符串
        const styles = style.split(';').reduce((acc, rule) => {
            const [property, value] = rule.split(':').map(s => s.trim());
            if (property && value) {
                acc[property] = value;
            }
            return acc;
        }, {} as Record<string, string>);

        style = styles;
    }

    const failures: string[] = [];

    Object.entries(style).forEach(([property, expectedValue]) => {
        const actualValue = computedStyle.getPropertyValue(property);
        if (actualValue !== expectedValue) {
            failures.push(`${property}: 期望 "${expectedValue}"，实际 "${actualValue}"`);
        }
    });

    const pass = failures.length === 0;

    if (pass) {
        return {
            message: () => `期望元素不具有指定样式，但它具有`,
            pass: true
        };
    } else {
        return {
            message: () => `期望元素具有指定样式，但有以下差异：\n${failures.join('\n')}`,
            pass: false
        };
    }
}