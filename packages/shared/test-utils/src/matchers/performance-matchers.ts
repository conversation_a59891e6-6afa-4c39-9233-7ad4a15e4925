/**
 * @fileoverview 性能相关的测试匹配器
 * @description 提供性能测试相关的断言
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

/**
 * 性能阈值接口
 */
export interface PerformanceThresholds {
    /** 最大执行时间（毫秒） */
    maxExecutionTime?: number;
    /** 最大内存使用（字节） */
    maxMemoryUsage?: number;
    /** 最小成功率 */
    minSuccessRate?: number;
}

/**
 * 检查性能指标是否在合理范围内
 */
export function toHaveReasonablePerformance(this: jest.MatcherContext, received: any, thresholds: PerformanceThresholds) {
    const errors: string[] = [];

    if (typeof received !== 'object' || received === null) {
        errors.push('性能数据必须是对象');
        return {
            message: () => `期望接收到性能对象，但接收到 ${typeof received}`,
            pass: false
        };
    }

    // 检查执行时间
    if (thresholds.maxExecutionTime && received.executionTime) {
        if (received.executionTime > thresholds.maxExecutionTime) {
            errors.push(`执行时间超出阈值: ${received.executionTime}ms > ${thresholds.maxExecutionTime}ms`);
        }
    }

    // 检查内存使用
    if (thresholds.maxMemoryUsage && received.memoryUsage) {
        if (received.memoryUsage > thresholds.maxMemoryUsage) {
            errors.push(`内存使用超出阈值: ${received.memoryUsage} bytes > ${thresholds.maxMemoryUsage} bytes`);
        }
    }

    // 检查成功率
    if (thresholds.minSuccessRate && received.successRate !== undefined) {
        if (received.successRate < thresholds.minSuccessRate) {
            errors.push(`成功率低于阈值: ${received.successRate} < ${thresholds.minSuccessRate}`);
        }
    }

    const pass = errors.length === 0;

    if (pass) {
        return {
            message: () => `期望性能不在合理范围内，但它在`,
            pass: true
        };
    } else {
        return {
            message: () => `期望性能在合理范围内，但有以下问题：\n${errors.join('\n')}`,
            pass: false
        };
    }
}