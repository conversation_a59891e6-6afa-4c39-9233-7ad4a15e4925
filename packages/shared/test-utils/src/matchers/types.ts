/**
 * @fileoverview 测试匹配器类型定义
 * @description 提供测试匹配器相关的类型定义
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

import type { PerformanceThresholds } from './performance-matchers';

/**
 * 扩展 Jest 匹配器类型
 */
declare global {
    namespace jest {
        interface Matchers<R> {
            /** 断言元素存在 */
            toBeInDOM(): R;
            /** 断言元素可见 */
            toBeVisible(): R;
            /** 断言元素隐藏 */
            toBeHidden(): R;
            /** 断言元素有特定文本 */
            toHaveTextContent(text: string): R;
            /** 断言元素有特定属性 */
            toHaveAttribute(attribute: string, value?: string): R;
            /** 断言元素有特定类名 */
            toHaveClass(className: string): R;
            /** 断言元素有特定样式 */
            toHaveStyle(style: string | Record<string, any>): R;
            /** 断言函数在指定时间内被调用 */
            toHaveBeenCalledWithin(timeout: number): Promise<R>;
            /** 断言对象有特定结构 */
            toMatchStructure(structure: any): R;
            /** 断言数组按特定顺序排列 */
            toBeInOrder(compareFn?: (a: any, b: any) => number): R;
            /** 断言Promise在指定时间内解决 */
            toResolveWithin(timeout: number): Promise<R>;
            /** 断言Promise在指定时间内拒绝 */
            toRejectWithin(timeout: number): Promise<R>;
            /** 断言对象是有效的微应用配置 */
            toBeValidMicroAppConfig(): R;
            /** 断言对象是有效的适配器实例 */
            toBeValidAdapterInstance(): R;
            /** 断言对象是有效的构建结果 */
            toBeValidBuildResult(): R;
            /** 断言性能指标在合理范围内 */
            toHaveReasonablePerformance(thresholds: PerformanceThresholds): R;
        }
    }
}

// 导出性能阈值类型
export type { PerformanceThresholds } from './performance-matchers';
