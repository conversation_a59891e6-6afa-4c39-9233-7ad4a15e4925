/**
 * @fileoverview 数据结构相关的测试匹配器
 * @description 提供数据结构和对象相关的测试断言
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

/**
 * 检查对象结构
 */
export function toMatchStructure(this: jest.MatcherContext, received: any, structure: any) {
    function checkStructure(obj: any, struct: any, path: string = ''): string[] {
        const errors: string[] = [];

        if (typeof struct !== 'object' || struct === null) {
            return errors;
        }

        Object.keys(struct).forEach(key => {
            const currentPath = path ? `${path}.${key}` : key;

            if (!(key in obj)) {
                errors.push(`缺少属性: ${currentPath}`);
                return;
            }

            const expectedType = typeof struct[key];
            const actualType = typeof obj[key];

            if (expectedType !== actualType) {
                errors.push(`类型不匹配 ${currentPath}: 期望 ${expectedType}，实际 ${actualType}`);
                return;
            }

            if (expectedType === 'object' && struct[key] !== null && obj[key] !== null) {
                errors.push(...checkStructure(obj[key], struct[key], currentPath));
            }
        });

        return errors;
    }

    const errors = checkStructure(received, structure);
    const pass = errors.length === 0;

    if (pass) {
        return {
            message: () => `期望对象不匹配结构，但它匹配`,
            pass: true
        };
    } else {
        return {
            message: () => `期望对象匹配结构，但有以下错误：\n${errors.join('\n')}`,
            pass: false
        };
    }
}

/**
 * 检查数组顺序
 */
export function toBeInOrder(this: jest.MatcherContext, received: any[], compareFn?: (a: any, b: any) => number) {
    if (!Array.isArray(received)) {
        return {
            message: () => `期望接收到数组，但接收到 ${typeof received}`,
            pass: false
        };
    }

    const defaultCompareFn = (a: any, b: any) => {
        if (a < b) return -1;
        if (a > b) return 1;
        return 0;
    };

    const compare = compareFn || defaultCompareFn;

    for (let i = 1; i < received.length; i++) {
        if (compare(received[i - 1], received[i]) > 0) {
            return {
                message: () => `期望数组按顺序排列，但在索引 ${i - 1} 和 ${i} 处顺序错误`,
                pass: false
            };
        }
    }

    return {
        message: () => `期望数组不按顺序排列，但它按顺序排列`,
        pass: true
    };
}