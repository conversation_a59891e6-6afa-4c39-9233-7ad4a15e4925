/**
 * @fileoverview 微核心特定的测试匹配器
 * @description 提供微核心项目特定的测试断言
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

/**
 * 检查是否为有效的微应用配置
 */
export function toBeValidMicroAppConfig(this: jest.MatcherContext, received: any) {
    const errors: string[] = [];

    if (typeof received !== 'object' || received === null) {
        errors.push('配置必须是对象');
    } else {
        if (!received.name || typeof received.name !== 'string') {
            errors.push('缺少或无效的 name 属性');
        }

        if (!received.entry || typeof received.entry !== 'string') {
            errors.push('缺少或无效的 entry 属性');
        }

        if (received.container && typeof received.container !== 'string' && !(received.container instanceof HTMLElement)) {
            errors.push('无效的 container 属性');
        }

        if (received.props && typeof received.props !== 'object') {
            errors.push('无效的 props 属性');
        }
    }

    const pass = errors.length === 0;

    if (pass) {
        return {
            message: () => `期望不是有效的微应用配置，但它是`,
            pass: true
        };
    } else {
        return {
            message: () => `期望是有效的微应用配置，但有以下错误：\n${errors.join('\n')}`,
            pass: false
        };
    }
}

/**
 * 检查是否为有效的适配器实例
 */
export function toBeValidAdapterInstance(this: jest.MatcherContext, received: any) {
    const errors: string[] = [];

    if (typeof received !== 'object' || received === null) {
        errors.push('适配器实例必须是对象');
    } else {
        if (!received.name || typeof received.name !== 'string') {
            errors.push('缺少或无效的 name 属性');
        }

        if (!received.version || typeof received.version !== 'string') {
            errors.push('缺少或无效的 version 属性');
        }

        if (!received.framework || typeof received.framework !== 'string') {
            errors.push('缺少或无效的 framework 属性');
        }

        if (typeof received.load !== 'function') {
            errors.push('缺少 load 方法');
        }

        if (typeof received.mount !== 'function') {
            errors.push('缺少 mount 方法');
        }

        if (typeof received.unmount !== 'function') {
            errors.push('缺少 unmount 方法');
        }
    }

    const pass = errors.length === 0;

    if (pass) {
        return {
            message: () => `期望不是有效的适配器实例，但它是`,
            pass: true
        };
    } else {
        return {
            message: () => `期望是有效的适配器实例，但有以下错误：\n${errors.join('\n')}`,
            pass: false
        };
    }
}

/**
 * 检查是否为有效的构建结果
 */
export function toBeValidBuildResult(this: jest.MatcherContext, received: any) {
    const errors: string[] = [];

    if (typeof received !== 'object' || received === null) {
        errors.push('构建结果必须是对象');
    } else {
        if (typeof received.success !== 'boolean') {
            errors.push('缺少或无效的 success 属性');
        }

        if (received.buildTime && typeof received.buildTime !== 'string') {
            errors.push('无效的 buildTime 属性');
        }

        if (received.outputSize && typeof received.outputSize !== 'string') {
            errors.push('无效的 outputSize 属性');
        }

        if (received.outputs && !Array.isArray(received.outputs)) {
            errors.push('无效的 outputs 属性');
        }

        if (received.assets && !Array.isArray(received.assets)) {
            errors.push('无效的 assets 属性');
        }

        if (received.errors && !Array.isArray(received.errors)) {
            errors.push('无效的 errors 属性');
        }
    }

    const pass = errors.length === 0;

    if (pass) {
        return {
            message: () => `期望不是有效的构建结果，但它是`,
            pass: true
        };
    } else {
        return {
            message: () => `期望是有效的构建结果，但有以下错误：\n${errors.join('\n')}`,
            pass: false
        };
    }
}