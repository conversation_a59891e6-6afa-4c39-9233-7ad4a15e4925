/**
 * @fileoverview 网络模拟工具
 * @description 提供网络请求模拟功能
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

/**
 * 网络模拟工具
 */
export class NetworkMockUtils {
    private static originalFetch: typeof fetch;
    private static mockResponses: Map<string, any> = new Map();

    /**
     * 模拟网络请求
     */
    static mockFetch(responses: Record<string, any>): void {
        if (!this.originalFetch) {
            this.originalFetch = global.fetch;
        }

        // 存储模拟响应
        Object.entries(responses).forEach(([url, response]) => {
            this.mockResponses.set(url, response);
        });

        global.fetch = jest.fn((url: string, options?: RequestInit) => {
            const response = this.mockResponses.get(url);
            if (response) {
                return Promise.resolve({
                    ok: true,
                    status: 200,
                    statusText: 'OK',
                    headers: new Headers(),
                    url,
                    json: () => Promise.resolve(response),
                    text: () => Promise.resolve(JSON.stringify(response)),
                    blob: () => Promise.resolve(new Blob([JSON.stringify(response)])),
                    arrayBuffer: () => Promise.resolve(new ArrayBuffer(0)),
                    clone: () => this.createMockResponse(response)
                } as Response);
            }
            return Promise.reject(new Error(`未找到模拟响应: ${url}`));
        });
    }

    /**
     * 模拟网络延迟
     */
    static mockNetworkDelay(delay: number): void {
        if (!this.originalFetch) {
            this.originalFetch = global.fetch;
        }

        global.fetch = jest.fn(async (...args) => {
            await new Promise(resolve => setTimeout(resolve, delay));
            return this.originalFetch(...args);
        });
    }

    /**
     * 模拟网络错误
     */
    static mockNetworkError(errorMessage: string = '网络错误'): void {
        global.fetch = jest.fn(() => Promise.reject(new Error(errorMessage)));
    }

    /**
     * 模拟特定状态码响应
     */
    static mockStatusCode(url: string, statusCode: number, statusText?: string): void {
        this.mockResponses.set(url, {
            status: statusCode,
            statusText: statusText || this.getStatusText(statusCode),
            ok: statusCode >= 200 && statusCode < 300
        });

        global.fetch = jest.fn((requestUrl: string) => {
            if (requestUrl === url) {
                const mockData = this.mockResponses.get(url);
                return Promise.resolve({
                    ...mockData,
                    json: () => Promise.resolve({}),
                    text: () => Promise.resolve(''),
                    blob: () => Promise.resolve(new Blob()),
                    arrayBuffer: () => Promise.resolve(new ArrayBuffer(0))
                } as Response);
            }
            return this.originalFetch(requestUrl);
        });
    }

    /**
     * 模拟XMLHttpRequest
     */
    static mockXHR(): void {
        const mockXHR = {
            open: jest.fn(),
            send: jest.fn(),
            setRequestHeader: jest.fn(),
            addEventListener: jest.fn(),
            removeEventListener: jest.fn(),
            abort: jest.fn(),
            readyState: 4,
            status: 200,
            statusText: 'OK',
            responseText: '{}',
            response: {},
            onreadystatechange: null,
            onload: null,
            onerror: null
        };

        (global as any).XMLHttpRequest = jest.fn(() => mockXHR);
    }

    /**
     * 恢复原始 fetch
     */
    static restoreFetch(): void {
        if (this.originalFetch) {
            global.fetch = this.originalFetch;
        }
        this.mockResponses.clear();
    }

    /**
     * 创建模拟响应对象
     */
    private static createMockResponse(data: any): Response {
        return {
            ok: true,
            status: 200,
            statusText: 'OK',
            headers: new Headers(),
            url: '',
            json: () => Promise.resolve(data),
            text: () => Promise.resolve(JSON.stringify(data)),
            blob: () => Promise.resolve(new Blob([JSON.stringify(data)])),
            arrayBuffer: () => Promise.resolve(new ArrayBuffer(0)),
            clone: () => this.createMockResponse(data)
        } as Response;
    }

    /**
     * 获取状态码对应的状态文本
     */
    private static getStatusText(statusCode: number): string {
        const statusTexts: Record<number, string> = {
            200: 'OK',
            201: 'Created',
            204: 'No Content',
            400: 'Bad Request',
            401: 'Unauthorized',
            403: 'Forbidden',
            404: 'Not Found',
            500: 'Internal Server Error',
            502: 'Bad Gateway',
            503: 'Service Unavailable'
        };
        return statusTexts[statusCode] || 'Unknown';
    }

    /**
     * 模拟WebSocket
     */
    static mockWebSocket(): void {
        const mockWebSocket = jest.fn().mockImplementation(() => ({
            send: jest.fn(),
            close: jest.fn(),
            addEventListener: jest.fn(),
            removeEventListener: jest.fn(),
            readyState: 1, // OPEN
            onopen: null,
            onmessage: null,
            onclose: null,
            onerror: null
        }));

        (global as any).WebSocket = mockWebSocket;
    }

    /**
     * 模拟EventSource
     */
    static mockEventSource(): void {
        const mockEventSource = jest.fn().mockImplementation(() => ({
            close: jest.fn(),
            addEventListener: jest.fn(),
            removeEventListener: jest.fn(),
            readyState: 1, // OPEN
            onopen: null,
            onmessage: null,
            onerror: null
        }));

        (global as any).EventSource = mockEventSource;
    }
}