/**
 * @fileoverview 存储模拟工具
 * @description 提供localStorage、sessionStorage等存储API的模拟功能
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

/**
 * 存储模拟工具
 */
export class StorageMockUtils {
    /**
     * 创建 localStorage 模拟
     */
    static createLocalStorageMock(): Storage {
        const store: Record<string, string> = {};

        return {
            getItem: jest.fn((key: string) => store[key] || null),
            setItem: jest.fn((key: string, value: string) => {
                store[key] = value;
            }),
            removeItem: jest.fn((key: string) => {
                delete store[key];
            }),
            clear: jest.fn(() => {
                Object.keys(store).forEach(key => delete store[key]);
            }),
            length: Object.keys(store).length,
            key: jest.fn((index: number) => {
                const keys = Object.keys(store);
                return keys[index] || null;
            })
        };
    }

    /**
     * 设置 localStorage 模拟
     */
    static mockLocalStorage(): void {
        Object.defineProperty(window, 'localStorage', {
            value: this.createLocalStorageMock(),
            writable: true
        });
    }

    /**
     * 设置 sessionStorage 模拟
     */
    static mockSessionStorage(): void {
        Object.defineProperty(window, 'sessionStorage', {
            value: this.createLocalStorageMock(),
            writable: true
        });
    }

    /**
     * 模拟 IndexedDB
     */
    static mockIndexedDB(): void {
        const mockIDBRequest = {
            result: null,
            error: null,
            onsuccess: null,
            onerror: null,
            addEventListener: jest.fn(),
            removeEventListener: jest.fn()
        };

        const mockIDBDatabase = {
            name: 'test-db',
            version: 1,
            objectStoreNames: [],
            transaction: jest.fn(() => mockIDBTransaction),
            createObjectStore: jest.fn(),
            deleteObjectStore: jest.fn(),
            close: jest.fn()
        };

        const mockIDBTransaction = {
            objectStore: jest.fn(() => mockIDBObjectStore),
            abort: jest.fn(),
            oncomplete: null,
            onerror: null,
            onabort: null
        };

        const mockIDBObjectStore = {
            add: jest.fn(() => mockIDBRequest),
            put: jest.fn(() => mockIDBRequest),
            get: jest.fn(() => mockIDBRequest),
            delete: jest.fn(() => mockIDBRequest),
            clear: jest.fn(() => mockIDBRequest),
            count: jest.fn(() => mockIDBRequest),
            openCursor: jest.fn(() => mockIDBRequest),
            createIndex: jest.fn(),
            deleteIndex: jest.fn(),
            index: jest.fn()
        };

        const mockIndexedDB = {
            open: jest.fn(() => {
                const request = { ...mockIDBRequest };
                setTimeout(() => {
                    request.result = mockIDBDatabase;
                    if (request.onsuccess) {
                        request.onsuccess({ target: request } as any);
                    }
                }, 0);
                return request;
            }),
            deleteDatabase: jest.fn(() => mockIDBRequest),
            cmp: jest.fn()
        };

        Object.defineProperty(window, 'indexedDB', {
            value: mockIndexedDB,
            writable: true
        });
    }

    /**
     * 模拟 Cookie
     */
    static mockCookie(): void {
        let cookieStore: Record<string, string> = {};

        Object.defineProperty(document, 'cookie', {
            get: jest.fn(() => {
                return Object.entries(cookieStore)
                    .map(([key, value]) => `${key}=${value}`)
                    .join('; ');
            }),
            set: jest.fn((value: string) => {
                const [keyValue] = value.split(';');
                const [key, val] = keyValue.split('=');
                if (key && val) {
                    cookieStore[key.trim()] = val.trim();
                }
            }),
            configurable: true
        });
    }

    /**
     * 模拟 Cache API
     */
    static mockCacheAPI(): void {
        const mockCache = {
            match: jest.fn(() => Promise.resolve(undefined)),
            matchAll: jest.fn(() => Promise.resolve([])),
            add: jest.fn(() => Promise.resolve()),
            addAll: jest.fn(() => Promise.resolve()),
            put: jest.fn(() => Promise.resolve()),
            delete: jest.fn(() => Promise.resolve(false)),
            keys: jest.fn(() => Promise.resolve([]))
        };

        const mockCaches = {
            open: jest.fn(() => Promise.resolve(mockCache)),
            match: jest.fn(() => Promise.resolve(undefined)),
            has: jest.fn(() => Promise.resolve(false)),
            delete: jest.fn(() => Promise.resolve(false)),
            keys: jest.fn(() => Promise.resolve([]))
        };

        Object.defineProperty(window, 'caches', {
            value: mockCaches,
            writable: true
        });
    }

    /**
     * 模拟 Web Storage API 事件
     */
    static mockStorageEvents(): void {
        const originalAddEventListener = window.addEventListener;
        const storageListeners: Array<(event: StorageEvent) => void> = [];

        window.addEventListener = jest.fn((type: string, listener: any) => {
            if (type === 'storage') {
                storageListeners.push(listener);
            } else {
                originalAddEventListener.call(window, type, listener);
            }
        });

        // 提供触发存储事件的方法
        (window as any).triggerStorageEvent = (key: string, oldValue: string | null, newValue: string | null) => {
            const event = new StorageEvent('storage', {
                key,
                oldValue,
                newValue,
                url: window.location.href,
                storageArea: window.localStorage
            });

            storageListeners.forEach(listener => listener(event));
        };
    }

    /**
     * 清理所有存储模拟
     */
    static clearAllMocks(): void {
        if (typeof window !== 'undefined') {
            // 清理 localStorage
            if (window.localStorage && window.localStorage.clear) {
                window.localStorage.clear();
            }

            // 清理 sessionStorage
            if (window.sessionStorage && window.sessionStorage.clear) {
                window.sessionStorage.clear();
            }

            // 清理 cookie
            if (document.cookie) {
                document.cookie.split(';').forEach(cookie => {
                    const eqPos = cookie.indexOf('=');
                    const name = eqPos > -1 ? cookie.substr(0, eqPos) : cookie;
                    document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`;
                });
            }
        }
    }

    /**
     * 创建存储数据快照
     */
    static createStorageSnapshot(): {
        localStorage: Record<string, string>;
        sessionStorage: Record<string, string>;
        cookies: string;
    } {
        const localStorageData: Record<string, string> = {};
        const sessionStorageData: Record<string, string> = {};

        // 获取 localStorage 数据
        if (typeof window !== 'undefined' && window.localStorage) {
            for (let i = 0; i < window.localStorage.length; i++) {
                const key = window.localStorage.key(i);
                if (key) {
                    localStorageData[key] = window.localStorage.getItem(key) || '';
                }
            }
        }

        // 获取 sessionStorage 数据
        if (typeof window !== 'undefined' && window.sessionStorage) {
            for (let i = 0; i < window.sessionStorage.length; i++) {
                const key = window.sessionStorage.key(i);
                if (key) {
                    sessionStorageData[key] = window.sessionStorage.getItem(key) || '';
                }
            }
        }

        return {
            localStorage: localStorageData,
            sessionStorage: sessionStorageData,
            cookies: typeof document !== 'undefined' ? document.cookie : ''
        };
    }

    /**
     * 恢复存储数据快照
     */
    static restoreStorageSnapshot(snapshot: {
        localStorage: Record<string, string>;
        sessionStorage: Record<string, string>;
        cookies: string;
    }): void {
        // 恢复 localStorage
        if (typeof window !== 'undefined' && window.localStorage) {
            window.localStorage.clear();
            Object.entries(snapshot.localStorage).forEach(([key, value]) => {
                window.localStorage.setItem(key, value);
            });
        }

        // 恢复 sessionStorage
        if (typeof window !== 'undefined' && window.sessionStorage) {
            window.sessionStorage.clear();
            Object.entries(snapshot.sessionStorage).forEach(([key, value]) => {
                window.sessionStorage.setItem(key, value);
            });
        }

        // 恢复 cookies（简化实现）
        if (typeof document !== 'undefined' && snapshot.cookies) {
            // 注意：这是一个简化的实现，实际使用中可能需要更复杂的cookie解析
            document.cookie = snapshot.cookies;
        }
    }
}