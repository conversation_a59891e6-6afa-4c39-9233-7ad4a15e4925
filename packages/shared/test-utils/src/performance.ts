/**
 * @fileoverview 性能测试工具
 * @description 提供性能测试和基准测试功能
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

import type { ExecutionTimeResult, MemoryUsage, PerformanceTestResult } from './types';

/**
 * 性能测试工具
 */
export class PerformanceTestUtils {
    /**
     * 测量函数执行时间
     */
    static async measureExecutionTime<T>(fn: () => T | Promise<T>): Promise<ExecutionTimeResult<T>> {
        const startTime = performance.now();
        const result = await fn();
        const endTime = performance.now();
        return {
            result,
            time: endTime - startTime
        };
    }

    /**
     * 测量内存使用
     */
    static measureMemoryUsage(): MemoryUsage | null {
        if (typeof window !== 'undefined' && 'memory' in performance) {
            const memory = (performance as any).memory;
            return {
                used: memory.usedJSHeapSize,
                total: memory.totalJSHeapSize
            };
        }
        return null;
    }

    /**
     * 性能基准测试
     */
    static async benchmark<T>(
        name: string,
        fn: () => T | Promise<T>,
        iterations: number = 100
    ): Promise<PerformanceTestResult> {
        const times: number[] = [];

        for (let i = 0; i < iterations; i++) {
            const { time } = await this.measureExecutionTime(fn);
            times.push(time);
        }

        const totalTime = times.reduce((sum, time) => sum + time, 0);
        const averageTime = totalTime / iterations;
        const minTime = Math.min(...times);
        const maxTime = Math.max(...times);

        return {
            name,
            iterations,
            totalTime,
            averageTime,
            minTime,
            maxTime
        };
    }

    /**
     * 比较两个函数的性能
     */
    static async compare<T>(
        name1: string,
        fn1: () => T | Promise<T>,
        name2: string,
        fn2: () => T | Promise<T>,
        iterations: number = 100
    ): Promise<{
        faster: string;
        slower: string;
        speedup: number;
        results: [PerformanceTestResult, PerformanceTestResult];
    }> {
        const [result1, result2] = await Promise.all([
            this.benchmark(name1, fn1, iterations),
            this.benchmark(name2, fn2, iterations)
        ]);

        const faster = result1.averageTime < result2.averageTime ? name1 : name2;
        const slower = result1.averageTime < result2.averageTime ? name2 : name1;
        const speedup = Math.max(result1.averageTime, result2.averageTime) /
            Math.min(result1.averageTime, result2.averageTime);

        return {
            faster,
            slower,
            speedup,
            results: [result1, result2]
        };
    }

    /**
     * 测量内存泄漏
     */
    static async measureMemoryLeak<T>(
        fn: () => T | Promise<T>,
        iterations: number = 10
    ): Promise<{
        initialMemory: MemoryUsage | null;
        finalMemory: MemoryUsage | null;
        memoryIncrease: number;
        averageIncreasePerIteration: number;
    }> {
        const initialMemory = this.measureMemoryUsage();

        for (let i = 0; i < iterations; i++) {
            await fn();
            // 强制垃圾回收（如果可用）
            if (global.gc) {
                global.gc();
            }
        }

        const finalMemory = this.measureMemoryUsage();
        const memoryIncrease = finalMemory && initialMemory ?
            finalMemory.used - initialMemory.used : 0;
        const averageIncreasePerIteration = memoryIncrease / iterations;

        return {
            initialMemory,
            finalMemory,
            memoryIncrease,
            averageIncreasePerIteration
        };
    }

    /**
     * 测试函数的稳定性（多次执行时间的方差）
     */
    static async measureStability<T>(
        fn: () => T | Promise<T>,
        iterations: number = 50
    ): Promise<{
        averageTime: number;
        variance: number;
        standardDeviation: number;
        coefficientOfVariation: number;
        isStable: boolean;
    }> {
        const times: number[] = [];

        for (let i = 0; i < iterations; i++) {
            const { time } = await this.measureExecutionTime(fn);
            times.push(time);
        }

        const averageTime = times.reduce((sum, time) => sum + time, 0) / iterations;
        const variance = times.reduce((sum, time) => sum + Math.pow(time - averageTime, 2), 0) / iterations;
        const standardDeviation = Math.sqrt(variance);
        const coefficientOfVariation = standardDeviation / averageTime;

        // 如果变异系数小于0.1，认为是稳定的
        const isStable = coefficientOfVariation < 0.1;

        return {
            averageTime,
            variance,
            standardDeviation,
            coefficientOfVariation,
            isStable
        };
    }

    /**
     * 测试并发性能
     */
    static async measureConcurrency<T>(
        fn: () => T | Promise<T>,
        concurrency: number = 10
    ): Promise<{
        totalTime: number;
        averageTime: number;
        throughput: number;
        results: T[];
    }> {
        const startTime = performance.now();

        const promises = Array.from({ length: concurrency }, () => fn());
        const results = await Promise.all(promises);

        const endTime = performance.now();
        const totalTime = endTime - startTime;
        const averageTime = totalTime / concurrency;
        const throughput = concurrency / (totalTime / 1000); // 每秒处理数

        return {
            totalTime,
            averageTime,
            throughput,
            results
        };
    }

    /**
     * 压力测试
     */
    static async stressTest<T>(
        fn: () => T | Promise<T>,
        duration: number = 5000, // 5秒
        maxConcurrency: number = 100
    ): Promise<{
        duration: number;
        totalExecutions: number;
        successfulExecutions: number;
        failedExecutions: number;
        averageExecutionTime: number;
        throughput: number;
        errors: Error[];
    }> {
        const startTime = performance.now();
        const endTime = startTime + duration;
        const errors: Error[] = [];
        const executionTimes: number[] = [];
        let totalExecutions = 0;
        let successfulExecutions = 0;
        let failedExecutions = 0;

        const executeFunction = async (): Promise<void> => {
            totalExecutions++;
            try {
                const { time } = await this.measureExecutionTime(fn);
                executionTimes.push(time);
                successfulExecutions++;
            } catch (error) {
                failedExecutions++;
                errors.push(error instanceof Error ? error : new Error(String(error)));
            }
        };

        const runningPromises = new Set<Promise<void>>();

        while (performance.now() < endTime) {
            // 控制并发数
            if (runningPromises.size < maxConcurrency) {
                const promise = executeFunction();
                runningPromises.add(promise);
                promise.finally(() => runningPromises.delete(promise));
            }

            // 短暂等待以避免阻塞
            await new Promise(resolve => setTimeout(resolve, 1));
        }

        // 等待所有正在执行的任务完成
        await Promise.allSettled(runningPromises);

        const actualDuration = performance.now() - startTime;
        const averageExecutionTime = executionTimes.length > 0 ?
            executionTimes.reduce((sum, time) => sum + time, 0) / executionTimes.length : 0;
        const throughput = successfulExecutions / (actualDuration / 1000);

        return {
            duration: actualDuration,
            totalExecutions,
            successfulExecutions,
            failedExecutions,
            averageExecutionTime,
            throughput,
            errors
        };
    }

    /**
     * 生成性能报告
     */
    static generateReport(results: PerformanceTestResult[]): string {
        const lines = [
            '性能测试报告',
            '='.repeat(50),
            ''
        ];

        results.forEach(result => {
            lines.push(`测试名称: ${result.name}`);
            lines.push(`迭代次数: ${result.iterations}`);
            lines.push(`总时间: ${result.totalTime.toFixed(2)}ms`);
            lines.push(`平均时间: ${result.averageTime.toFixed(2)}ms`);
            lines.push(`最小时间: ${result.minTime.toFixed(2)}ms`);
            lines.push(`最大时间: ${result.maxTime.toFixed(2)}ms`);
            lines.push('');
        });

        return lines.join('\n');
    }

    /**
     * 性能断言
     */
    static assertPerformance(
        actualTime: number,
        expectedMaxTime: number,
        message?: string
    ): void {
        const defaultMessage = `执行时间 ${actualTime.toFixed(2)}ms 应该小于 ${expectedMaxTime}ms`;
        expect(actualTime).toBeLessThanOrEqual(expectedMaxTime);
        if (message) {
            console.log(message);
        } else {
            console.log(defaultMessage);
        }
    }

    /**
     * 内存使用断言
     */
    static assertMemoryUsage(
        actualUsage: number,
        expectedMaxUsage: number,
        message?: string
    ): void {
        const defaultMessage = `内存使用 ${actualUsage} bytes 应该小于 ${expectedMaxUsage} bytes`;
        expect(actualUsage).toBeLessThanOrEqual(expectedMaxUsage);
        if (message) {
            console.log(message);
        } else {
            console.log(defaultMessage);
        }
    }

    /**
     * 吞吐量断言
     */
    static assertThroughput(
        actualThroughput: number,
        expectedMinThroughput: number,
        message?: string
    ): void {
        const defaultMessage = `吞吐量 ${actualThroughput.toFixed(2)} ops/s 应该大于 ${expectedMinThroughput} ops/s`;
        expect(actualThroughput).toBeGreaterThanOrEqual(expectedMinThroughput);
        if (message) {
            console.log(message);
        } else {
            console.log(defaultMessage);
        }
    }
}