/**
 * @fileoverview 微核心测试工具实现
 * @description 提供核心的测试工具功能
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

import type { TestConfig, TestUtils } from './types';

/**
 * 微核心测试工具实现
 */
export class MicroCoreTestUtils implements TestUtils {
    private config: TestConfig;
    private containers: Set<HTMLElement> = new Set();

    constructor(config: TestConfig = {}) {
        this.config = {
            environment: 'jsdom',
            timeout: 5000,
            debug: false,
            testDataDir: './test-data',
            mocks: {},
            globals: {},
            ...config
        };

        this.setupEnvironment();
    }

    /**
     * 设置测试环境
     */
    private setupEnvironment(): void {
        // 设置全局变量
        Object.entries(this.config.globals || {}).forEach(([key, value]) => {
            (global as any)[key] = value;
        });

        // 设置模拟对象
        Object.entries(this.config.mocks || {}).forEach(([key, value]) => {
            jest.mock(key, () => value);
        });

        // 设置环境特定配置
        if (this.config.environment === 'jsdom') {
            this.setupJSDOMEnvironment();
        } else if (this.config.environment === 'browser') {
            this.setupBrowserEnvironment();
        }
    }

    /**
     * 设置 JSDOM 环境
     */
    private setupJSDOMEnvironment(): void {
        // 模拟浏览器 API
        if (typeof window !== 'undefined') {
            // 模拟 localStorage
            const localStorageMock = {
                getItem: jest.fn(),
                setItem: jest.fn(),
                removeItem: jest.fn(),
                clear: jest.fn(),
                length: 0,
                key: jest.fn()
            };
            Object.defineProperty(window, 'localStorage', {
                value: localStorageMock
            });

            // 模拟 sessionStorage
            Object.defineProperty(window, 'sessionStorage', {
                value: localStorageMock
            });

            // 模拟 location
            Object.defineProperty(window, 'location', {
                value: {
                    href: 'http://localhost:3000',
                    origin: 'http://localhost:3000',
                    protocol: 'http:',
                    host: 'localhost:3000',
                    hostname: 'localhost',
                    port: '3000',
                    pathname: '/',
                    search: '',
                    hash: '',
                    assign: jest.fn(),
                    replace: jest.fn(),
                    reload: jest.fn()
                },
                writable: true
            });

            // 模拟 fetch
            if (!window.fetch) {
                window.fetch = jest.fn(() =>
                    Promise.resolve({
                        ok: true,
                        status: 200,
                        json: () => Promise.resolve({}),
                        text: () => Promise.resolve(''),
                        blob: () => Promise.resolve(new Blob()),
                        arrayBuffer: () => Promise.resolve(new ArrayBuffer(0))
                    } as Response)
                );
            }

            // 模拟 IntersectionObserver
            if (!window.IntersectionObserver) {
                window.IntersectionObserver = jest.fn(() => ({
                    observe: jest.fn(),
                    unobserve: jest.fn(),
                    disconnect: jest.fn()
                })) as any;
            }

            // 模拟 ResizeObserver
            if (!window.ResizeObserver) {
                window.ResizeObserver = jest.fn(() => ({
                    observe: jest.fn(),
                    unobserve: jest.fn(),
                    disconnect: jest.fn()
                })) as any;
            }

            // 模拟 MutationObserver
            if (!window.MutationObserver) {
                window.MutationObserver = jest.fn(() => ({
                    observe: jest.fn(),
                    disconnect: jest.fn(),
                    takeRecords: jest.fn(() => [])
                })) as any;
            }
        }
    }

    /**
     * 设置浏览器环境
     */
    private setupBrowserEnvironment(): void {
        // 浏览器环境特定设置
        console.log('设置浏览器测试环境');
    }

    /**
     * 创建模拟函数
     */
    createMock<T extends (...args: any[]) => any>(implementation?: T): jest.MockedFunction<T> {
        return jest.fn(implementation) as jest.MockedFunction<T>;
    }

    /**
     * 创建模拟对象
     */
    createMockObject<T>(obj: T): jest.Mocked<T> {
        const mock = {} as jest.Mocked<T>;

        Object.keys(obj as any).forEach(key => {
            const value = (obj as any)[key];
            if (typeof value === 'function') {
                (mock as any)[key] = jest.fn();
            } else if (typeof value === 'object' && value !== null) {
                (mock as any)[key] = this.createMockObject(value);
            } else {
                (mock as any)[key] = value;
            }
        });

        return mock;
    }

    /**
     * 等待条件满足
     */
    async waitFor(condition: () => boolean | Promise<boolean>, timeout: number = this.config.timeout!): Promise<void> {
        const startTime = Date.now();

        while (Date.now() - startTime < timeout) {
            try {
                const result = await condition();
                if (result) {
                    return;
                }
            } catch (error) {
                // 继续等待
            }

            await this.delay(50);
        }

        throw new Error(`等待条件超时 (${timeout}ms)`);
    }

    /**
     * 延迟执行
     */
    async delay(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 创建测试容器
     */
    createContainer(id?: string): HTMLElement {
        const container = document.createElement('div');
        if (id) {
            container.id = id;
        }
        container.setAttribute('data-testid', 'test-container');
        document.body.appendChild(container);
        this.containers.add(container);
        return container;
    }

    /**
     * 清理测试容器
     */
    cleanupContainer(container: HTMLElement): void {
        if (container.parentNode) {
            container.parentNode.removeChild(container);
        }
        this.containers.delete(container);
    }

    /**
     * 清理所有容器
     */
    cleanupAllContainers(): void {
        this.containers.forEach(container => {
            this.cleanupContainer(container);
        });
    }

    /**
     * 触发事件
     */
    fireEvent(element: Element, event: Event): void {
        element.dispatchEvent(event);
    }

    /**
     * 查找元素
     */
    findElement(selector: string, container: Element = document.body): Element | null {
        return container.querySelector(selector);
    }

    /**
     * 查找所有元素
     */
    findAllElements(selector: string, container: Element = document.body): Element[] {
        return Array.from(container.querySelectorAll(selector));
    }

    /**
     * 获取元素文本
     */
    getElementText(element: Element): string {
        return element.textContent || '';
    }

    /**
     * 设置元素属性
     */
    setElementAttribute(element: Element, name: string, value: string): void {
        element.setAttribute(name, value);
    }

    /**
     * 模拟用户输入
     */
    simulateUserInput(element: HTMLInputElement, value: string): void {
        element.value = value;
        this.fireEvent(element, new Event('input', { bubbles: true }));
        this.fireEvent(element, new Event('change', { bubbles: true }));
    }

    /**
     * 模拟点击事件
     */
    simulateClick(element: Element): void {
        this.fireEvent(element, new MouseEvent('click', {
            bubbles: true,
            cancelable: true,
            view: window
        }));
    }

    /**
     * 模拟键盘事件
     */
    simulateKeyboard(element: Element, key: string, options: KeyboardEventInit = {}): void {
        const event = new KeyboardEvent('keydown', {
            key,
            bubbles: true,
            cancelable: true,
            ...options
        });
        this.fireEvent(element, event);
    }

    /**
     * 模拟鼠标事件
     */
    simulateMouse(element: Element, type: string, options: MouseEventInit = {}): void {
        const event = new MouseEvent(type, {
            bubbles: true,
            cancelable: true,
            view: window,
            ...options
        });
        this.fireEvent(element, event);
    }

    /**
     * 生成模拟用户数据
     */
    static mockUserData(): any {
        return {
            id: this.randomNumber(1, 10000),
            name: this.randomString(8),
            email: `${this.randomString(6)}@example.com`,
            age: this.randomNumber(18, 80),
            active: this.randomBoolean()
        };
    }

    /**
     * 生成随机字符串
     */
    static randomString(length: number = 10): string {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }

    /**
     * 生成随机数字
     */
    static randomNumber(min: number = 0, max: number = 100): number {
        return Math.floor(Math.random() * (max - min + 1)) + min;
    }

    /**
     * 生成随机布尔值
     */
    static randomBoolean(): boolean {
        return Math.random() < 0.5;
    }

    /**
     * 销毁测试工具
     */
    destroy(): void {
        this.cleanupAllContainers();
    }
}