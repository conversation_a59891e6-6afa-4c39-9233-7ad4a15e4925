{"name": "@micro-core/eslint-config", "version": "0.1.0", "description": "Shared ESLint configuration for Micro-Core packages", "main": "base.js", "files": ["*.js"], "keywords": ["eslint", "config", "micro-core"], "author": "Echo <<EMAIL>>", "license": "MIT", "dependencies": {"@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-prettier": "^5.1.0"}, "peerDependencies": {"eslint": "^8.0.0", "prettier": "^3.0.0", "typescript": "^5.0.0"}, "repository": {"type": "git", "url": "https://github.com/echo008/micro-core.git", "directory": "packages/shared/eslint-config"}}