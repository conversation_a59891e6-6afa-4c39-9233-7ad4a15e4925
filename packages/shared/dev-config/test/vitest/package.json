{"name": "@micro-core/vitest-config", "version": "0.1.0", "description": "Vitest configuration for Micro-Core", "main": "index.js", "types": "index.d.ts", "files": ["*.js", "*.d.ts"], "scripts": {"test": "vitest run __tests__", "test:watch": "vitest __tests__"}, "keywords": ["vitest", "vitestconfig", "micro-core"], "author": "Echo <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/echo008/micro-core.git", "directory": "packages/shared/vitest-config"}, "publishConfig": {"access": "public"}, "dependencies": {"@vitest/coverage-v8": "^1.0.4", "jsdom": "^23.0.1"}, "peerDependencies": {"vitest": ">=1.0.0"}}