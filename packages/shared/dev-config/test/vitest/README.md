# @micro-core/vitest-config

Vitest configuration for Micro-Core projects.

## Installation

```bash
npm install --save-dev @micro-core/vitest-config
```

## Usage

### Basic Usage

In your `vitest.config.ts`:

```typescript
import { defineConfig } from 'vitest/config';
import { base } from '@micro-core/vitest-config';

export default defineConfig(base);
```

### Node.js Projects

```typescript
import { defineConfig } from 'vitest/config';
import { node } from '@micro-core/vitest-config';

export default defineConfig(node);
```

### React Projects

```typescript
import { defineConfig } from 'vitest/config';
import { react } from '@micro-core/vitest-config';

export default defineConfig(react);
```

### Library Projects

```typescript
import { defineConfig } from 'vitest/config';
import { library } from '@micro-core/vitest-config';

export default defineConfig(library);
```

## Configurations

### Base Configuration

- **Test Environment**: jsdom
- **Setup Files**: `./test/setup.ts`
- **Coverage Provider**: v8
- **Coverage Threshold**: 80% for all metrics
- **Module Aliases**: `@/` and `@micro-core/`

### Node Configuration

- **Test Environment**: node
- **No DOM setup**: Optimized for server-side testing

### React Configuration

- **Test Environment**: jsdom
- **Additional Setup**: `@testing-library/jest-dom`
- **React Testing**: Optimized for React component testing

### Library Configuration

- **Test Environment**: node
- **Enhanced Coverage**: Excludes test files from coverage
- **Library Testing**: Optimized for library development

## Features

- ✅ TypeScript support out of the box
- ✅ Module path mapping
- ✅ Coverage reporting with thresholds
- ✅ Multiple environment configurations
- ✅ Optimized for Micro-Core architecture
- ✅ Fast test execution with Vite

## License

MIT
