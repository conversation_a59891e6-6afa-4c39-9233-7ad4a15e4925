/**
 * @fileoverview Vitest configuration types for Micro-Core projects
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

import { UserConfig } from 'vitest/config';

/**
 * Vitest configuration object
 */
export interface VitestConfig extends UserConfig {}

/**
 * Base Vitest configuration
 */
export declare const base: VitestConfig;

/**
 * Node.js specific Vitest configuration
 */
export declare const node: VitestConfig;

/**
 * React specific Vitest configuration
 */
export declare const react: VitestConfig;

/**
 * Library specific Vitest configuration
 */
export declare const library: VitestConfig;
