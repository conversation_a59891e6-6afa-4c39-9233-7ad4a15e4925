/**
 * @fileoverview Vitest configuration tests
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

const { base, node, react, library } = require('../index');

describe('@micro-core/vitest-config', () => {
    describe('base configuration', () => {
        it('should have correct test environment', () => {
            expect(base.test.environment).toBe('jsdom');
        });

        it('should have globals enabled', () => {
            expect(base.test.globals).toBe(true);
        });

        it('should have setup files', () => {
            expect(base.test.setupFiles).toContain('./test/setup.ts');
        });

        it('should have coverage configuration', () => {
            expect(base.test.coverage.provider).toBe('v8');
            expect(base.test.coverage.reporter).toEqual(['text', 'json', 'html']);
        });

        it('should have coverage thresholds', () => {
            expect(base.test.coverage.thresholds.global.branches).toBe(80);
            expect(base.test.coverage.thresholds.global.functions).toBe(80);
            expect(base.test.coverage.thresholds.global.lines).toBe(80);
            expect(base.test.coverage.thresholds.global.statements).toBe(80);
        });

        it('should have coverage exclusions', () => {
            expect(base.test.coverage.exclude).toContain('node_modules/');
            expect(base.test.coverage.exclude).toContain('test/');
            expect(base.test.coverage.exclude).toContain('dist/');
        });

        it('should have resolve aliases', () => {
            expect(base.resolve.alias).toHaveProperty('@');
            expect(base.resolve.alias).toHaveProperty('@micro-core');
        });
    });

    describe('node configuration', () => {
        it('should extend base configuration', () => {
            expect(node.test.environment).toBe('node');
            expect(node.test.globals).toBe(true);
        });

        it('should not have setup files', () => {
            expect(node.test.setupFiles).toBeUndefined();
        });

        it('should have same coverage configuration', () => {
            expect(node.test.coverage.provider).toBe('v8');
            expect(node.test.coverage.thresholds).toEqual(base.test.coverage.thresholds);
        });
    });

    describe('react configuration', () => {
        it('should extend base configuration', () => {
            expect(react.test.environment).toBe('jsdom');
            expect(react.test.globals).toBe(true);
        });

        it('should have additional setup files', () => {
            expect(react.test.setupFiles).toContain('./test/setup.ts');
            expect(react.test.setupFiles).toContain('@testing-library/jest-dom');
        });
    });

    describe('library configuration', () => {
        it('should extend base configuration', () => {
            expect(library.test.environment).toBe('node');
            expect(library.test.globals).toBe(true);
        });

        it('should have enhanced coverage exclusions', () => {
            expect(library.test.coverage.exclude).toContain('**/*.test.ts');
            expect(library.test.coverage.exclude).toContain('**/*.spec.ts');
        });
    });

    describe('configuration validation', () => {
        it('should have valid configuration objects', () => {
            expect(typeof base).toBe('object');
            expect(typeof node).toBe('object');
            expect(typeof react).toBe('object');
            expect(typeof library).toBe('object');
        });

        it('should not have circular references', () => {
            expect(() => JSON.stringify(base)).not.toThrow();
            expect(() => JSON.stringify(node)).not.toThrow();
            expect(() => JSON.stringify(react)).not.toThrow();
            expect(() => JSON.stringify(library)).not.toThrow();
        });

        it('should have test configuration', () => {
            expect(base.test).toBeDefined();
            expect(node.test).toBeDefined();
            expect(react.test).toBeDefined();
            expect(library.test).toBeDefined();
        });
    });
});
