# @micro-core/jest-config

Jest configuration for Micro-Core projects.

## Installation

```bash
npm install --save-dev @micro-core/jest-config
```

## Usage

### Basic Usage

In your `jest.config.js`:

```javascript
const { base } = require('@micro-core/jest-config');

module.exports = {
  ...base,
  // Your custom configuration
};
```

### Node.js Projects

```javascript
const { node } = require('@micro-core/jest-config');

module.exports = {
  ...node,
  // Your custom configuration
};
```

### React Projects

```javascript
const { react } = require('@micro-core/jest-config');

module.exports = {
  ...react,
  // Your custom configuration
};
```

## Configurations

### Base Configuration

- **Test Environment**: jsdom
- **Setup Files**: `<rootDir>/test/setup.ts`
- **Transform**: TypeScript and JavaScript files
- **Module Mapping**: Support for `@/` and `@micro-core/` aliases
- **Coverage**: 80% threshold for all metrics

### Node Configuration

- **Test Environment**: node
- **No DOM setup**: Optimized for server-side testing

### React Configuration

- **Test Environment**: jsdom
- **Additional Setup**: `@testing-library/jest-dom`
- **React Testing**: Optimized for React component testing

## Features

- ✅ TypeScript support out of the box
- ✅ Module path mapping
- ✅ Coverage reporting with thresholds
- ✅ Multiple environment configurations
- ✅ Optimized for Micro-Core architecture

## License

MIT
