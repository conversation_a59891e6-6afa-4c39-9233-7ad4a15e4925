/**
 * @fileoverview 性能监控系统 - 重新导出模块化后的性能监控功能
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 * @deprecated 此文件已重构为模块化结构，请使用 ./performance/index
 */

// 重新导出新的模块化性能监控系统
export * from './performance/index';

// 向后兼容性警告
if (typeof console !== 'undefined' && console.warn) {
    console.warn(
        '[MicroCore] packages/shared/utils/src/monitor/performance.ts 已重构为模块化结构。' +
        '请更新导入路径为 "./performance/index" 或直接使用 "./performance"'
    );
}