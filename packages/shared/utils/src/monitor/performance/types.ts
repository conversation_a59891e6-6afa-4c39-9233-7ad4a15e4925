/**
 * @fileoverview 性能监控类型定义
 * @description 性能监控系统的所有类型定义
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

/**
 * 性能指标类型
 */
export enum MetricType {
    /** 计数器 */
    COUNTER = 'counter',
    /** 计量器 */
    GAUGE = 'gauge',
    /** 直方图 */
    HISTOGRAM = 'histogram',
    /** 计时器 */
    TIMER = 'timer'
}

/**
 * 性能指标
 */
export interface PerformanceMetric {
    /** 指标名称 */
    name: string;
    /** 指标类型 */
    type: MetricType;
    /** 指标值 */
    value: number;
    /** 指标单位 */
    unit?: string;
    /** 标签 */
    labels?: Record<string, string>;
    /** 时间戳 */
    timestamp: number;
    /** 描述 */
    description?: string;
}

/**
 * 性能监控配置
 */
export interface PerformanceMonitorConfig {
    /** 是否启用监控 */
    enabled?: boolean;
    /** 采样率 (0-1) */
    sampleRate?: number;
    /** 指标收集间隔（毫秒） */
    collectInterval?: number;
    /** 最大指标数量 */
    maxMetrics?: number;
    /** 是否自动收集浏览器性能指标 */
    collectBrowserMetrics?: boolean;
    /** 是否自动收集资源加载指标 */
    collectResourceMetrics?: boolean;
    /** 是否自动收集用户交互指标 */
    collectUserMetrics?: boolean;
    /** 自定义指标收集器 */
    customCollectors?: PerformanceCollector[];
}

/**
 * 性能收集器接口
 */
export interface PerformanceCollector {
    /** 收集器名称 */
    name: string;
    /** 收集指标 */
    collect(): PerformanceMetric[] | Promise<PerformanceMetric[]>;
    /** 是否启用 */
    enabled?: boolean;
}

/**
 * 性能报告器接口
 */
export interface PerformanceReporter {
    /** 报告指标 */
    report(metrics: PerformanceMetric[]): Promise<void>;
    /** 报告单个指标 */
    reportMetric(metric: PerformanceMetric): Promise<void>;
}

/**
 * 性能统计信息
 */
export interface PerformanceStats {
    /** 应用启动时间 */
    appStartTime: number;
    /** 页面加载时间 */
    pageLoadTime?: number;
    /** 首次内容绘制时间 */
    firstContentfulPaint?: number;
    /** 最大内容绘制时间 */
    largestContentfulPaint?: number;
    /** 首次输入延迟 */
    firstInputDelay?: number;
    /** 累积布局偏移 */
    cumulativeLayoutShift?: number;
    /** 内存使用情况 */
    memoryUsage?: {
        used: number;
        total: number;
        limit?: number;
    };
    /** 网络连接信息 */
    connection?: {
        effectiveType: string;
        downlink: number;
        rtt: number;
    };
}