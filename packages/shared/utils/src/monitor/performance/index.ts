/**
 * @fileoverview 性能监控系统主导出
 * @description 统一导出性能监控系统的所有功能
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

// 核心类型和枚举
export { MetricType } from './types';
export type {
    PerformanceCollector, PerformanceMetric,
    PerformanceMonitorConfig, PerformanceReporter,
    PerformanceStats
} from './types';

// 主要类
export { MicroCorePerformanceMonitor } from './performance-monitor';

// 收集器
export { ApplicationMetricsCollector } from './collectors/application-collector';
export { BrowserMetricsCollector } from './collectors/browser-collector';
export { SystemMetricsCollector } from './collectors/system-collector';

// 报告器
export { ConsolePerformanceReporter } from './reporters/console-reporter';
export { HttpPerformanceReporter } from './reporters/http-reporter';

// 工具函数
export {
    createDefaultPerformanceMonitor, createPerformanceTimer, globalPerformanceMonitor, performanceMonitor, recordPerformance, withPerformanceMonitoring
} from './utils';
