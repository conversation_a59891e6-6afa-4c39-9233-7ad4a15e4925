/**
 * @fileoverview 系统指标收集器
 * @description 收集系统级别的性能指标
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

import type { PerformanceCollector, PerformanceMetric } from '../types';
import { MetricType } from '../types';

/**
 * 系统指标收集器
 */
export class SystemMetricsCollector implements PerformanceCollector {
    name = 'system';

    collect(): PerformanceMetric[] {
        const metrics: PerformanceMetric[] = [];
        const now = Date.now();

        // 内存使用情况
        if (typeof window !== 'undefined' && 'memory' in performance) {
            const memory = (performance as any).memory;
            metrics.push({
                name: 'system.memory.used',
                type: MetricType.GAUGE,
                value: memory.usedJSHeapSize,
                unit: 'bytes',
                timestamp: now
            });

            metrics.push({
                name: 'system.memory.total',
                type: MetricType.GAUGE,
                value: memory.totalJSHeapSize,
                unit: 'bytes',
                timestamp: now
            });

            metrics.push({
                name: 'system.memory.limit',
                type: MetricType.GAUGE,
                value: memory.jsHeapSizeLimit,
                unit: 'bytes',
                timestamp: now
            });
        }

        // Node.js 环境内存信息
        if (typeof process !== 'undefined' && process.memoryUsage) {
            const memory = process.memoryUsage();
            metrics.push({
                name: 'system.memory.rss',
                type: MetricType.GAUGE,
                value: memory.rss,
                unit: 'bytes',
                timestamp: now
            });

            metrics.push({
                name: 'system.memory.heap.used',
                type: MetricType.GAUGE,
                value: memory.heapUsed,
                unit: 'bytes',
                timestamp: now
            });

            metrics.push({
                name: 'system.memory.heap.total',
                type: MetricType.GAUGE,
                value: memory.heapTotal,
                unit: 'bytes',
                timestamp: now
            });

            metrics.push({
                name: 'system.memory.external',
                type: MetricType.GAUGE,
                value: memory.external,
                unit: 'bytes',
                timestamp: now
            });
        }

        // 连接信息
        if (typeof navigator !== 'undefined' && 'connection' in navigator) {
            const connection = (navigator as any).connection;
            metrics.push({
                name: 'system.connection.downlink',
                type: MetricType.GAUGE,
                value: connection.downlink,
                unit: 'mbps',
                timestamp: now
            });

            metrics.push({
                name: 'system.connection.rtt',
                type: MetricType.GAUGE,
                value: connection.rtt,
                unit: 'ms',
                timestamp: now
            });

            metrics.push({
                name: 'system.connection.effective_type',
                type: MetricType.GAUGE,
                value: this.getConnectionTypeValue(connection.effectiveType),
                labels: {
                    type: connection.effectiveType
                },
                timestamp: now
            });
        }

        return metrics;
    }

    /**
     * 将连接类型转换为数值
     */
    private getConnectionTypeValue(type: string): number {
        const typeMap: Record<string, number> = {
            'slow-2g': 1,
            '2g': 2,
            '3g': 3,
            '4g': 4,
            '5g': 5
        };
        return typeMap[type] || 0;
    }
}