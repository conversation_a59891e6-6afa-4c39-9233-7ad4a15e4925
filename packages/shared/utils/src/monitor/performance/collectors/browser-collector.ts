/**
 * @fileoverview 浏览器指标收集器
 * @description 收集浏览器环境的性能指标
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

import type { PerformanceCollector, PerformanceMetric } from '../types';
import { MetricType } from '../types';

/**
 * 浏览器指标收集器
 */
export class BrowserMetricsCollector implements PerformanceCollector {
    name = 'browser';

    collect(): PerformanceMetric[] {
        const metrics: PerformanceMetric[] = [];
        const now = Date.now();

        if (typeof window === 'undefined') {
            return metrics;
        }

        // 页面可见性
        if ('visibilityState' in document) {
            metrics.push({
                name: 'browser.page.visible',
                type: MetricType.GAUGE,
                value: document.visibilityState === 'visible' ? 1 : 0,
                labels: {
                    state: document.visibilityState
                },
                timestamp: now,
                description: '页面可见性状态'
            });
        }

        // 在线状态
        if ('onLine' in navigator) {
            metrics.push({
                name: 'browser.online',
                type: MetricType.GAUGE,
                value: navigator.onLine ? 1 : 0,
                timestamp: now,
                description: '网络连接状态'
            });
        }

        // 设备像素比
        if ('devicePixelRatio' in window) {
            metrics.push({
                name: 'browser.device.pixel_ratio',
                type: MetricType.GAUGE,
                value: window.devicePixelRatio,
                timestamp: now,
                description: '设备像素比'
            });
        }

        // 视口尺寸
        metrics.push({
            name: 'browser.viewport.width',
            type: MetricType.GAUGE,
            value: window.innerWidth,
            unit: 'px',
            timestamp: now,
            description: '视口宽度'
        });

        metrics.push({
            name: 'browser.viewport.height',
            type: MetricType.GAUGE,
            value: window.innerHeight,
            unit: 'px',
            timestamp: now,
            description: '视口高度'
        });

        // 屏幕尺寸
        if ('screen' in window) {
            metrics.push({
                name: 'browser.screen.width',
                type: MetricType.GAUGE,
                value: screen.width,
                unit: 'px',
                timestamp: now,
                description: '屏幕宽度'
            });

            metrics.push({
                name: 'browser.screen.height',
                type: MetricType.GAUGE,
                value: screen.height,
                unit: 'px',
                timestamp: now,
                description: '屏幕高度'
            });
        }

        // 性能导航时间
        if (performance && performance.timing) {
            const timing = performance.timing;
            const navigationStart = timing.navigationStart;

            if (timing.loadEventEnd > 0) {
                metrics.push({
                    name: 'browser.navigation.load_time',
                    type: MetricType.TIMER,
                    value: timing.loadEventEnd - navigationStart,
                    unit: 'ms',
                    timestamp: now,
                    description: '页面加载时间'
                });
            }

            if (timing.domContentLoadedEventEnd > 0) {
                metrics.push({
                    name: 'browser.navigation.dom_ready_time',
                    type: MetricType.TIMER,
                    value: timing.domContentLoadedEventEnd - navigationStart,
                    unit: 'ms',
                    timestamp: now,
                    description: 'DOM就绪时间'
                });
            }
        }

        // 资源数量统计
        if (performance && performance.getEntriesByType) {
            const resources = performance.getEntriesByType('resource');
            const resourceTypes = this.groupResourcesByType(resources as PerformanceResourceTiming[]);

            Object.entries(resourceTypes).forEach(([type, count]) => {
                metrics.push({
                    name: 'browser.resources.count',
                    type: MetricType.GAUGE,
                    value: count,
                    labels: {
                        type
                    },
                    timestamp: now,
                    description: `${type}资源数量`
                });
            });
        }

        return metrics;
    }

    /**
     * 按类型分组资源
     */
    private groupResourcesByType(resources: PerformanceResourceTiming[]): Record<string, number> {
        const groups: Record<string, number> = {};

        resources.forEach(resource => {
            const type = this.getResourceType(resource.name);
            groups[type] = (groups[type] || 0) + 1;
        });

        return groups;
    }

    /**
     * 获取资源类型
     */
    private getResourceType(url: string): string {
        const ext = url.split('.').pop()?.toLowerCase();

        switch (ext) {
            case 'js':
            case 'mjs':
                return 'script';
            case 'css':
                return 'stylesheet';
            case 'png':
            case 'jpg':
            case 'jpeg':
            case 'gif':
            case 'svg':
            case 'webp':
                return 'image';
            case 'woff':
            case 'woff2':
            case 'eot':
            case 'ttf':
                return 'font';
            case 'json':
                return 'xhr';
            default:
                return 'other';
        }
    }
}