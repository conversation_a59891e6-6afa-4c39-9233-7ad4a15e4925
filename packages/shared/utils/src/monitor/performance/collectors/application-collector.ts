/**
 * @fileoverview 应用指标收集器
 * @description 收集应用级别的性能指标
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

import type { PerformanceCollector, PerformanceMetric } from '../types';
import { MetricType } from '../types';

/**
 * 应用指标收集器
 */
export class ApplicationMetricsCollector implements PerformanceCollector {
    name = 'application';

    constructor(
        private startTime: number,
        private getMetricsCount: () => number
    ) { }

    collect(): PerformanceMetric[] {
        const metrics: PerformanceMetric[] = [];
        const now = Date.now();

        // 应用运行时间
        metrics.push({
            name: 'app.uptime',
            type: MetricType.GAUGE,
            value: now - this.startTime,
            unit: 'ms',
            timestamp: now,
            description: '应用运行时间'
        });

        // 指标数量
        metrics.push({
            name: 'app.metrics.count',
            type: MetricType.GAUGE,
            value: this.getMetricsCount(),
            timestamp: now,
            description: '当前指标数量'
        });

        // 环境信息
        const environment = typeof window !== 'undefined' ? 'browser' : 'node';
        metrics.push({
            name: 'app.environment',
            type: MetricType.GAUGE,
            value: environment === 'browser' ? 1 : 0,
            labels: {
                environment
            },
            timestamp: now,
            description: '运行环境'
        });

        return metrics;
    }
}