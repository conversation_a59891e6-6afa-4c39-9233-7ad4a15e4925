/**
 * @fileoverview 性能监控工具函数
 * @description 提供性能监控相关的工具函数和装饰器
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

import { MicroCorePerformanceMonitor } from './performance-monitor';
import { ConsolePerformanceReporter } from './reporters/console-reporter';
import { HttpPerformanceReporter } from './reporters/http-reporter';
import type { MetricType, PerformanceMonitorConfig } from './types';

/**
 * 创建默认性能监控器
 */
export function createDefaultPerformanceMonitor(options: PerformanceMonitorConfig & {
    enableConsoleReporter?: boolean;
    httpEndpoint?: string;
    httpHeaders?: Record<string, string>;
} = {}): MicroCorePerformanceMonitor {
    const monitor = new MicroCorePerformanceMonitor(options);

    // 添加控制台报告器
    if (options.enableConsoleReporter !== false) {
        monitor.addReporter(new ConsolePerformanceReporter());
    }

    // 添加HTTP报告器
    if (options.httpEndpoint) {
        monitor.addReporter(new HttpPerformanceReporter(options.httpEndpoint, options.httpHeaders));
    }

    return monitor;
}

// 导出全局性能监控器实例
export const globalPerformanceMonitor = createDefaultPerformanceMonitor();

/**
 * 便捷的性能记录函数
 */
export function recordPerformance(
    name: string,
    value: number,
    type: MetricType = MetricType.GAUGE,
    options?: {
        unit?: string;
        labels?: Record<string, string>;
        description?: string;
    }
): void {
    globalPerformanceMonitor.recordMetric(name, value, type, options);
}

/**
 * 创建性能计时器
 */
export function createPerformanceTimer(name: string, labels?: Record<string, string>): () => void {
    return globalPerformanceMonitor.createTimer(name, labels);
}

/**
 * 性能装饰器
 */
export function performanceMonitor(metricName?: string) {
    return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
        const originalMethod = descriptor.value;
        const name = metricName || `${target.constructor.name}.${propertyKey}`;

        descriptor.value = function (...args: any[]) {
            const timer = createPerformanceTimer(name);

            try {
                const result = originalMethod.apply(this, args);

                if (result instanceof Promise) {
                    return result.finally(() => timer());
                } else {
                    timer();
                    return result;
                }
            } catch (error) {
                timer();
                throw error;
            }
        };

        return descriptor;
    };
}

/**
 * 异步函数性能监控
 */
export async function withPerformanceMonitoring<T>(
    name: string,
    fn: () => Promise<T>,
    labels?: Record<string, string>
): Promise<T> {
    const timer = createPerformanceTimer(name, labels);

    try {
        const result = await fn();
        timer();
        return result;
    } catch (error) {
        timer();
        throw error;
    }
}