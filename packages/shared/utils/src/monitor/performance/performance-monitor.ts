/**
 * @fileoverview 性能监控核心类
 * @description 提供应用性能监控的核心功能
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

import { ApplicationMetricsCollector } from './collectors/application-collector';
import { BrowserMetricsCollector } from './collectors/browser-collector';
import { SystemMetricsCollector } from './collectors/system-collector';
import type {
    MetricType,
    PerformanceCollector,
    PerformanceMetric,
    PerformanceMonitorConfig,
    PerformanceReporter,
    PerformanceStats
} from './types';

/**
 * 微核心性能监控器
 */
export class MicroCorePerformanceMonitor {
    private config: Required<PerformanceMonitorConfig>;
    private metrics: PerformanceMetric[] = [];
    private collectors: PerformanceCollector[] = [];
    private reporters: PerformanceReporter[] = [];
    private collectTimer: NodeJS.Timeout | null = null;
    private startTime: number;
    private observer: PerformanceObserver | null = null;

    constructor(config: PerformanceMonitorConfig = {}) {
        this.config = {
            enabled: true,
            sampleRate: 1.0,
            collectInterval: 5000,
            maxMetrics: 1000,
            collectBrowserMetrics: true,
            collectResourceMetrics: true,
            collectUserMetrics: true,
            customCollectors: [],
            ...config
        };

        this.startTime = Date.now();

        if (this.config.enabled) {
            this.initialize();
        }
    }

    /**
     * 初始化监控器
     */
    private initialize(): void {
        // 设置默认收集器
        this.setupDefaultCollectors();

        // 添加自定义收集器
        this.collectors.push(...this.config.customCollectors);

        // 设置浏览器性能监控
        if (this.config.collectBrowserMetrics) {
            this.setupBrowserMetrics();
        }

        // 设置资源监控
        if (this.config.collectResourceMetrics) {
            this.setupResourceMetrics();
        }

        // 设置用户交互监控
        if (this.config.collectUserMetrics) {
            this.setupUserMetrics();
        }

        // 启动定时收集
        this.startCollection();
    }

    /**
     * 记录指标
     */
    recordMetric(
        name: string,
        value: number,
        type: MetricType = MetricType.GAUGE,
        options: {
            unit?: string;
            labels?: Record<string, string>;
            description?: string;
        } = {}
    ): void {
        if (!this.config.enabled || !this.shouldSample()) {
            return;
        }

        const metric: PerformanceMetric = {
            name,
            type,
            value,
            unit: options.unit,
            labels: options.labels,
            timestamp: Date.now(),
            description: options.description
        };

        this.addMetric(metric);
    }

    /**
     * 记录计时器
     */
    recordTimer(name: string, startTime: number, labels?: Record<string, string>): void {
        const duration = Date.now() - startTime;
        this.recordMetric(name, duration, MetricType.TIMER, {
            unit: 'ms',
            labels,
            description: `Timer for ${name}`
        });
    }

    /**
     * 记录计数器
     */
    recordCounter(name: string, increment: number = 1, labels?: Record<string, string>): void {
        this.recordMetric(name, increment, MetricType.COUNTER, {
            labels,
            description: `Counter for ${name}`
        });
    }

    /**
     * 创建计时器
     */
    createTimer(name: string, labels?: Record<string, string>): () => void {
        const startTime = Date.now();
        return () => {
            this.recordTimer(name, startTime, labels);
        };
    }

    /**
     * 获取性能统计信息
     */
    getStats(): PerformanceStats {
        const stats: PerformanceStats = {
            appStartTime: this.startTime
        };

        if (typeof window !== 'undefined' && window.performance) {
            const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
            if (navigation) {
                stats.pageLoadTime = navigation.loadEventEnd - navigation.fetchStart;
            }

            // 获取 Web Vitals
            const paintEntries = performance.getEntriesByType('paint');
            const fcpEntry = paintEntries.find(entry => entry.name === 'first-contentful-paint');
            if (fcpEntry) {
                stats.firstContentfulPaint = fcpEntry.startTime;
            }

            // 获取内存信息
            if ('memory' in performance) {
                const memory = (performance as any).memory;
                stats.memoryUsage = {
                    used: memory.usedJSHeapSize,
                    total: memory.totalJSHeapSize,
                    limit: memory.jsHeapSizeLimit
                };
            }

            // 获取网络连接信息
            if ('connection' in navigator) {
                const connection = (navigator as any).connection;
                stats.connection = {
                    effectiveType: connection.effectiveType,
                    downlink: connection.downlink,
                    rtt: connection.rtt
                };
            }
        }

        return stats;
    }

    /**
     * 获取所有指标
     */
    getMetrics(): PerformanceMetric[] {
        return [...this.metrics];
    }

    /**
     * 清除指标
     */
    clearMetrics(): void {
        this.metrics = [];
    }

    /**
     * 添加收集器
     */
    addCollector(collector: PerformanceCollector): void {
        this.collectors.push(collector);
    }

    /**
     * 移除收集器
     */
    removeCollector(name: string): void {
        this.collectors = this.collectors.filter(c => c.name !== name);
    }

    /**
     * 添加报告器
     */
    addReporter(reporter: PerformanceReporter): void {
        this.reporters.push(reporter);
    }

    /**
     * 移除报告器
     */
    removeReporter(reporter: PerformanceReporter): void {
        const index = this.reporters.indexOf(reporter);
        if (index > -1) {
            this.reporters.splice(index, 1);
        }
    }

    /**
     * 启动监控
     */
    start(): void {
        if (!this.config.enabled) {
            this.config.enabled = true;
            this.initialize();
        }
    }

    /**
     * 停止监控
     */
    stop(): void {
        this.config.enabled = false;

        if (this.collectTimer) {
            clearInterval(this.collectTimer);
            this.collectTimer = null;
        }

        if (this.observer) {
            this.observer.disconnect();
            this.observer = null;
        }
    }

    /**
     * 立即收集并报告指标
     */
    async flush(): Promise<void> {
        await this.collectMetrics();
        await this.reportMetrics();
    }

    /**
     * 是否应该采样
     */
    private shouldSample(): boolean {
        return Math.random() < this.config.sampleRate;
    }

    /**
     * 添加指标
     */
    private addMetric(metric: PerformanceMetric): void {
        this.metrics.push(metric);

        // 如果超过最大数量，移除最旧的指标
        if (this.metrics.length > this.config.maxMetrics) {
            this.metrics.shift();
        }
    }

    /**
     * 设置默认收集器
     */
    private setupDefaultCollectors(): void {
        // 系统指标收集器
        this.collectors.push(new SystemMetricsCollector());

        // 应用指标收集器
        this.collectors.push(new ApplicationMetricsCollector(this.startTime, () => this.metrics.length));

        // 浏览器指标收集器
        if (typeof window !== 'undefined') {
            this.collectors.push(new BrowserMetricsCollector());
        }
    }

    /**
     * 设置浏览器性能监控
     */
    private setupBrowserMetrics(): void {
        if (typeof window === 'undefined' || !window.performance) {
            return;
        }

        // 监听性能条目
        if ('PerformanceObserver' in window) {
            this.observer = new PerformanceObserver((list) => {
                const entries = list.getEntries();
                entries.forEach(entry => {
                    this.processPerfEntry(entry);
                });
            });

            // 观察各种性能条目类型
            try {
                this.observer.observe({ entryTypes: ['navigation', 'paint', 'largest-contentful-paint', 'first-input', 'layout-shift'] });
            } catch (error) {
                console.warn('性能观察器设置失败:', error);
            }
        }
    }

    /**
     * 设置资源监控
     */
    private setupResourceMetrics(): void {
        if (typeof window === 'undefined' || !window.performance) {
            return;
        }

        // 监听资源加载
        const resourceObserver = new PerformanceObserver((list) => {
            const entries = list.getEntries();
            entries.forEach(entry => {
                if (entry.entryType === 'resource') {
                    this.processResourceEntry(entry as PerformanceResourceTiming);
                }
            });
        });

        try {
            resourceObserver.observe({ entryTypes: ['resource'] });
        } catch (error) {
            console.warn('资源监控设置失败:', error);
        }
    }

    /**
     * 设置用户交互监控
     */
    private setupUserMetrics(): void {
        if (typeof window === 'undefined') {
            return;
        }

        // 监听用户交互事件
        const events = ['click', 'scroll', 'keydown', 'touchstart'];
        events.forEach(eventType => {
            window.addEventListener(eventType, () => {
                this.recordCounter(`user.${eventType}`, 1, {
                    type: eventType,
                    timestamp: Date.now().toString()
                });
            }, { passive: true });
        });
    }

    /**
     * 启动定时收集
     */
    private startCollection(): void {
        this.collectTimer = setInterval(async () => {
            try {
                await this.collectMetrics();
                await this.reportMetrics();
            } catch (error) {
                console.error('指标收集失败:', error);
            }
        }, this.config.collectInterval);
    }

    /**
     * 收集指标
     */
    private async collectMetrics(): Promise<void> {
        const promises = this.collectors
            .filter(collector => collector.enabled !== false)
            .map(async collector => {
                try {
                    const metrics = await collector.collect();
                    metrics.forEach(metric => this.addMetric(metric));
                } catch (error) {
                    console.error(`收集器 ${collector.name} 执行失败:`, error);
                }
            });

        await Promise.allSettled(promises);
    }

    /**
     * 报告指标
     */
    private async reportMetrics(): Promise<void> {
        if (this.metrics.length === 0 || this.reporters.length === 0) {
            return;
        }

        const metricsToReport = [...this.metrics];
        this.clearMetrics();

        const promises = this.reporters.map(async reporter => {
            try {
                await reporter.report(metricsToReport);
            } catch (error) {
                console.error('指标报告失败:', error);
            }
        });

        await Promise.allSettled(promises);
    }

    /**
     * 处理性能条目
     */
    private processPerfEntry(entry: PerformanceEntry): void {
        switch (entry.entryType) {
            case 'navigation':
                const navEntry = entry as PerformanceNavigationTiming;
                this.recordMetric('page.load.time', navEntry.loadEventEnd - navEntry.fetchStart, MetricType.TIMER, {
                    unit: 'ms',
                    description: '页面加载时间'
                });
                break;

            case 'paint':
                if (entry.name === 'first-contentful-paint') {
                    this.recordMetric('page.fcp', entry.startTime, MetricType.TIMER, {
                        unit: 'ms',
                        description: '首次内容绘制时间'
                    });
                }
                break;

            case 'largest-contentful-paint':
                this.recordMetric('page.lcp', entry.startTime, MetricType.TIMER, {
                    unit: 'ms',
                    description: '最大内容绘制时间'
                });
                break;

            case 'first-input':
                const fidEntry = entry as PerformanceEventTiming;
                this.recordMetric('page.fid', fidEntry.processingStart - fidEntry.startTime, MetricType.TIMER, {
                    unit: 'ms',
                    description: '首次输入延迟'
                });
                break;

            case 'layout-shift':
                const clsEntry = entry as any;
                if (!clsEntry.hadRecentInput) {
                    this.recordMetric('page.cls', clsEntry.value, MetricType.GAUGE, {
                        description: '累积布局偏移'
                    });
                }
                break;
        }
    }

    /**
     * 处理资源条目
     */
    private processResourceEntry(entry: PerformanceResourceTiming): void {
        const resourceType = this.getResourceType(entry.name);
        const loadTime = entry.responseEnd - entry.startTime;

        this.recordMetric(`resource.${resourceType}.load.time`, loadTime, MetricType.TIMER, {
            unit: 'ms',
            labels: {
                url: entry.name,
                type: resourceType
            },
            description: `${resourceType} 资源加载时间`
        });

        this.recordCounter(`resource.${resourceType}.count`, 1, {
            type: resourceType
        });
    }

    /**
     * 获取资源类型
     */
    private getResourceType(url: string): string {
        const ext = url.split('.').pop()?.toLowerCase();

        switch (ext) {
            case 'js':
            case 'mjs':
                return 'script';
            case 'css':
                return 'stylesheet';
            case 'png':
            case 'jpg':
            case 'jpeg':
            case 'gif':
            case 'svg':
            case 'webp':
                return 'image';
            case 'woff':
            case 'woff2':
            case 'eot':
            case 'ttf':
                return 'font';
            case 'json':
                return 'xhr';
            default:
                return 'other';
        }
    }
}