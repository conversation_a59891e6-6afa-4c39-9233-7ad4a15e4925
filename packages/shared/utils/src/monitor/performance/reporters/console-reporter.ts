/**
 * @fileoverview 控制台性能报告器
 * @description 将性能指标输出到控制台
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

import type { PerformanceMetric, PerformanceReporter } from '../types';

/**
 * 控制台性能报告器
 */
export class ConsolePerformanceReporter implements PerformanceReporter {
    async report(metrics: PerformanceMetric[]): Promise<void> {
        if (metrics.length === 0) {
            return;
        }

        console.group(`📊 性能指标报告 (${metrics.length} 个指标)`);

        // 按类型分组
        const groupedMetrics = this.groupMetricsByType(metrics);

        Object.entries(groupedMetrics).forEach(([type, typeMetrics]) => {
            console.group(`${type.toUpperCase()} (${typeMetrics.length})`);
            typeMetrics.forEach(metric => {
                const value = metric.unit ? `${metric.value} ${metric.unit}` : metric.value;
                const labels = metric.labels ? ` [${Object.entries(metric.labels).map(([k, v]) => `${k}=${v}`).join(', ')}]` : '';
                console.log(`${metric.name}: ${value}${labels}`);
            });
            console.groupEnd();
        });

        console.groupEnd();
    }

    async reportMetric(metric: PerformanceMetric): Promise<void> {
        const value = metric.unit ? `${metric.value} ${metric.unit}` : metric.value;
        const labels = metric.labels ? ` [${Object.entries(metric.labels).map(([k, v]) => `${k}=${v}`).join(', ')}]` : '';
        console.log(`📊 ${metric.name}: ${value}${labels}`);
    }

    private groupMetricsByType(metrics: PerformanceMetric[]): Record<string, PerformanceMetric[]> {
        return metrics.reduce((groups, metric) => {
            const type = metric.type;
            if (!groups[type]) {
                groups[type] = [];
            }
            groups[type].push(metric);
            return groups;
        }, {} as Record<string, PerformanceMetric[]>);
    }
}