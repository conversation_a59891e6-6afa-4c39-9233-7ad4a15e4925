/**
 * @fileoverview HTTP性能报告器
 * @description 将性能指标通过HTTP发送到远程服务器
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

import type { PerformanceMetric, PerformanceReporter } from '../types';

/**
 * HTTP性能报告器
 */
export class HttpPerformanceReporter implements PerformanceReporter {
    private endpoint: string;
    private headers: Record<string, string>;

    constructor(endpoint: string, headers: Record<string, string> = {}) {
        this.endpoint = endpoint;
        this.headers = {
            'Content-Type': 'application/json',
            ...headers
        };
    }

    async report(metrics: PerformanceMetric[]): Promise<void> {
        try {
            const response = await fetch(this.endpoint, {
                method: 'POST',
                headers: this.headers,
                body: JSON.stringify({ metrics })
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
        } catch (error) {
            console.error('HTTP性能报告失败:', error);
            throw error;
        }
    }

    async reportMetric(metric: PerformanceMetric): Promise<void> {
        await this.report([metric]);
    }
}