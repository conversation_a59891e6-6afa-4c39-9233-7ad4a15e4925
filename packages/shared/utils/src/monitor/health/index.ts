/**
 * @fileoverview 健康检查系统主导出
 * @description 统一导出健康检查系统的所有功能
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

// 核心类型和枚举
export { HealthStatus } from './types';
export type {
    HealthCheckConfig, HealthChecker, HealthCheckResult, SystemHealth
} from './types';

// 主要类
export { MicroCoreHealthChecker } from './health-monitor';

// 检查器
export { ApplicationHealthChecker } from './checkers/application-checker';
export { BrowserHealthChecker } from './checkers/browser-checker';
export { CustomHealthChecker } from './checkers/custom-checker';
export { DatabaseHealthChecker } from './checkers/database-checker';
export { MemoryHealthChecker } from './checkers/memory-checker';
export { NetworkHealthChecker } from './checkers/network-checker';

// 工具函数
export {
    checkHealth, createCustomHealthChecker, createDatabaseHealthChecker, createDefaultHealthChecker, createHealthCheckMiddleware, createMemoryHealthChecker,
    createNetworkHealthChecker, globalHealthChecker, healthCheck
} from './utils';

// 格式化工具
export { HealthFormatter } from './formatter';
