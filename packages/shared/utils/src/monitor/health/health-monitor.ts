/**
 * @fileoverview 健康监控基础类
 * @description 提供应用健康状态监控的核心功能
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

import { ApplicationHealthChecker } from './checkers/application-checker';
import { <PERSON><PERSON><PERSON><PERSON>eal<PERSON><PERSON><PERSON><PERSON> } from './checkers/browser-checker';
import { Memory<PERSON>ealthChecker } from './checkers/memory-checker';
import type { HealthCheckConfig, HealthChecker, HealthCheckResult, SystemHealth } from './types';
import { HealthStatus } from './types';

/**
 * 微核心健康检查器
 */
export class MicroCoreHealthChecker {
    private config: Required<HealthCheckConfig>;
    private checkers: Map<string, HealthChecker> = new Map();
    private results: Map<string, HealthCheckResult> = new Map();
    private timers: Map<string, NodeJS.Timeout> = new Map();
    private startTime: number;
    private running = false;

    constructor(config: HealthCheckConfig = {}) {
        this.config = {
            enabled: true,
            defaultInterval: 30000, // 30秒
            defaultTimeout: 5000,   // 5秒
            concurrencyLimit: 10,
            checkOnStart: true,
            customCheckers: [],
            ...config
        };

        this.startTime = Date.now();

        if (this.config.enabled) {
            this.initialize();
        }
    }

    /**
     * 初始化健康检查器
     */
    private initialize(): void {
        // 添加默认检查器
        this.setupDefaultCheckers();

        // 添加自定义检查器
        this.config.customCheckers.forEach(checker => {
            this.addChecker(checker);
        });

        // 启动时立即检查
        if (this.config.checkOnStart) {
            this.checkAll().catch(error => {
                console.error('启动时健康检查失败:', error);
            });
        }
    }

    /**
     * 添加健康检查器
     */
    addChecker(checker: HealthChecker): void {
        this.checkers.set(checker.name, checker);

        // 如果正在运行，启动定时检查
        if (this.running) {
            this.startPeriodicCheck(checker);
        }
    }

    /**
     * 移除健康检查器
     */
    removeChecker(name: string): void {
        this.checkers.delete(name);
        this.results.delete(name);

        // 清除定时器
        const timer = this.timers.get(name);
        if (timer) {
            clearInterval(timer);
            this.timers.delete(name);
        }
    }

    /**
     * 获取健康检查器
     */
    getChecker(name: string): HealthChecker | undefined {
        return this.checkers.get(name);
    }

    /**
     * 获取所有检查器
     */
    getCheckers(): HealthChecker[] {
        return Array.from(this.checkers.values());
    }

    /**
     * 执行单个健康检查
     */
    async check(name: string): Promise<HealthCheckResult> {
        const checker = this.checkers.get(name);
        if (!checker) {
            throw new Error(`健康检查器 '${name}' 不存在`);
        }

        return this.executeCheck(checker);
    }

    /**
     * 执行所有健康检查
     */
    async checkAll(): Promise<HealthCheckResult[]> {
        const checkers = Array.from(this.checkers.values())
            .filter(checker => checker.enabled !== false);

        // 限制并发数量
        const results: HealthCheckResult[] = [];
        const limit = this.config.concurrencyLimit;

        for (let i = 0; i < checkers.length; i += limit) {
            const batch = checkers.slice(i, i + limit);
            const batchResults = await Promise.allSettled(
                batch.map(checker => this.executeCheck(checker))
            );

            batchResults.forEach(result => {
                if (result.status === 'fulfilled') {
                    results.push(result.value);
                } else {
                    console.error('健康检查执行失败:', result.reason);
                }
            });
        }

        return results;
    }

    /**
     * 获取系统健康状态
     */
    async getSystemHealth(): Promise<SystemHealth> {
        const checks = await this.checkAll();

        // 更新结果缓存
        checks.forEach(result => {
            this.results.set(result.name, result);
        });

        // 统计状态
        const healthyCount = checks.filter(c => c.status === HealthStatus.HEALTHY).length;
        const warningCount = checks.filter(c => c.status === HealthStatus.WARNING).length;
        const unhealthyCount = checks.filter(c => c.status === HealthStatus.UNHEALTHY).length;

        // 确定整体状态
        let overallStatus = HealthStatus.HEALTHY;
        if (unhealthyCount > 0) {
            overallStatus = HealthStatus.UNHEALTHY;
        } else if (warningCount > 0) {
            overallStatus = HealthStatus.WARNING;
        }

        return {
            status: overallStatus,
            timestamp: Date.now(),
            checks,
            healthyCount,
            warningCount,
            unhealthyCount,
            totalCount: checks.length,
            uptime: Date.now() - this.startTime,
            version: this.getVersion()
        };
    }

    /**
     * 获取缓存的检查结果
     */
    getCachedResults(): HealthCheckResult[] {
        return Array.from(this.results.values());
    }

    /**
     * 获取特定检查的缓存结果
     */
    getCachedResult(name: string): HealthCheckResult | undefined {
        return this.results.get(name);
    }

    /**
     * 启动定期健康检查
     */
    start(): void {
        if (this.running) {
            return;
        }

        this.running = true;

        // 为每个检查器启动定时检查
        this.checkers.forEach(checker => {
            this.startPeriodicCheck(checker);
        });

        console.log('健康检查器已启动');
    }

    /**
     * 停止定期健康检查
     */
    stop(): void {
        if (!this.running) {
            return;
        }

        this.running = false;

        // 清除所有定时器
        this.timers.forEach(timer => {
            clearInterval(timer);
        });
        this.timers.clear();

        console.log('健康检查器已停止');
    }

    /**
     * 执行健康检查
     */
    private async executeCheck(checker: HealthChecker): Promise<HealthCheckResult> {
        const startTime = Date.now();
        const timeout = checker.timeout || this.config.defaultTimeout;

        try {
            // 使用超时控制
            const result = await Promise.race([
                checker.check(),
                this.createTimeoutPromise(timeout, checker.name)
            ]);

            const responseTime = Date.now() - startTime;

            return {
                ...result,
                responseTime,
                timestamp: Date.now()
            };
        } catch (error) {
            const responseTime = Date.now() - startTime;

            return {
                name: checker.name,
                status: HealthStatus.UNHEALTHY,
                message: error instanceof Error ? error.message : String(error),
                timestamp: Date.now(),
                responseTime,
                error: error instanceof Error ? error : new Error(String(error))
            };
        }
    }

    /**
     * 创建超时Promise
     */
    private createTimeoutPromise(timeout: number, checkerName: string): Promise<never> {
        return new Promise((_, reject) => {
            setTimeout(() => {
                reject(new Error(`健康检查 '${checkerName}' 超时 (${timeout}ms)`));
            }, timeout);
        });
    }

    /**
     * 启动定期检查
     */
    private startPeriodicCheck(checker: HealthChecker): void {
        if (checker.enabled === false) {
            return;
        }

        const interval = checker.interval || this.config.defaultInterval;

        const timer = setInterval(async () => {
            try {
                const result = await this.executeCheck(checker);
                this.results.set(checker.name, result);
            } catch (error) {
                console.error(`定期健康检查 '${checker.name}' 失败:`, error);
            }
        }, interval);

        this.timers.set(checker.name, timer);
    }

    /**
     * 设置默认检查器
     */
    private setupDefaultCheckers(): void {
        // 内存使用检查器
        this.addChecker(new MemoryHealthChecker());

        // 应用状态检查器
        this.addChecker(new ApplicationHealthChecker(this.startTime));

        // 如果在浏览器环境，添加浏览器相关检查器
        if (typeof window !== 'undefined') {
            this.addChecker(new BrowserHealthChecker());
        }
    }

    /**
     * 获取版本信息
     */
    private getVersion(): string {
        try {
            return process.env.npm_package_version || '1.0.0';
        } catch {
            return '1.0.0';
        }
    }
}