/**
 * @fileoverview 应用健康检查器
 * @description 监控应用运行状态
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

import type { HealthChecker, HealthCheckResult } from '../types';
import { HealthStatus } from '../types';

/**
 * 应用健康检查器
 */
export class <PERSON>HealthChecker implements HealthChecker {
    name = 'application';
    interval = 60000; // 60秒
    timeout = 3000;   // 3秒

    constructor(private startTime: number) { }

    async check(): Promise<HealthCheckResult> {
        const startTime = Date.now();

        try {
            const uptime = Date.now() - this.startTime;
            const uptimeSeconds = Math.floor(uptime / 1000);
            const uptimeMinutes = Math.floor(uptimeSeconds / 60);
            const uptimeHours = Math.floor(uptimeMinutes / 60);

            const data = {
                uptime,
                uptimeFormatted: `${uptimeHours}h ${uptimeMinutes % 60}m ${uptimeSeconds % 60}s`,
                startTime: this.startTime,
                currentTime: Date.now(),
                environment: typeof window !== 'undefined' ? 'browser' : 'node'
            };

            return {
                name: this.name,
                status: HealthStatus.HEALTHY,
                message: `应用运行正常，已运行 ${data.uptimeFormatted}`,
                timestamp: Date.now(),
                responseTime: Date.now() - startTime,
                data
            };
        } catch (error) {
            return {
                name: this.name,
                status: HealthStatus.UNHEALTHY,
                message: '应用状态检查失败',
                timestamp: Date.now(),
                responseTime: Date.now() - startTime,
                error: error instanceof Error ? error : new Error(String(error))
            };
        }
    }
}