/**
 * @fileoverview 数据库健康检查器
 * @description 监控数据库连接状态
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

import type { HealthChecker, HealthCheckResult } from '../types';
import { HealthStatus } from '../types';

/**
 * 数据库健康检查器
 */
export class DatabaseHealthChecker implements HealthChecker {
    name = 'database';
    interval = 30000; // 30秒
    timeout = 5000;   // 5秒

    constructor(private checkFunction: () => Promise<boolean>) { }

    async check(): Promise<HealthCheckResult> {
        const startTime = Date.now();

        try {
            const isHealthy = await this.checkFunction();

            return {
                name: this.name,
                status: isHealthy ? HealthStatus.HEALTHY : HealthStatus.UNHEALTHY,
                message: isHealthy ? '数据库连接正常' : '数据库连接失败',
                timestamp: Date.now(),
                responseTime: Date.now() - startTime
            };
        } catch (error) {
            return {
                name: this.name,
                status: HealthStatus.UNHEALTHY,
                message: '数据库检查失败',
                timestamp: Date.now(),
                responseTime: Date.now() - startTime,
                error: error instanceof Error ? error : new Error(String(error))
            };
        }
    }
}