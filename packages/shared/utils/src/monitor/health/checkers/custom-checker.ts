/**
 * @fileoverview 自定义健康检查器
 * @description 提供自定义健康检查功能
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

import type { HealthChecker, HealthCheckResult } from '../types';
import { HealthStatus } from '../types';

/**
 * 自定义健康检查器
 */
export class CustomHealthChecker implements HealthChecker {
    constructor(
        public name: string,
        private checkFunction: () => Promise<Omit<HealthCheckResult, 'name' | 'timestamp' | 'responseTime'>>,
        public interval?: number,
        public timeout?: number,
        public enabled?: boolean
    ) { }

    async check(): Promise<HealthCheckResult> {
        const startTime = Date.now();

        try {
            const result = await this.checkFunction();

            return {
                ...result,
                name: this.name,
                timestamp: Date.now(),
                responseTime: Date.now() - startTime
            };
        } catch (error) {
            return {
                name: this.name,
                status: HealthStatus.UNHEALTHY,
                message: error instanceof Error ? error.message : String(error),
                timestamp: Date.now(),
                responseTime: Date.now() - startTime,
                error: error instanceof Error ? error : new Error(String(error))
            };
        }
    }
}