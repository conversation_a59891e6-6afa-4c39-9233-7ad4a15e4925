/**
 * @fileoverview 浏览器健康检查器
 * @description 监控浏览器环境状态
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

import type { HealthChecker, HealthCheckResult } from '../types';
import { HealthStatus } from '../types';

/**
 * 浏览器健康检查器
 */
export class BrowserHealthChecker implements HealthChecker {
    name = 'browser';
    interval = 60000; // 60秒
    timeout = 3000;   // 3秒

    async check(): Promise<HealthCheckResult> {
        const startTime = Date.now();

        try {
            if (typeof window === 'undefined') {
                return {
                    name: this.name,
                    status: HealthStatus.UNKNOWN,
                    message: '非浏览器环境',
                    timestamp: Date.now(),
                    responseTime: Date.now() - startTime
                };
            }

            const data: any = {
                userAgent: navigator.userAgent,
                language: navigator.language,
                onLine: navigator.onLine,
                cookieEnabled: navigator.cookieEnabled,
                platform: navigator.platform
            };

            // 检查连接状态
            if ('connection' in navigator) {
                const connection = (navigator as any).connection;
                data.connection = {
                    effectiveType: connection.effectiveType,
                    downlink: connection.downlink,
                    rtt: connection.rtt,
                    saveData: connection.saveData
                };
            }

            // 检查存储
            try {
                localStorage.setItem('health-check', 'test');
                localStorage.removeItem('health-check');
                data.localStorage = true;
            } catch {
                data.localStorage = false;
            }

            // 判断健康状态
            let status = HealthStatus.HEALTHY;
            let message = '浏览器环境正常';

            if (!navigator.onLine) {
                status = HealthStatus.WARNING;
                message = '网络连接异常';
            } else if (!data.localStorage) {
                status = HealthStatus.WARNING;
                message = 'localStorage 不可用';
            }

            return {
                name: this.name,
                status,
                message,
                timestamp: Date.now(),
                responseTime: Date.now() - startTime,
                data
            };
        } catch (error) {
            return {
                name: this.name,
                status: HealthStatus.UNHEALTHY,
                message: '浏览器环境检查失败',
                timestamp: Date.now(),
                responseTime: Date.now() - startTime,
                error: error instanceof Error ? error : new Error(String(error))
            };
        }
    }
}