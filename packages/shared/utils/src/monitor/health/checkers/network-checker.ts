/**
 * @fileoverview 网络健康检查器
 * @description 监控网络连接状态
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

import type { HealthChecker, HealthCheckResult } from '../types';
import { HealthStatus } from '../types';

/**
 * 网络健康检查器
 */
export class NetworkHealthChecker implements HealthChecker {
    name = 'network';
    interval = 30000; // 30秒
    timeout = 10000;  // 10秒

    constructor(private endpoints: string[] = ['https://www.google.com', 'https://www.baidu.com']) { }

    async check(): Promise<HealthCheckResult> {
        const startTime = Date.now();

        try {
            const results = await Promise.allSettled(
                this.endpoints.map(endpoint => this.checkEndpoint(endpoint))
            );

            const successful = results.filter(r => r.status === 'fulfilled').length;
            const total = results.length;
            const successRate = (successful / total) * 100;

            let status = HealthStatus.HEALTHY;
            let message = `网络连接正常 (${successful}/${total} 成功)`;

            if (successRate === 0) {
                status = HealthStatus.UNHEALTHY;
                message = '网络连接完全失败';
            } else if (successRate < 50) {
                status = HealthStatus.UNHEALTHY;
                message = `网络连接不稳定 (${successRate.toFixed(1)}% 成功率)`;
            } else if (successRate < 100) {
                status = HealthStatus.WARNING;
                message = `网络连接部分异常 (${successRate.toFixed(1)}% 成功率)`;
            }

            return {
                name: this.name,
                status,
                message,
                timestamp: Date.now(),
                responseTime: Date.now() - startTime,
                data: {
                    successRate,
                    successful,
                    total,
                    results: results.map((r, i) => ({
                        endpoint: this.endpoints[i],
                        success: r.status === 'fulfilled',
                        error: r.status === 'rejected' ? r.reason?.message : undefined
                    }))
                }
            };
        } catch (error) {
            return {
                name: this.name,
                status: HealthStatus.UNHEALTHY,
                message: '网络检查失败',
                timestamp: Date.now(),
                responseTime: Date.now() - startTime,
                error: error instanceof Error ? error : new Error(String(error))
            };
        }
    }

    private async checkEndpoint(endpoint: string): Promise<void> {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), this.timeout);

        try {
            const response = await fetch(endpoint, {
                method: 'HEAD',
                signal: controller.signal,
                mode: 'no-cors' // 避免CORS问题
            });
            clearTimeout(timeoutId);
        } catch (error) {
            clearTimeout(timeoutId);
            throw error;
        }
    }
}