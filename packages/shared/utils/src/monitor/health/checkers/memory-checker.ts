/**
 * @fileoverview 内存健康检查器
 * @description 监控系统内存使用情况
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

import type { HealthChecker, HealthCheckResult } from '../types';
import { HealthStatus } from '../types';

/**
 * 内存健康检查器
 */
export class MemoryHealthChecker implements HealthChecker {
    name = 'memory';
    interval = 30000; // 30秒
    timeout = 5000;   // 5秒

    async check(): Promise<HealthCheckResult> {
        const startTime = Date.now();

        try {
            let memoryInfo: any = {};

            // 浏览器环境
            if (typeof window !== 'undefined' && 'memory' in performance) {
                const memory = (performance as any).memory;
                memoryInfo = {
                    used: memory.usedJSHeapSize,
                    total: memory.totalJSHeapSize,
                    limit: memory.jsHeapSizeLimit,
                    usagePercent: (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100
                };
            }
            // Node.js 环境
            else if (typeof process !== 'undefined' && process.memoryUsage) {
                const memory = process.memoryUsage();
                memoryInfo = {
                    rss: memory.rss,
                    heapTotal: memory.heapTotal,
                    heapUsed: memory.heapUsed,
                    external: memory.external,
                    usagePercent: (memory.heapUsed / memory.heapTotal) * 100
                };
            }

            // 判断健康状态
            let status = HealthStatus.HEALTHY;
            let message = '内存使用正常';

            if (memoryInfo.usagePercent > 90) {
                status = HealthStatus.UNHEALTHY;
                message = `内存使用率过高: ${memoryInfo.usagePercent.toFixed(1)}%`;
            } else if (memoryInfo.usagePercent > 80) {
                status = HealthStatus.WARNING;
                message = `内存使用率较高: ${memoryInfo.usagePercent.toFixed(1)}%`;
            }

            return {
                name: this.name,
                status,
                message,
                timestamp: Date.now(),
                responseTime: Date.now() - startTime,
                data: memoryInfo
            };
        } catch (error) {
            return {
                name: this.name,
                status: HealthStatus.UNHEALTHY,
                message: '内存检查失败',
                timestamp: Date.now(),
                responseTime: Date.now() - startTime,
                error: error instanceof Error ? error : new Error(String(error))
            };
        }
    }
}