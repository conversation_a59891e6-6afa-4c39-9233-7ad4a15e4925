/**
 * @fileoverview 健康状态格式化工具
 * @description 提供健康状态的格式化和展示功能
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

import type { SystemHealth } from './types';
import { HealthStatus } from './types';

/**
 * 健康状态格式化工具
 */
export const HealthFormatter = {
    /**
     * 格式化健康状态为字符串
     */
    formatStatus(status: HealthStatus): string {
        const statusMap = {
            [HealthStatus.HEALTHY]: '✅ 健康',
            [HealthStatus.WARNING]: '⚠️ 警告',
            [HealthStatus.UNHEALTHY]: '❌ 不健康',
            [HealthStatus.UNKNOWN]: '❓ 未知'
        };
        return statusMap[status] || '❓ 未知';
    },

    /**
     * 格式化系统健康状态
     */
    formatSystemHealth(health: SystemHealth): string {
        const lines = [
            `系统健康状态: ${this.formatStatus(health.status)}`,
            `检查时间: ${new Date(health.timestamp).toLocaleString()}`,
            `运行时间: ${Math.floor(health.uptime / 1000)}秒`,
            `检查结果: ${health.healthyCount}健康 / ${health.warningCount}警告 / ${health.unhealthyCount}不健康`,
            ''
        ];

        health.checks.forEach(check => {
            lines.push(`${this.formatStatus(check.status)} ${check.name}: ${check.message} (${check.responseTime}ms)`);
        });

        return lines.join('\n');
    },

    /**
     * 格式化为HTML
     */
    formatAsHTML(health: SystemHealth): string {
        const statusColor = {
            [HealthStatus.HEALTHY]: '#28a745',
            [HealthStatus.WARNING]: '#ffc107',
            [HealthStatus.UNHEALTHY]: '#dc3545',
            [HealthStatus.UNKNOWN]: '#6c757d'
        };

        return `
            <div style="font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px;">
                <h1 style="color: ${statusColor[health.status]};">
                    ${this.formatStatus(health.status)} 系统健康状态
                </h1>
                
                <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
                    <p><strong>检查时间:</strong> ${new Date(health.timestamp).toLocaleString()}</p>
                    <p><strong>运行时间:</strong> ${Math.floor(health.uptime / 1000)}秒</p>
                    <p><strong>检查统计:</strong> ${health.healthyCount}健康 / ${health.warningCount}警告 / ${health.unhealthyCount}不健康</p>
                </div>

                <h2>详细检查结果</h2>
                <div>
                    ${health.checks.map(check => `
                        <div style="
                            border-left: 4px solid ${statusColor[check.status]};
                            padding: 10px 15px;
                            margin: 10px 0;
                            background: #fff;
                            border-radius: 0 5px 5px 0;
                            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                        ">
                            <h3 style="margin: 0 0 5px 0; color: ${statusColor[check.status]};">
                                ${check.name}
                            </h3>
                            <p style="margin: 0; color: #666;">
                                ${check.message} <small>(响应时间: ${check.responseTime}ms)</small>
                            </p>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }
};