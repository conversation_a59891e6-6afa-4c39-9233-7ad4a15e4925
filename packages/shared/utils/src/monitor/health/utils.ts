/**
 * @fileoverview 健康检查工具函数
 * @description 提供健康检查相关的工具函数和装饰器
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

import { CustomHealthChecker } from './checkers/custom-checker';
import { DatabaseHealthChecker } from './checkers/database-checker';
import { MemoryHealthChecker } from './checkers/memory-checker';
import { NetworkHealthChecker } from './checkers/network-checker';
import { MicroCoreHealthChecker } from './health-monitor';
import type { HealthCheckConfig, HealthCheckResult, SystemHealth } from './types';
import { HealthStatus } from './types';

/**
 * 创建默认健康检查器
 */
export function createDefaultHealthChecker(config: HealthCheckConfig = {}): MicroCoreHealthChecker {
    return new MicroCoreHealthChecker(config);
}

/**
 * 创建内存健康检查器
 */
export function createMemoryHealthChecker(): MemoryHealthChecker {
    return new MemoryHealthChecker();
}

/**
 * 创建网络健康检查器
 */
export function createNetworkHealthChecker(endpoints?: string[]): NetworkHealthChecker {
    return new NetworkHealthChecker(endpoints);
}

/**
 * 创建数据库健康检查器
 */
export function createDatabaseHealthChecker(checkFunction: () => Promise<boolean>): DatabaseHealthChecker {
    return new DatabaseHealthChecker(checkFunction);
}

/**
 * 创建自定义健康检查器
 */
export function createCustomHealthChecker(
    name: string,
    checkFunction: () => Promise<Omit<HealthCheckResult, 'name' | 'timestamp' | 'responseTime'>>,
    options: {
        interval?: number;
        timeout?: number;
        enabled?: boolean;
    } = {}
): CustomHealthChecker {
    return new CustomHealthChecker(
        name,
        checkFunction,
        options.interval,
        options.timeout,
        options.enabled
    );
}

// 导出全局健康检查器实例
export const globalHealthChecker = createDefaultHealthChecker();

/**
 * 便捷的健康检查函数
 */
export async function checkHealth(): Promise<SystemHealth> {
    return globalHealthChecker.getSystemHealth();
}

/**
 * 健康检查装饰器
 */
export function healthCheck(checkerName?: string) {
    return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
        const originalMethod = descriptor.value;
        const name = checkerName || `${target.constructor.name}.${propertyKey}`;

        // 创建自定义检查器
        const checker = createCustomHealthChecker(name, async () => {
            try {
                await originalMethod.apply(target);
                return {
                    status: HealthStatus.HEALTHY,
                    message: `${name} 执行成功`
                };
            } catch (error) {
                return {
                    status: HealthStatus.UNHEALTHY,
                    message: `${name} 执行失败: ${error instanceof Error ? error.message : String(error)}`,
                    error: error instanceof Error ? error : new Error(String(error))
                };
            }
        });

        // 添加到全局检查器
        globalHealthChecker.addChecker(checker);

        return descriptor;
    };
}

/**
 * 健康检查中间件（用于Express等）
 */
export function createHealthCheckMiddleware(path = '/health') {
    return async (req: any, res: any, next: any) => {
        if (req.path === path) {
            try {
                const health = await checkHealth();
                const statusCode = health.status === HealthStatus.HEALTHY ? 200 : 503;

                res.status(statusCode).json(health);
            } catch (error) {
                res.status(500).json({
                    status: HealthStatus.UNHEALTHY,
                    message: '健康检查失败',
                    error: error instanceof Error ? error.message : String(error),
                    timestamp: Date.now()
                });
            }
        } else {
            next();
        }
    };
}