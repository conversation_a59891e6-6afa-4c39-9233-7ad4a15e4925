/**
 * @fileoverview 健康检查类型定义
 * @description 健康检查系统的所有类型定义
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

/**
 * 健康状态枚举
 */
export enum HealthStatus {
    /** 健康 */
    HEALTHY = 'healthy',
    /** 警告 */
    WARNING = 'warning',
    /** 不健康 */
    UNHEALTHY = 'unhealthy',
    /** 未知 */
    UNKNOWN = 'unknown'
}

/**
 * 健康检查结果
 */
export interface HealthCheckResult {
    /** 检查名称 */
    name: string;
    /** 健康状态 */
    status: HealthStatus;
    /** 检查消息 */
    message?: string;
    /** 检查时间 */
    timestamp: number;
    /** 响应时间（毫秒） */
    responseTime: number;
    /** 额外数据 */
    data?: Record<string, any>;
    /** 错误信息 */
    error?: Error;
}

/**
 * 健康检查器接口
 */
export interface HealthChecker {
    /** 检查器名称 */
    name: string;
    /** 执行健康检查 */
    check(): Promise<HealthCheckResult>;
    /** 是否启用 */
    enabled?: boolean;
    /** 检查间隔（毫秒） */
    interval?: number;
    /** 超时时间（毫秒） */
    timeout?: number;
}

/**
 * 系统健康状态
 */
export interface SystemHealth {
    /** 整体状态 */
    status: HealthStatus;
    /** 检查时间 */
    timestamp: number;
    /** 检查结果列表 */
    checks: HealthCheckResult[];
    /** 健康的检查数量 */
    healthyCount: number;
    /** 警告的检查数量 */
    warningCount: number;
    /** 不健康的检查数量 */
    unhealthyCount: number;
    /** 总检查数量 */
    totalCount: number;
    /** 系统运行时间 */
    uptime: number;
    /** 版本信息 */
    version?: string;
}

/**
 * 健康检查配置
 */
export interface HealthCheckConfig {
    /** 是否启用健康检查 */
    enabled?: boolean;
    /** 默认检查间隔（毫秒） */
    defaultInterval?: number;
    /** 默认超时时间（毫秒） */
    defaultTimeout?: number;
    /** 并行检查数量限制 */
    concurrencyLimit?: number;
    /** 是否在启动时立即检查 */
    checkOnStart?: boolean;
    /** 自定义检查器 */
    customCheckers?: HealthChecker[];
}