/**
 * @fileoverview 基础懒加载器
 * @description 提供懒加载器的基础实现
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

import type { LazyLoadConfig, LazyLoader, LazyLoadItem, LazyLoadStatus, LazyLoadStrategy } from './types';
import { LazyLoadStatus as Status } from './types';

/**
 * 基础懒加载器
 */
export abstract class BaseLazyLoader<T = any> implements LazyLoader<T> {
    protected items = new Map<string, LazyLoadItem<T>>();
    protected cache = new Map<string, T>();
    protected defaultConfig: Required<LazyLoadConfig>;

    constructor(config: LazyLoadConfig = {}) {
        this.defaultConfig = {
            strategy: 'deferred' as LazyLoadStrategy,
            delay: 0,
            intersectionOptions: { threshold: 0.1 },
            idleOptions: { timeout: 5000 },
            preloadPriority: 'auto',
            retries: 3,
            retryDelay: 1000,
            timeout: 30000,
            cache: true,
            onError: () => { },
            onSuccess: () => { },
            onProgress: () => { },
            ...config
        };
    }

    /**
     * 加载项
     */
    async load(id: string, loader: () => Promise<T>, config?: LazyLoadConfig): Promise<T> {
        const effectiveConfig = { ...this.defaultConfig, ...config };

        // 检查缓存
        if (effectiveConfig.cache && this.cache.has(id)) {
            return this.cache.get(id)!;
        }

        // 检查是否已存在
        let item = this.items.get(id);
        if (item) {
            if (item.status === Status.LOADED && item.result !== undefined) {
                return item.result;
            }
            if (item.status === Status.LOADING) {
                return this.waitForCompletion(item);
            }
        }

        // 创建新项
        item = {
            id,
            loader,
            config: effectiveConfig,
            status: Status.PENDING,
            createdAt: Date.now(),
            retryCount: 0
        };

        this.items.set(id, item);

        // 根据策略执行加载
        return this.executeLoad(item);
    }

    /**
     * 取消加载
     */
    cancel(id: string): boolean {
        const item = this.items.get(id);
        if (item && item.status === Status.LOADING) {
            item.status = Status.CANCELLED;
            return true;
        }
        return false;
    }

    /**
     * 获取状态
     */
    getStatus(id: string): LazyLoadStatus | undefined {
        return this.items.get(id)?.status;
    }

    /**
     * 获取结果
     */
    getResult(id: string): T | undefined {
        return this.items.get(id)?.result;
    }

    /**
     * 清理
     */
    cleanup(): void {
        // 清理已完成或失败的项
        for (const [id, item] of this.items.entries()) {
            if (item.status === Status.LOADED ||
                item.status === Status.FAILED ||
                item.status === Status.CANCELLED) {
                this.items.delete(id);
            }
        }
    }

    /**
     * 销毁
     */
    destroy(): void {
        this.items.clear();
        this.cache.clear();
    }

    /**
     * 抽象方法：执行加载
     */
    protected abstract executeLoad(item: LazyLoadItem<T>): Promise<T>;

    /**
     * 等待完成
     */
    protected async waitForCompletion(item: LazyLoadItem<T>): Promise<T> {
        return new Promise((resolve, reject) => {
            const checkStatus = () => {
                if (item.status === Status.LOADED && item.result !== undefined) {
                    resolve(item.result);
                } else if (item.status === Status.FAILED && item.error) {
                    reject(item.error);
                } else if (item.status === Status.CANCELLED) {
                    reject(new Error('加载已取消'));
                } else {
                    setTimeout(checkStatus, 100);
                }
            };
            checkStatus();
        });
    }

    /**
     * 执行实际加载
     */
    protected async performLoad(item: LazyLoadItem<T>): Promise<T> {
        item.status = Status.LOADING;
        item.startedAt = Date.now();

        try {
            // 设置超时
            const timeoutPromise = new Promise<never>((_, reject) => {
                setTimeout(() => reject(new Error('加载超时')), item.config.timeout);
            });

            const result = await Promise.race([
                item.loader(),
                timeoutPromise
            ]);

            item.result = result;
            item.status = Status.LOADED;
            item.completedAt = Date.now();

            // 缓存结果
            if (item.config.cache) {
                this.cache.set(item.id, result);
            }

            // 调用成功回调
            item.config.onSuccess?.(result);

            return result;
        } catch (error) {
            const err = error instanceof Error ? error : new Error(String(error));

            // 重试逻辑
            if (item.retryCount < item.config.retries!) {
                item.retryCount++;
                await this.delay(item.config.retryDelay!);
                return this.performLoad(item);
            }

            item.error = err;
            item.status = Status.FAILED;
            item.completedAt = Date.now();

            // 调用错误回调
            item.config.onError?.(err);

            throw err;
        }
    }

    /**
     * 延迟执行
     */
    protected delay(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}