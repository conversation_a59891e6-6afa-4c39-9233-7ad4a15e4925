/**
 * @fileoverview 懒加载工厂和管理器
 * @description 提供懒加载器的创建和管理功能
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

import {
    DeferredLazyLoader,
    IdleLazyLoader,
    ImmediateLazyLoader,
    IntersectionLazyLoader,
    OnDemandLazyLoader,
    PreloadLazyLoader
} from './loaders';
import type { LazyLoadConfig, LazyLoader } from './types';
import { LazyLoadStrategy } from './types';

/**
 * 懒加载工厂
 */
export class LazyLoaderFactory {
    /**
     * 创建懒加载器
     */
    static create<T = any>(strategy: LazyLoadStrategy, config?: LazyLoadConfig): LazyLoader<T> {
        switch (strategy) {
            case LazyLoadStrategy.IMMEDIATE:
                return new ImmediateLazyLoader<T>(config);
            case LazyLoadStrategy.DEFERRED:
                return new DeferredLazyLoader<T>(config);
            case LazyLoadStrategy.ON_DEMAND:
                return new OnDemandLazyLoader<T>(config);
            case LazyLoadStrategy.INTERSECTION:
                return new IntersectionLazyLoader<T>(config);
            case LazyLoadStrategy.IDLE:
                return new IdleLazyLoader<T>(config);
            case LazyLoadStrategy.PRELOAD:
                return new PreloadLazyLoader<T>(config);
            default:
                throw new Error(`不支持的懒加载策略: ${strategy}`);
        }
    }
}

/**
 * 懒加载管理器
 */
export class LazyLoadManager {
    private loaders = new Map<string, LazyLoader>();
    private defaultStrategy: LazyLoadStrategy;

    constructor(defaultStrategy: LazyLoadStrategy = LazyLoadStrategy.DEFERRED) {
        this.defaultStrategy = defaultStrategy;
    }

    /**
     * 注册加载器
     */
    registerLoader(name: string, loader: LazyLoader): void {
        this.loaders.set(name, loader);
    }

    /**
     * 获取加载器
     */
    getLoader(name: string): LazyLoader | undefined {
        return this.loaders.get(name);
    }

    /**
     * 创建加载器
     */
    createLoader(strategy: LazyLoadStrategy = this.defaultStrategy, config?: LazyLoadConfig): LazyLoader {
        return LazyLoaderFactory.create(strategy, config);
    }

    /**
     * 懒加载
     */
    async load<T>(
        id: string,
        loader: () => Promise<T>,
        strategy: LazyLoadStrategy = this.defaultStrategy,
        config?: LazyLoadConfig
    ): Promise<T> {
        const loaderInstance = this.createLoader(strategy, config);
        return loaderInstance.load(id, loader, config);
    }

    /**
     * 销毁所有加载器
     */
    destroy(): void {
        this.loaders.forEach(loader => loader.destroy());
        this.loaders.clear();
    }
}

/**
 * 默认懒加载管理器实例
 */
export const defaultLazyLoadManager = new LazyLoadManager();