/**
 * @fileoverview 懒加载工具函数
 * @description 提供便捷的懒加载工具函数和装饰器
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

import { defaultLazyLoadManager, LazyLoaderFactory } from './factory';
import type { LazyLoadConfig } from './types';
import { LazyLoadStrategy } from './types';

/**
 * 懒加载装饰器
 */
export function lazyLoad(strategy: LazyLoadStrategy = LazyLoadStrategy.DEFERRED, config?: LazyLoadConfig) {
    return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
        const originalMethod = descriptor.value;
        const loader = LazyLoaderFactory.create(strategy, config);

        descriptor.value = function (...args: any[]) {
            const id = `${target.constructor.name}.${propertyKey}`;
            return loader.load(id, () => originalMethod.apply(this, args), config);
        };

        return descriptor;
    };
}

/**
 * 懒加载组件高阶函数
 */
export function withLazyLoad<T>(
    loader: () => Promise<T>,
    strategy: LazyLoadStrategy = LazyLoadStrategy.INTERSECTION,
    config?: LazyLoadConfig
): () => Promise<T> {
    const lazyLoader = LazyLoaderFactory.create<T>(strategy, config);
    const id = `lazy_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    return () => lazyLoader.load(id, loader, config);
}

/**
 * 便捷的懒加载函数
 */
export const lazyLoadUtils = {
    /**
     * 立即加载
     */
    immediate: <T>(id: string, loader: () => Promise<T>, config?: LazyLoadConfig) =>
        defaultLazyLoadManager.load(id, loader, LazyLoadStrategy.IMMEDIATE, config),

    /**
     * 延迟加载
     */
    deferred: <T>(id: string, loader: () => Promise<T>, delay: number = 1000, config?: LazyLoadConfig) =>
        defaultLazyLoadManager.load(id, loader, LazyLoadStrategy.DEFERRED, { ...config, delay }),

    /**
     * 按需加载
     */
    onDemand: <T>(id: string, loader: () => Promise<T>, config?: LazyLoadConfig) =>
        defaultLazyLoadManager.load(id, loader, LazyLoadStrategy.ON_DEMAND, config),

    /**
     * 可见时加载
     */
    intersection: <T>(id: string, loader: () => Promise<T>, target: Element, config?: LazyLoadConfig) =>
        defaultLazyLoadManager.load(id, loader, LazyLoadStrategy.INTERSECTION, { ...config, target } as any),

    /**
     * 空闲时加载
     */
    idle: <T>(id: string, loader: () => Promise<T>, config?: LazyLoadConfig) =>
        defaultLazyLoadManager.load(id, loader, LazyLoadStrategy.IDLE, config),

    /**
     * 预加载
     */
    preload: <T>(id: string, loader: () => Promise<T>, config?: LazyLoadConfig) =>
        defaultLazyLoadManager.load(id, loader, LazyLoadStrategy.PRELOAD, config)
};