/**
 * @fileoverview 懒加载类型定义
 * @description 提供懒加载相关的类型定义和接口
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

/**
 * 懒加载策略枚举
 */
export enum LazyLoadStrategy {
    /** 立即加载 */
    IMMEDIATE = 'immediate',
    /** 延迟加载 */
    DEFERRED = 'deferred',
    /** 按需加载 */
    ON_DEMAND = 'on_demand',
    /** 可见时加载 */
    INTERSECTION = 'intersection',
    /** 空闲时加载 */
    IDLE = 'idle',
    /** 预加载 */
    PRELOAD = 'preload'
}

/**
 * 懒加载配置
 */
export interface LazyLoadConfig {
    /** 加载策略 */
    strategy?: LazyLoadStrategy;
    /** 延迟时间（毫秒） */
    delay?: number;
    /** 交集观察器选项 */
    intersectionOptions?: IntersectionObserverInit;
    /** 空闲回调选项 */
    idleOptions?: IdleRequestOptions;
    /** 预加载优先级 */
    preloadPriority?: 'high' | 'low' | 'auto';
    /** 重试次数 */
    retries?: number;
    /** 重试间隔 */
    retryDelay?: number;
    /** 超时时间 */
    timeout?: number;
    /** 是否启用缓存 */
    cache?: boolean;
    /** 错误处理函数 */
    onError?: (error: Error) => void;
    /** 加载成功回调 */
    onSuccess?: (result: any) => void;
    /** 加载进度回调 */
    onProgress?: (progress: number) => void;
}

/**
 * 懒加载状态
 */
export enum LazyLoadStatus {
    /** 未开始 */
    PENDING = 'pending',
    /** 加载中 */
    LOADING = 'loading',
    /** 已完成 */
    LOADED = 'loaded',
    /** 失败 */
    FAILED = 'failed',
    /** 已取消 */
    CANCELLED = 'cancelled'
}

/**
 * 懒加载项接口
 */
export interface LazyLoadItem<T = any> {
    /** 唯一标识 */
    id: string;
    /** 加载函数 */
    loader: () => Promise<T>;
    /** 配置 */
    config: LazyLoadConfig;
    /** 状态 */
    status: LazyLoadStatus;
    /** 结果 */
    result?: T;
    /** 错误 */
    error?: Error;
    /** 创建时间 */
    createdAt: number;
    /** 开始加载时间 */
    startedAt?: number;
    /** 完成时间 */
    completedAt?: number;
    /** 重试次数 */
    retryCount: number;
}

/**
 * 懒加载器接口
 */
export interface LazyLoader<T = any> {
    /** 加载项 */
    load(id: string, loader: () => Promise<T>, config?: LazyLoadConfig): Promise<T>;
    /** 取消加载 */
    cancel(id: string): boolean;
    /** 获取状态 */
    getStatus(id: string): LazyLoadStatus | undefined;
    /** 获取结果 */
    getResult(id: string): T | undefined;
    /** 清理 */
    cleanup(): void;
    /** 销毁 */
    destroy(): void;
}