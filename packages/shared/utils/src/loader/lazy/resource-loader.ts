/**
 * @fileoverview 懒加载资源工具
 * @description 提供各种资源的懒加载功能
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

import { LazyLoadManager } from './factory';
import type { LazyLoadConfig } from './types';
import { LazyLoadStrategy } from './types';

/**
 * 懒加载资源工具
 */
export class LazyResourceLoader {
    private static instance: LazyResourceLoader;
    private manager: LazyLoadManager;

    private constructor() {
        this.manager = new LazyLoadManager();
    }

    static getInstance(): LazyResourceLoader {
        if (!LazyResourceLoader.instance) {
            LazyResourceLoader.instance = new LazyResourceLoader();
        }
        return LazyResourceLoader.instance;
    }

    /**
     * 懒加载脚本
     */
    async loadScript(src: string, config?: LazyLoadConfig): Promise<HTMLScriptElement> {
        return this.manager.load(
            `script_${src}`,
            () => this.createScriptLoader(src),
            LazyLoadStrategy.DEFERRED,
            config
        );
    }

    /**
     * 懒加载样式
     */
    async loadStyle(href: string, config?: LazyLoadConfig): Promise<HTMLLinkElement> {
        return this.manager.load(
            `style_${href}`,
            () => this.createStyleLoader(href),
            LazyLoadStrategy.DEFERRED,
            config
        );
    }

    /**
     * 懒加载图片
     */
    async loadImage(src: string, config?: LazyLoadConfig): Promise<HTMLImageElement> {
        return this.manager.load(
            `image_${src}`,
            () => this.createImageLoader(src),
            LazyLoadStrategy.INTERSECTION,
            config
        );
    }

    /**
     * 懒加载模块
     */
    async loadModule<T = any>(modulePath: string, config?: LazyLoadConfig): Promise<T> {
        return this.manager.load(
            `module_${modulePath}`,
            () => import(modulePath),
            LazyLoadStrategy.ON_DEMAND,
            config
        );
    }

    private createScriptLoader(src: string): Promise<HTMLScriptElement> {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = src;
            script.onload = () => resolve(script);
            script.onerror = () => reject(new Error(`脚本加载失败: ${src}`));
            document.head.appendChild(script);
        });
    }

    private createStyleLoader(href: string): Promise<HTMLLinkElement> {
        return new Promise((resolve, reject) => {
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = href;
            link.onload = () => resolve(link);
            link.onerror = () => reject(new Error(`样式加载失败: ${href}`));
            document.head.appendChild(link);
        });
    }

    private createImageLoader(src: string): Promise<HTMLImageElement> {
        return new Promise((resolve, reject) => {
            const img = new Image();
            img.onload = () => resolve(img);
            img.onerror = () => reject(new Error(`图片加载失败: ${src}`));
            img.src = src;
        });
    }
}

/**
 * 全局懒加载资源加载器
 */
export const lazyResourceLoader = LazyResourceLoader.getInstance();