/**
 * @fileoverview 具体懒加载器实现
 * @description 提供各种策略的懒加载器实现
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

import { BaseLazyLoader } from './base-loader';
import type { LazyLoadItem } from './types';

/**
 * 立即加载器
 */
export class ImmediateLazyLoader<T = any> extends BaseLazyLoader<T> {
    protected async executeLoad(item: LazyLoadItem<T>): Promise<T> {
        return this.performLoad(item);
    }
}

/**
 * 延迟加载器
 */
export class DeferredLazyLoader<T = any> extends BaseLazyLoader<T> {
    protected async executeLoad(item: LazyLoadItem<T>): Promise<T> {
        if (item.config.delay! > 0) {
            await this.delay(item.config.delay!);
        }
        return this.performLoad(item);
    }
}

/**
 * 按需加载器
 */
export class OnDemandLazyLoader<T = any> extends BaseLazyLoader<T> {
    protected async executeLoad(item: LazyLoadItem<T>): Promise<T> {
        // 按需加载通常由外部触发，这里直接执行
        return this.performLoad(item);
    }
}

/**
 * 交集观察器加载器
 */
export class IntersectionLazyLoader<T = any> extends BaseLazyLoader<T> {
    private observers = new Map<string, IntersectionObserver>();

    protected async executeLoad(item: LazyLoadItem<T>): Promise<T> {
        return new Promise((resolve, reject) => {
            // 创建观察器
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        observer.disconnect();
                        this.observers.delete(item.id);
                        this.performLoad(item).then(resolve).catch(reject);
                    }
                });
            }, item.config.intersectionOptions);

            this.observers.set(item.id, observer);

            // 需要外部提供目标元素
            // 这里假设在配置中提供了目标元素
            const target = (item.config as any).target;
            if (target instanceof Element) {
                observer.observe(target);
            } else {
                reject(new Error('交集观察器需要目标元素'));
            }
        });
    }

    destroy(): void {
        super.destroy();
        this.observers.forEach(observer => observer.disconnect());
        this.observers.clear();
    }
}

/**
 * 空闲时加载器
 */
export class IdleLazyLoader<T = any> extends BaseLazyLoader<T> {
    protected async executeLoad(item: LazyLoadItem<T>): Promise<T> {
        return new Promise((resolve, reject) => {
            if (typeof requestIdleCallback !== 'undefined') {
                requestIdleCallback(() => {
                    this.performLoad(item).then(resolve).catch(reject);
                }, item.config.idleOptions);
            } else {
                // 降级到 setTimeout
                setTimeout(() => {
                    this.performLoad(item).then(resolve).catch(reject);
                }, 0);
            }
        });
    }
}

/**
 * 预加载器
 */
export class PreloadLazyLoader<T = any> extends BaseLazyLoader<T> {
    private preloadQueue: LazyLoadItem<T>[] = [];
    private isProcessing = false;

    protected async executeLoad(item: LazyLoadItem<T>): Promise<T> {
        // 添加到预加载队列
        this.preloadQueue.push(item);
        this.processQueue();

        return this.waitForCompletion(item);
    }

    private async processQueue(): Promise<void> {
        if (this.isProcessing || this.preloadQueue.length === 0) {
            return;
        }

        this.isProcessing = true;

        while (this.preloadQueue.length > 0) {
            const item = this.preloadQueue.shift()!;

            try {
                await this.performLoad(item);
            } catch (error) {
                // 预加载失败不影响队列处理
                console.warn('预加载失败:', error);
            }

            // 让出控制权
            await new Promise(resolve => setTimeout(resolve, 0));
        }

        this.isProcessing = false;
    }
}