/**
 * @fileoverview 懒加载系统
 * @description 提供多种懒加载策略和实现
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

// 导出类型定义
export * from './lazy/types';

// 导出基础加载器
export * from './lazy/base-loader';

// 导出具体加载器实现
export * from './lazy/loaders';

// 导出工厂和管理器
export * from './lazy/factory';

// 导出工具函数
export * from './lazy/utils';

// 导出资源加载器
export * from './lazy/resource-loader';

// 重新导出主要类和函数
export { BaseLazyLoader } from './lazy/base-loader';
export { defaultLazyLoadManager, LazyLoaderFactory, LazyLoadManager } from './lazy/factory';
export { LazyResourceLoader, lazyResourceLoader } from './lazy/resource-loader';
export { LazyLoadStatus, LazyLoadStrategy } from './lazy/types';
export type { LazyLoadConfig, LazyLoader, LazyLoadItem } from './lazy/types';
export { lazyLoad, lazyLoadUtils, withLazyLoad } from './lazy/utils';

// 默认导出
export { defaultLazyLoadManager as default } from './lazy/factory';
