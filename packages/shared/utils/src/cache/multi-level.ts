/**
 * @fileoverview 多级缓存管理器
 * @description 提供多级缓存的统一管理
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

import type { CacheManager, CacheStats } from './types';

/**
 * 多级缓存管理器
 */
export class MultiLevelCacheManager<T = any> implements CacheManager<T> {
    private levels: CacheManager<T>[];
    private stats: CacheStats;

    constructor(levels: CacheManager<T>[]) {
        if (levels.length === 0) {
            throw new Error('至少需要一个缓存级别');
        }

        this.levels = levels;
        this.stats = {
            hits: 0,
            misses: 0,
            hitRate: 0,
            size: 0,
            memoryUsage: 0,
            expiredCount: 0,
            evictedCount: 0
        };
    }

    get(key: string): T | undefined {
        for (let i = 0; i < this.levels.length; i++) {
            const value = this.levels[i].get(key);
            if (value !== undefined) {
                // 将值提升到更高级别的缓存
                for (let j = 0; j < i; j++) {
                    this.levels[j].set(key, value);
                }
                this.stats.hits++;
                this.updateHitRate();
                return value;
            }
        }

        this.stats.misses++;
        this.updateHitRate();
        return undefined;
    }

    set(key: string, value: T, ttl?: number): void {
        // 设置到所有级别
        this.levels.forEach(level => {
            level.set(key, value, ttl);
        });
        this.updateStats();
    }

    delete(key: string): boolean {
        let deleted = false;
        this.levels.forEach(level => {
            if (level.delete(key)) {
                deleted = true;
            }
        });
        this.updateStats();
        return deleted;
    }

    has(key: string): boolean {
        return this.levels.some(level => level.has(key));
    }

    clear(): void {
        this.levels.forEach(level => level.clear());
        this.updateStats();
    }

    size(): number {
        return Math.max(...this.levels.map(level => level.size()));
    }

    keys(): string[] {
        const allKeys = new Set<string>();
        this.levels.forEach(level => {
            level.keys().forEach(key => allKeys.add(key));
        });
        return Array.from(allKeys);
    }

    values(): T[] {
        const allValues = new Map<string, T>();
        // 从最高级别开始，避免重复
        for (const level of this.levels) {
            level.keys().forEach(key => {
                if (!allValues.has(key)) {
                    const value = level.get(key);
                    if (value !== undefined) {
                        allValues.set(key, value);
                    }
                }
            });
        }
        return Array.from(allValues.values());
    }

    getStats(): CacheStats {
        return { ...this.stats };
    }

    cleanup(): void {
        this.levels.forEach(level => level.cleanup());
        this.updateStats();
    }

    destroy(): void {
        this.levels.forEach(level => level.destroy());
    }

    private updateHitRate(): void {
        const total = this.stats.hits + this.stats.misses;
        this.stats.hitRate = total > 0 ? this.stats.hits / total : 0;
    }

    private updateStats(): void {
        this.stats.size = this.size();
        // 聚合所有级别的统计信息
        let totalMemoryUsage = 0;
        let totalExpiredCount = 0;
        let totalEvictedCount = 0;

        this.levels.forEach(level => {
            const levelStats = level.getStats();
            totalMemoryUsage += levelStats.memoryUsage;
            totalExpiredCount += levelStats.expiredCount;
            totalEvictedCount += levelStats.evictedCount;
        });

        this.stats.memoryUsage = totalMemoryUsage;
        this.stats.expiredCount = totalExpiredCount;
        this.stats.evictedCount = totalEvictedCount;
    }
}