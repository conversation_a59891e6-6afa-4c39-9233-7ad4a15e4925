/**
 * @fileoverview 缓存类型定义
 * @description 提供缓存相关的类型定义和接口
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

/**
 * 缓存策略枚举
 */
export enum CacheStrategy {
    /** 最近最少使用 */
    LRU = 'lru',
    /** 先进先出 */
    FIFO = 'fifo',
    /** 最少使用频率 */
    LFU = 'lfu',
    /** 基于时间的过期 */
    TTL = 'ttl'
}

/**
 * 缓存项接口
 */
export interface CacheItem<T = any> {
    /** 缓存键 */
    key: string;
    /** 缓存值 */
    value: T;
    /** 创建时间 */
    createdAt: number;
    /** 最后访问时间 */
    lastAccessedAt: number;
    /** 访问次数 */
    accessCount: number;
    /** 过期时间 */
    expiresAt?: number;
    /** 数据大小（字节） */
    size?: number;
}

/**
 * 缓存配置接口
 */
export interface CacheConfig {
    /** 缓存策略 */
    strategy?: CacheStrategy;
    /** 最大缓存项数量 */
    maxSize?: number;
    /** 最大内存使用（字节） */
    maxMemory?: number;
    /** 默认TTL（毫秒） */
    defaultTTL?: number;
    /** 是否启用统计 */
    enableStats?: boolean;
    /** 清理间隔（毫秒） */
    cleanupInterval?: number;
    /** 是否自动清理过期项 */
    autoCleanup?: boolean;
}

/**
 * 缓存统计信息
 */
export interface CacheStats {
    /** 缓存命中次数 */
    hits: number;
    /** 缓存未命中次数 */
    misses: number;
    /** 缓存命中率 */
    hitRate: number;
    /** 当前缓存项数量 */
    size: number;
    /** 内存使用量 */
    memoryUsage: number;
    /** 过期项数量 */
    expiredCount: number;
    /** 驱逐项数量 */
    evictedCount: number;
}

/**
 * 缓存管理器接口
 */
export interface CacheManager<T = any> {
    /** 获取缓存 */
    get(key: string): T | undefined;
    /** 设置缓存 */
    set(key: string, value: T, ttl?: number): void;
    /** 删除缓存 */
    delete(key: string): boolean;
    /** 检查缓存是否存在 */
    has(key: string): boolean;
    /** 清空缓存 */
    clear(): void;
    /** 获取缓存大小 */
    size(): number;
    /** 获取所有键 */
    keys(): string[];
    /** 获取所有值 */
    values(): T[];
    /** 获取统计信息 */
    getStats(): CacheStats;
    /** 清理过期项 */
    cleanup(): void;
    /** 销毁缓存管理器 */
    destroy(): void;
}