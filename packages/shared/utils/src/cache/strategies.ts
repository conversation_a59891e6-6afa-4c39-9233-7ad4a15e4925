/**
 * @fileoverview 缓存策略实现
 * @description 提供各种缓存策略的具体实现
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

import { BaseCacheManager } from './base-manager';

/**
 * LRU 缓存管理器
 */
export class LRUCacheManager<T = any> extends BaseCacheManager<T> {
    protected selectItemToEvict(): string | null {
        let oldestKey: string | null = null;
        let oldestTime = Infinity;

        for (const [key, item] of this.cache.entries()) {
            if (item.lastAccessedAt < oldestTime) {
                oldestTime = item.lastAccessedAt;
                oldestKey = key;
            }
        }

        return oldestKey;
    }
}

/**
 * FIFO 缓存管理器
 */
export class FIFOCacheManager<T = any> extends BaseCacheManager<T> {
    protected selectItemToEvict(): string | null {
        let oldestKey: string | null = null;
        let oldestTime = Infinity;

        for (const [key, item] of this.cache.entries()) {
            if (item.createdAt < oldestTime) {
                oldestTime = item.createdAt;
                oldestKey = key;
            }
        }

        return oldestKey;
    }
}

/**
 * LFU 缓存管理器
 */
export class LFUCacheManager<T = any> extends BaseCacheManager<T> {
    protected selectItemToEvict(): string | null {
        let leastUsedKey: string | null = null;
        let leastUsedCount = Infinity;

        for (const [key, item] of this.cache.entries()) {
            if (item.accessCount < leastUsedCount) {
                leastUsedCount = item.accessCount;
                leastUsedKey = key;
            }
        }

        return leastUsedKey;
    }
}

/**
 * TTL 缓存管理器
 */
export class TTLCacheManager<T = any> extends BaseCacheManager<T> {
    protected selectItemToEvict(): string | null {
        // TTL 策略优先清理过期项
        const now = Date.now();

        for (const [key, item] of this.cache.entries()) {
            if (this.isExpired(item)) {
                return key;
            }
        }

        // 如果没有过期项，选择最早过期的项
        let earliestExpiryKey: string | null = null;
        let earliestExpiryTime = Infinity;

        for (const [key, item] of this.cache.entries()) {
            const expiryTime = item.expiresAt || Infinity;
            if (expiryTime < earliestExpiryTime) {
                earliestExpiryTime = expiryTime;
                earliestExpiryKey = key;
            }
        }

        return earliestExpiryKey;
    }
}