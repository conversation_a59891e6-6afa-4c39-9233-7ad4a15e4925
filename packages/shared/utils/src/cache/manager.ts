/**
 * @fileoverview 缓存管理系统
 * @description 提供多种缓存策略的统一管理接口
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

// 导出类型定义
export * from './types';

// 导出基础管理器
export * from './base-manager';

// 导出策略实现
export * from './strategies';

// 导出工厂
export * from './factory';

// 导出多级缓存
export * from './multi-level';

// 重新导出主要类和函数
export { BaseCacheManager } from './base-manager';
export { CacheFactory, defaultCache } from './factory';
export { MultiLevelCacheManager } from './multi-level';
export { FIFOCacheManager, LFUCacheManager, LRUCacheManager, TTLCacheManager } from './strategies';
export { CacheStrategy } from './types';
export type { CacheConfig, CacheItem, CacheManager, CacheStats } from './types';

// 便捷的缓存函数
import { defaultCache } from './factory';
import type { CacheStats } from './types';

export const cache = {
    get: <T>(key: string): T | undefined => defaultCache.get(key),
    set: <T>(key: string, value: T, ttl?: number): void => defaultCache.set(key, value, ttl),
    delete: (key: string): boolean => defaultCache.delete(key),
    has: (key: string): boolean => defaultCache.has(key),
    clear: (): void => defaultCache.clear(),
    stats: (): CacheStats => defaultCache.getStats()
};

// 默认导出
export { defaultCache as default } from './factory';
