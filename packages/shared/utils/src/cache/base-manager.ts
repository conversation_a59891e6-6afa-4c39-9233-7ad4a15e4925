/**
 * @fileoverview 基础缓存管理器
 * @description 提供缓存管理器的基础实现
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

import type { CacheConfig, CacheItem, CacheManager, CacheStats } from './types';
import { CacheStrategy } from './types';

/**
 * 基础缓存管理器
 */
export abstract class BaseCacheManager<T = any> implements CacheManager<T> {
    protected cache = new Map<string, CacheItem<T>>();
    protected config: Required<CacheConfig>;
    protected stats: CacheStats;
    private cleanupTimer: NodeJS.Timeout | null = null;

    constructor(config: CacheConfig = {}) {
        this.config = {
            strategy: CacheStrategy.LRU,
            maxSize: 1000,
            maxMemory: 50 * 1024 * 1024, // 50MB
            defaultTTL: 0, // 永不过期
            enableStats: true,
            cleanupInterval: 60000, // 1分钟
            autoCleanup: true,
            ...config
        };

        this.stats = {
            hits: 0,
            misses: 0,
            hitRate: 0,
            size: 0,
            memoryUsage: 0,
            expiredCount: 0,
            evictedCount: 0
        };

        if (this.config.autoCleanup) {
            this.startCleanupTimer();
        }
    }

    /**
     * 获取缓存
     */
    get(key: string): T | undefined {
        const item = this.cache.get(key);

        if (!item) {
            this.updateStats('miss');
            return undefined;
        }

        // 检查是否过期
        if (this.isExpired(item)) {
            this.cache.delete(key);
            this.updateStats('miss');
            this.stats.expiredCount++;
            return undefined;
        }

        // 更新访问信息
        this.updateAccessInfo(item);
        this.updateStats('hit');

        return item.value;
    }

    /**
     * 设置缓存
     */
    set(key: string, value: T, ttl?: number): void {
        const now = Date.now();
        const effectiveTTL = ttl ?? this.config.defaultTTL;

        const item: CacheItem<T> = {
            key,
            value,
            createdAt: now,
            lastAccessedAt: now,
            accessCount: 0,
            expiresAt: effectiveTTL > 0 ? now + effectiveTTL : undefined,
            size: this.calculateSize(value)
        };

        // 如果键已存在，先删除旧项
        if (this.cache.has(key)) {
            this.cache.delete(key);
        }

        // 检查是否需要驱逐
        this.evictIfNecessary();

        // 添加新项
        this.cache.set(key, item);
        this.updateCacheStats();
    }

    /**
     * 删除缓存
     */
    delete(key: string): boolean {
        const deleted = this.cache.delete(key);
        if (deleted) {
            this.updateCacheStats();
        }
        return deleted;
    }

    /**
     * 检查缓存是否存在
     */
    has(key: string): boolean {
        const item = this.cache.get(key);
        if (!item) {
            return false;
        }

        if (this.isExpired(item)) {
            this.cache.delete(key);
            this.stats.expiredCount++;
            return false;
        }

        return true;
    }

    /**
     * 清空缓存
     */
    clear(): void {
        this.cache.clear();
        this.updateCacheStats();
    }

    /**
     * 获取缓存大小
     */
    size(): number {
        return this.cache.size;
    }

    /**
     * 获取所有键
     */
    keys(): string[] {
        return Array.from(this.cache.keys());
    }

    /**
     * 获取所有值
     */
    values(): T[] {
        return Array.from(this.cache.values()).map(item => item.value);
    }

    /**
     * 获取统计信息
     */
    getStats(): CacheStats {
        return { ...this.stats };
    }

    /**
     * 清理过期项
     */
    cleanup(): void {
        const now = Date.now();
        let cleanedCount = 0;

        for (const [key, item] of this.cache.entries()) {
            if (this.isExpired(item)) {
                this.cache.delete(key);
                cleanedCount++;
            }
        }

        if (cleanedCount > 0) {
            this.stats.expiredCount += cleanedCount;
            this.updateCacheStats();
        }
    }

    /**
     * 销毁缓存管理器
     */
    destroy(): void {
        if (this.cleanupTimer) {
            clearInterval(this.cleanupTimer);
            this.cleanupTimer = null;
        }
        this.clear();
    }

    /**
     * 抽象方法：选择要驱逐的项
     */
    protected abstract selectItemToEvict(): string | null;

    /**
     * 检查项是否过期
     */
    protected isExpired(item: CacheItem<T>): boolean {
        return item.expiresAt !== undefined && Date.now() > item.expiresAt;
    }

    /**
     * 更新访问信息
     */
    protected updateAccessInfo(item: CacheItem<T>): void {
        item.lastAccessedAt = Date.now();
        item.accessCount++;
    }

    /**
     * 计算值的大小
     */
    protected calculateSize(value: T): number {
        try {
            return JSON.stringify(value).length * 2; // 粗略估算
        } catch {
            return 0;
        }
    }

    /**
     * 驱逐项（如果必要）
     */
    protected evictIfNecessary(): void {
        // 检查数量限制
        while (this.cache.size >= this.config.maxSize) {
            const keyToEvict = this.selectItemToEvict();
            if (keyToEvict) {
                this.cache.delete(keyToEvict);
                this.stats.evictedCount++;
            } else {
                break;
            }
        }

        // 检查内存限制
        while (this.getCurrentMemoryUsage() > this.config.maxMemory) {
            const keyToEvict = this.selectItemToEvict();
            if (keyToEvict) {
                this.cache.delete(keyToEvict);
                this.stats.evictedCount++;
            } else {
                break;
            }
        }
    }

    /**
     * 获取当前内存使用量
     */
    protected getCurrentMemoryUsage(): number {
        let totalSize = 0;
        for (const item of this.cache.values()) {
            totalSize += item.size || 0;
        }
        return totalSize;
    }

    /**
     * 更新统计信息
     */
    protected updateStats(type: 'hit' | 'miss'): void {
        if (!this.config.enableStats) {
            return;
        }

        if (type === 'hit') {
            this.stats.hits++;
        } else {
            this.stats.misses++;
        }

        const total = this.stats.hits + this.stats.misses;
        this.stats.hitRate = total > 0 ? this.stats.hits / total : 0;
    }

    /**
     * 更新缓存统计信息
     */
    protected updateCacheStats(): void {
        this.stats.size = this.cache.size;
        this.stats.memoryUsage = this.getCurrentMemoryUsage();
    }

    /**
     * 启动清理定时器
     */
    private startCleanupTimer(): void {
        this.cleanupTimer = setInterval(() => {
            this.cleanup();
        }, this.config.cleanupInterval);
    }
}