/**
 * @fileoverview 缓存工厂
 * @description 提供缓存管理器的创建工厂
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

import { FIFOCacheManager, LFUCacheManager, LRUCacheManager, TTLCacheManager } from './strategies';
import type { CacheConfig, CacheManager } from './types';
import { CacheStrategy } from './types';

/**
 * 缓存工厂
 */
export class CacheFactory {
    /**
     * 创建缓存管理器
     */
    static create<T = any>(strategy: CacheStrategy, config?: CacheConfig): CacheManager<T> {
        switch (strategy) {
            case CacheStrategy.LRU:
                return new LRUCacheManager<T>(config);
            case CacheStrategy.FIFO:
                return new FIFOCacheManager<T>(config);
            case CacheStrategy.LFU:
                return new LFUCacheManager<T>(config);
            case CacheStrategy.TTL:
                return new TTLCacheManager<T>(config);
            default:
                throw new Error(`不支持的缓存策略: ${strategy}`);
        }
    }

    /**
     * 创建 LRU 缓存
     */
    static createLRU<T = any>(config?: CacheConfig): LRUCacheManager<T> {
        return new LRUCacheManager<T>(config);
    }

    /**
     * 创建 FIFO 缓存
     */
    static createFIFO<T = any>(config?: CacheConfig): FIFOCacheManager<T> {
        return new FIFOCacheManager<T>(config);
    }

    /**
     * 创建 LFU 缓存
     */
    static createLFU<T = any>(config?: CacheConfig): LFUCacheManager<T> {
        return new LFUCacheManager<T>(config);
    }

    /**
     * 创建 TTL 缓存
     */
    static createTTL<T = any>(config?: CacheConfig): TTLCacheManager<T> {
        return new TTLCacheManager<T>(config);
    }
}

/**
 * 默认缓存实例
 */
export const defaultCache = CacheFactory.createLRU({
    maxSize: 1000,
    defaultTTL: 300000, // 5分钟
    enableStats: true
});