/**
 * @fileoverview 对象工具函数测试
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

import { describe, expect, it } from 'vitest';
import {
    deepClone,
    deepMerge,
    entries,
    filter,
    flatten,
    fromEntries,
    get,
    has,
    invert,
    keys,
    mapKeys,
    mapValues,
    objectUtils,
    omit,
    pick,
    set,
    unflatten,
    unset,
    values
} from '../src/object';

describe('对象工具函数', () => {
    describe('deepMerge', () => {
        it('应该深度合并对象', () => {
            const target = { a: 1, b: { c: 2 } };
            const source = { b: { d: 3 }, e: 4 };
            const result = deepMerge(target, source);

            expect(result).toEqual({
                a: 1,
                b: { c: 2, d: 3 },
                e: 4
            });
        });

        it('应该处理多个源对象', () => {
            const target = { a: 1 };
            const source1 = { b: 2 };
            const source2 = { c: 3 };
            const result = deepMerge(target, source1, source2);

            expect(result).toEqual({ a: 1, b: 2, c: 3 });
        });

        it('应该覆盖原始值', () => {
            const target = { a: 1, b: 2 };
            const source = { b: 3, c: 4 };
            const result = deepMerge(target, source);

            expect(result).toEqual({ a: 1, b: 3, c: 4 });
        });
    });

    describe('deepClone', () => {
        it('应该深度克隆对象', () => {
            const original = {
                a: 1,
                b: { c: 2, d: [3, 4] },
                e: new Date('2023-01-01'),
                f: /test/g
            };
            const cloned = deepClone(original);

            expect(cloned).toEqual(original);
            expect(cloned).not.toBe(original);
            expect(cloned.b).not.toBe(original.b);
            expect(cloned.b.d).not.toBe(original.b.d);
        });

        it('应该处理循环引用', () => {
            const original: any = { a: 1 };
            original.self = original;

            const cloned = deepClone(original);
            expect(cloned.a).toBe(1);
            expect(cloned.self).toBe(cloned);
        });

        it('应该克隆数组', () => {
            const original = [1, { a: 2 }, [3, 4]];
            const cloned = deepClone(original);

            expect(cloned).toEqual(original);
            expect(cloned).not.toBe(original);
            expect(cloned[1]).not.toBe(original[1]);
            expect(cloned[2]).not.toBe(original[2]);
        });

        it('应该克隆Map和Set', () => {
            const map = new Map([['a', 1], ['b', 2]]);
            const set = new Set([1, 2, 3]);

            const clonedMap = deepClone(map);
            const clonedSet = deepClone(set);

            expect(clonedMap).toBeInstanceOf(Map);
            expect(clonedMap).not.toBe(map);
            expect(clonedMap.get('a')).toBe(1);
            expect(clonedMap.get('b')).toBe(2);

            expect(clonedSet).toBeInstanceOf(Set);
            expect(clonedSet).not.toBe(set);
            expect(clonedSet.has(1)).toBe(true);
            expect(clonedSet.has(2)).toBe(true);
            expect(clonedSet.has(3)).toBe(true);
        });
    });

    describe('get', () => {
        const obj = {
            a: 1,
            b: {
                c: 2,
                d: {
                    e: 3
                }
            },
            f: [4, 5, { g: 6 }]
        };

        it('应该获取嵌套属性值', () => {
            expect(get(obj, 'a')).toBe(1);
            expect(get(obj, 'b.c')).toBe(2);
            expect(get(obj, 'b.d.e')).toBe(3);
            expect(get(obj, 'f.2.g')).toBe(6);
        });

        it('应该返回默认值', () => {
            expect(get(obj, 'x', 'default')).toBe('default');
            expect(get(obj, 'b.x', 'default')).toBe('default');
            expect(get(obj, 'b.d.x', 'default')).toBe('default');
        });

        it('应该支持数组路径', () => {
            expect(get(obj, ['b', 'c'])).toBe(2);
            expect(get(obj, ['b', 'd', 'e'])).toBe(3);
        });
    });

    describe('set', () => {
        it('应该设置嵌套属性值', () => {
            const obj = {};
            set(obj, 'a.b.c', 123);

            expect(obj).toEqual({
                a: {
                    b: {
                        c: 123
                    }
                }
            });
        });

        it('应该覆盖现有值', () => {
            const obj = { a: { b: 1 } };
            set(obj, 'a.b', 2);

            expect(obj.a.b).toBe(2);
        });

        it('应该支持数组路径', () => {
            const obj = {};
            set(obj, ['a', 'b', 'c'], 123);

            expect(get(obj, 'a.b.c')).toBe(123);
        });
    });

    describe('unset', () => {
        it('应该删除属性', () => {
            const obj = { a: { b: { c: 1 } } };
            const result = unset(obj, 'a.b.c');

            expect(result).toBe(true);
            expect(obj.a.b).toEqual({});
        });

        it('应该处理不存在的路径', () => {
            const obj = { a: 1 };
            const result = unset(obj, 'b.c');

            expect(result).toBe(false);
            expect(obj).toEqual({ a: 1 });
        });
    });

    describe('has', () => {
        const obj = { a: { b: { c: null } } };

        it('应该检查属性是否存在', () => {
            expect(has(obj, 'a')).toBe(true);
            expect(has(obj, 'a.b')).toBe(true);
            expect(has(obj, 'a.b.c')).toBe(true);
            expect(has(obj, 'a.b.d')).toBe(false);
            expect(has(obj, 'x')).toBe(false);
        });
    });

    describe('pick', () => {
        it('应该选择指定属性', () => {
            const obj = { a: 1, b: 2, c: 3 };
            const result = pick(obj, ['a', 'c']);

            expect(result).toEqual({ a: 1, c: 3 });
        });

        it('应该忽略不存在的属性', () => {
            const obj = { a: 1, b: 2 };
            const result = pick(obj, ['a', 'c'] as any);

            expect(result).toEqual({ a: 1 });
        });
    });

    describe('omit', () => {
        it('应该排除指定属性', () => {
            const obj = { a: 1, b: 2, c: 3 };
            const result = omit(obj, ['b']);

            expect(result).toEqual({ a: 1, c: 3 });
        });
    });

    describe('keys', () => {
        it('应该获取对象键', () => {
            const obj = { a: 1, b: 2, c: 3 };
            const result = keys(obj);

            expect(result).toEqual(['a', 'b', 'c']);
        });
    });

    describe('values', () => {
        it('应该获取对象值', () => {
            const obj = { a: 1, b: 2, c: 3 };
            const result = values(obj);

            expect(result).toEqual([1, 2, 3]);
        });
    });

    describe('entries', () => {
        it('应该获取键值对数组', () => {
            const obj = { a: 1, b: 2 };
            const result = entries(obj);

            expect(result).toEqual([['a', 1], ['b', 2]]);
        });
    });

    describe('fromEntries', () => {
        it('应该从键值对数组创建对象', () => {
            const entries = [['a', 1], ['b', 2]] as const;
            const result = fromEntries(entries);

            expect(result).toEqual({ a: 1, b: 2 });
        });
    });

    describe('invert', () => {
        it('应该反转对象键值', () => {
            const obj = { a: '1', b: '2' };
            const result = invert(obj);

            expect(result).toEqual({ '1': 'a', '2': 'b' });
        });
    });

    describe('mapValues', () => {
        it('应该映射对象值', () => {
            const obj = { a: 1, b: 2, c: 3 };
            const result = mapValues(obj, (value) => value * 2);

            expect(result).toEqual({ a: 2, b: 4, c: 6 });
        });
    });

    describe('mapKeys', () => {
        it('应该映射对象键', () => {
            const obj = { a: 1, b: 2 };
            const result = mapKeys(obj, (key) => key.toUpperCase());

            expect(result).toEqual({ A: 1, B: 2 });
        });
    });

    describe('filter', () => {
        it('应该过滤对象属性', () => {
            const obj = { a: 1, b: 2, c: 3, d: 4 };
            const result = filter(obj, (value) => value % 2 === 0);

            expect(result).toEqual({ b: 2, d: 4 });
        });
    });

    describe('flatten', () => {
        it('应该扁平化对象', () => {
            const obj = {
                a: 1,
                b: {
                    c: 2,
                    d: {
                        e: 3
                    }
                }
            };
            const result = flatten(obj);

            expect(result).toEqual({
                'a': 1,
                'b.c': 2,
                'b.d.e': 3
            });
        });

        it('应该支持自定义分隔符', () => {
            const obj = { a: { b: 1 } };
            const result = flatten(obj, '', '/');

            expect(result).toEqual({ 'a/b': 1 });
        });
    });

    describe('unflatten', () => {
        it('应该展开扁平化的对象', () => {
            const obj = {
                'a': 1,
                'b.c': 2,
                'b.d.e': 3
            };
            const result = unflatten(obj);

            expect(result).toEqual({
                a: 1,
                b: {
                    c: 2,
                    d: {
                        e: 3
                    }
                }
            });
        });
    });

    describe('objectUtils 对象', () => {
        it('应该包含所有对象工具函数', () => {
            expect(objectUtils.deepMerge).toBe(deepMerge);
            expect(objectUtils.deepClone).toBe(deepClone);
            expect(objectUtils.get).toBe(get);
            expect(objectUtils.set).toBe(set);
            expect(objectUtils.pick).toBe(pick);
            expect(objectUtils.omit).toBe(omit);
        });
    });
});
