/**
 * @fileoverview 核心类型检查工具函数测试
 * @description 测试从 @micro-core/core 迁移的类型检查函数
 */

import {
    coreTypeUtils,
    isArray,
    isBoolean,
    isEmpty,
    isFunction,
    isNumber,
    isObject,
    isPromise,
    isString,
    TYPE_CHECK_FUNCTIONS,
    validateTypeCheckFunctions
} from '../src/type-check/core';

describe('Core Type Check Functions', () => {
    describe('isObject', () => {
        it('should return true for plain objects', () => {
            expect(isObject({})).toBe(true);
            expect(isObject({ a: 1 })).toBe(true);
            expect(isObject({ nested: { value: true } })).toBe(true);
        });

        it('should return false for arrays', () => {
            expect(isObject([])).toBe(false);
            expect(isObject([1, 2, 3])).toBe(false);
        });

        it('should return false for null and undefined', () => {
            expect(isObject(null)).toBe(false);
            expect(isObject(undefined)).toBe(false);
        });

        it('should return false for primitive types', () => {
            expect(isObject('string')).toBe(false);
            expect(isObject(123)).toBe(false);
            expect(isObject(true)).toBe(false);
        });

        it('should return true for Date objects', () => {
            expect(isObject(new Date())).toBe(true);
        });

        it('should return true for RegExp objects', () => {
            expect(isObject(/regex/)).toBe(true);
        });
    });

    describe('isFunction', () => {
        it('should return true for functions', () => {
            expect(isFunction(() => { })).toBe(true);
            expect(isFunction(function () { })).toBe(true);
            expect(isFunction(function named() { })).toBe(true);
            expect(isFunction(async () => { })).toBe(true);
            expect(isFunction(function* generator() { })).toBe(true);
        });

        it('should return true for built-in functions', () => {
            expect(isFunction(console.log)).toBe(true);
            expect(isFunction(Array.isArray)).toBe(true);
            expect(isFunction(Object.keys)).toBe(true);
        });

        it('should return false for non-functions', () => {
            expect(isFunction({})).toBe(false);
            expect(isFunction([])).toBe(false);
            expect(isFunction('function')).toBe(false);
            expect(isFunction(123)).toBe(false);
            expect(isFunction(null)).toBe(false);
            expect(isFunction(undefined)).toBe(false);
        });
    });

    describe('isString', () => {
        it('should return true for strings', () => {
            expect(isString('')).toBe(true);
            expect(isString('hello')).toBe(true);
            expect(isString('123')).toBe(true);
            expect(isString(String(123))).toBe(true);
        });

        it('should return false for non-strings', () => {
            expect(isString(123)).toBe(false);
            expect(isString(true)).toBe(false);
            expect(isString({})).toBe(false);
            expect(isString([])).toBe(false);
            expect(isString(null)).toBe(false);
            expect(isString(undefined)).toBe(false);
        });
    });

    describe('isNumber', () => {
        it('should return true for valid numbers', () => {
            expect(isNumber(0)).toBe(true);
            expect(isNumber(123)).toBe(true);
            expect(isNumber(-123)).toBe(true);
            expect(isNumber(3.14)).toBe(true);
            expect(isNumber(Number.MAX_VALUE)).toBe(true);
            expect(isNumber(Number.MIN_VALUE)).toBe(true);
            expect(isNumber(Infinity)).toBe(true);
            expect(isNumber(-Infinity)).toBe(true);
        });

        it('should return false for NaN', () => {
            expect(isNumber(NaN)).toBe(false);
            expect(isNumber(Number.NaN)).toBe(false);
        });

        it('should return false for non-numbers', () => {
            expect(isNumber('123')).toBe(false);
            expect(isNumber(true)).toBe(false);
            expect(isNumber({})).toBe(false);
            expect(isNumber([])).toBe(false);
            expect(isNumber(null)).toBe(false);
            expect(isNumber(undefined)).toBe(false);
        });
    });

    describe('isBoolean', () => {
        it('should return true for booleans', () => {
            expect(isBoolean(true)).toBe(true);
            expect(isBoolean(false)).toBe(true);
            expect(isBoolean(Boolean(1))).toBe(true);
            expect(isBoolean(Boolean(0))).toBe(true);
        });

        it('should return false for non-booleans', () => {
            expect(isBoolean(1)).toBe(false);
            expect(isBoolean(0)).toBe(false);
            expect(isBoolean('true')).toBe(false);
            expect(isBoolean('false')).toBe(false);
            expect(isBoolean({})).toBe(false);
            expect(isBoolean([])).toBe(false);
            expect(isBoolean(null)).toBe(false);
            expect(isBoolean(undefined)).toBe(false);
        });
    });

    describe('isArray', () => {
        it('should return true for arrays', () => {
            expect(isArray([])).toBe(true);
            expect(isArray([1, 2, 3])).toBe(true);
            expect(isArray(new Array())).toBe(true);
            expect(isArray(Array.from('hello'))).toBe(true);
        });

        it('should return false for array-like objects', () => {
            expect(isArray({ length: 0 })).toBe(false);
            expect(isArray({ 0: 'a', 1: 'b', length: 2 })).toBe(false);
        });

        it('should return false for non-arrays', () => {
            expect(isArray({})).toBe(false);
            expect(isArray('array')).toBe(false);
            expect(isArray(123)).toBe(false);
            expect(isArray(null)).toBe(false);
            expect(isArray(undefined)).toBe(false);
        });
    });

    describe('isPromise', () => {
        it('should return true for Promises', () => {
            expect(isPromise(Promise.resolve())).toBe(true);
            expect(isPromise(Promise.reject().catch(() => { }))).toBe(true);
            expect(isPromise(new Promise(() => { }))).toBe(true);
        });

        it('should return true for thenable objects', () => {
            const thenable = { then: () => { } };
            expect(isPromise(thenable)).toBe(true);

            const customThenable = {
                then: (resolve: Function) => resolve('value')
            };
            expect(isPromise(customThenable)).toBe(true);
        });

        it('should return false for non-promises', () => {
            expect(isPromise({})).toBe(false);
            expect(isPromise({ then: 'not a function' })).toBe(false);
            expect(isPromise([])).toBe(false);
            expect(isPromise('promise')).toBe(false);
            expect(isPromise(123)).toBe(false);
            expect(isPromise(null)).toBe(false);
            expect(isPromise(undefined)).toBe(false);
        });

        it('should handle async functions', async () => {
            const asyncFn = async () => 'result';
            const result = asyncFn();
            expect(isPromise(result)).toBe(true);
            await result; // Clean up
        });
    });

    describe('isEmpty', () => {
        it('should return true for null and undefined', () => {
            expect(isEmpty(null)).toBe(true);
            expect(isEmpty(undefined)).toBe(true);
        });

        it('should return true for empty strings', () => {
            expect(isEmpty('')).toBe(true);
        });

        it('should return false for non-empty strings', () => {
            expect(isEmpty('hello')).toBe(false);
            expect(isEmpty(' ')).toBe(false);
            expect(isEmpty('0')).toBe(false);
        });

        it('should return true for empty arrays', () => {
            expect(isEmpty([])).toBe(true);
        });

        it('should return false for non-empty arrays', () => {
            expect(isEmpty([1])).toBe(false);
            expect(isEmpty([null])).toBe(false);
            expect(isEmpty([undefined])).toBe(false);
        });

        it('should return true for empty objects', () => {
            expect(isEmpty({})).toBe(true);
        });

        it('should return false for non-empty objects', () => {
            expect(isEmpty({ a: 1 })).toBe(false);
            expect(isEmpty({ a: null })).toBe(false);
            expect(isEmpty({ a: undefined })).toBe(false);
        });

        it('should return false for other types', () => {
            expect(isEmpty(0)).toBe(false);
            expect(isEmpty(false)).toBe(false);
            // Date and RegExp objects are treated as objects, so they're empty if they have no enumerable properties
            expect(isEmpty(new Date())).toBe(true); // Date objects have no enumerable properties
            expect(isEmpty(/regex/)).toBe(true); // RegExp objects have no enumerable properties
        });
    });

    describe('coreTypeUtils', () => {
        it('should contain all type check functions', () => {
            expect(typeof coreTypeUtils.isObject).toBe('function');
            expect(typeof coreTypeUtils.isFunction).toBe('function');
            expect(typeof coreTypeUtils.isString).toBe('function');
            expect(typeof coreTypeUtils.isNumber).toBe('function');
            expect(typeof coreTypeUtils.isBoolean).toBe('function');
            expect(typeof coreTypeUtils.isArray).toBe('function');
            expect(typeof coreTypeUtils.isPromise).toBe('function');
            expect(typeof coreTypeUtils.isEmpty).toBe('function');
        });

        it('should have the same functions as individual exports', () => {
            expect(coreTypeUtils.isObject).toBe(isObject);
            expect(coreTypeUtils.isFunction).toBe(isFunction);
            expect(coreTypeUtils.isString).toBe(isString);
            expect(coreTypeUtils.isNumber).toBe(isNumber);
            expect(coreTypeUtils.isBoolean).toBe(isBoolean);
            expect(coreTypeUtils.isArray).toBe(isArray);
            expect(coreTypeUtils.isPromise).toBe(isPromise);
            expect(coreTypeUtils.isEmpty).toBe(isEmpty);
        });
    });

    describe('TYPE_CHECK_FUNCTIONS', () => {
        it('should contain all function names', () => {
            expect(TYPE_CHECK_FUNCTIONS).toEqual([
                'isObject',
                'isFunction',
                'isString',
                'isNumber',
                'isBoolean',
                'isArray',
                'isPromise',
                'isEmpty'
            ]);
        });

        it('should be readonly', () => {
            // In TypeScript, 'as const' makes the array readonly at compile time
            // At runtime, we can still modify it, but TypeScript will prevent it
            expect(TYPE_CHECK_FUNCTIONS).toHaveLength(8);
            expect(Array.isArray(TYPE_CHECK_FUNCTIONS)).toBe(true);
        });
    });

    describe('validateTypeCheckFunctions', () => {
        it('should validate all functions are available', () => {
            const result = validateTypeCheckFunctions();

            expect(result.allAvailable).toBe(true);
            expect(result.missing).toEqual([]);
            expect(result.available).toEqual([
                'isObject',
                'isFunction',
                'isString',
                'isNumber',
                'isBoolean',
                'isArray',
                'isPromise',
                'isEmpty'
            ]);
        });
    });

    describe('Type Guards', () => {
        it('should work as TypeScript type guards', () => {
            const value: unknown = 'hello';

            if (isString(value)) {
                // TypeScript should know value is string here
                expect(value.length).toBe(5);
                expect(value.toUpperCase()).toBe('HELLO');
            }
        });

        it('should narrow types correctly', () => {
            const values: unknown[] = [
                'string',
                123,
                true,
                [],
                {},
                () => { },
                Promise.resolve(),
                null,
                undefined
            ];

            values.forEach(value => {
                if (isString(value)) {
                    expect(typeof value).toBe('string');
                } else if (isNumber(value)) {
                    expect(typeof value).toBe('number');
                } else if (isBoolean(value)) {
                    expect(typeof value).toBe('boolean');
                } else if (isArray(value)) {
                    expect(Array.isArray(value)).toBe(true);
                } else if (isObject(value)) {
                    expect(typeof value).toBe('object');
                    expect(value).not.toBeNull();
                    expect(Array.isArray(value)).toBe(false);
                } else if (isFunction(value)) {
                    expect(typeof value).toBe('function');
                } else if (isPromise(value)) {
                    expect(typeof value).toBe('object');
                    expect(value).not.toBeNull();
                    expect('then' in value).toBe(true);
                }
            });
        });
    });

    describe('Edge Cases', () => {
        it('should handle Symbol values', () => {
            const sym = Symbol('test');
            expect(isObject(sym)).toBe(false);
            expect(isFunction(sym)).toBe(false);
            expect(isString(sym)).toBe(false);
            expect(isNumber(sym)).toBe(false);
            expect(isBoolean(sym)).toBe(false);
            expect(isArray(sym)).toBe(false);
            expect(isPromise(sym)).toBe(false);
            expect(isEmpty(sym)).toBe(false);
        });

        it('should handle BigInt values', () => {
            const bigInt = BigInt(123);
            expect(isObject(bigInt)).toBe(false);
            expect(isFunction(bigInt)).toBe(false);
            expect(isString(bigInt)).toBe(false);
            expect(isNumber(bigInt)).toBe(false);
            expect(isBoolean(bigInt)).toBe(false);
            expect(isArray(bigInt)).toBe(false);
            expect(isPromise(bigInt)).toBe(false);
            expect(isEmpty(bigInt)).toBe(false);
        });

        it('should handle circular references in objects', () => {
            const circular: any = { a: 1 };
            circular.self = circular;

            expect(isObject(circular)).toBe(true);
            expect(isEmpty(circular)).toBe(false);
        });

        it('should handle objects with null prototype', () => {
            const nullProto = Object.create(null);
            expect(isObject(nullProto)).toBe(true);
            expect(isEmpty(nullProto)).toBe(true);

            nullProto.prop = 'value';
            expect(isEmpty(nullProto)).toBe(false);
        });
    });

    describe('Performance', () => {
        it('should be fast for large arrays', () => {
            const largeArray = new Array(10000).fill(0);
            const start = performance.now();

            for (let i = 0; i < 1000; i++) {
                isArray(largeArray);
                isEmpty(largeArray);
            }

            const end = performance.now();
            expect(end - start).toBeLessThan(100); // Should complete in less than 100ms
        });

        it('should be fast for large objects', () => {
            const largeObject: Record<string, number> = {};
            for (let i = 0; i < 1000; i++) {
                largeObject[`key${i}`] = i;
            }

            const start = performance.now();

            for (let i = 0; i < 1000; i++) {
                isObject(largeObject);
                isEmpty(largeObject);
            }

            const end = performance.now();
            expect(end - start).toBeLessThan(100); // Should complete in less than 100ms
        });
    });
});