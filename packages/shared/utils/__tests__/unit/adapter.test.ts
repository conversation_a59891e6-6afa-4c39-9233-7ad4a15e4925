/**
 * @fileoverview 适配器系统测试
 * @description 测试适配器基础设施的功能
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

import {
    AdapterDependencies,
    AdapterRegistry,
    AdapterStatus,
    BaseAdapter,
    BaseAdapterConfig,
    cleanupContainer,
    createAdapterFactory,
    createContainer,
    detectFramework,
    EnhancedAdapterFactory,
    formatAdapterError,
    mergeConfigs,
    validateConfig
} from '../src/adapter';

// Mock 适配器实现
class MockAdapter extends BaseAdapter {
    canHandle(appConfig: any): boolean {
        return appConfig.framework === 'mock';
    }

    async load(appConfig: BaseAdapterConfig): Promise<void> {
        this.setStatus(AdapterStatus.LOADING);
        await new Promise(resolve => setTimeout(resolve, 10));
        this.setStatus(AdapterStatus.LOADED);
    }

    async mount(): Promise<void> {
        this.setStatus(AdapterStatus.MOUNTING);
        await new Promise(resolve => setTimeout(resolve, 10));
        this.setStatus(AdapterStatus.MOUNTED);
    }

    async unmount(): Promise<void> {
        this.setStatus(AdapterStatus.UNMOUNTING);
        await new Promise(resolve => setTimeout(resolve, 10));
        this.setStatus(AdapterStatus.UNMOUNTED);
    }

    async update(props: any): Promise<void> {
        // Mock update implementation
    }
}

// Mock 依赖
const mockDependencies: AdapterDependencies = {
    lifecycleManager: {
        beforeLoad: jest.fn(),
        beforeMount: jest.fn(),
        afterMount: jest.fn(),
        beforeUnmount: jest.fn(),
        afterUnmount: jest.fn()
    },
    sandboxManager: {
        createSandbox: jest.fn(),
        destroySandbox: jest.fn(),
        getSandbox: jest.fn()
    },
    communicationManager: {
        emit: jest.fn(),
        on: jest.fn(),
        off: jest.fn(),
        broadcast: jest.fn()
    }
};

describe('适配器基础设施测试', () => {
    describe('BaseAdapter', () => {
        let adapter: MockAdapter;
        let config: BaseAdapterConfig;

        beforeEach(() => {
            config = {
                name: 'test-app',
                framework: 'mock',
                entry: 'http://localhost:3000'
            };
            adapter = new MockAdapter(config, mockDependencies);
        });

        test('应该正确初始化适配器', () => {
            expect(adapter.getStatus()).toBe(AdapterStatus.NOT_LOADED);
            expect(adapter.getConfig()).toEqual(config);
            expect(adapter.getAppInstance()).toBeUndefined();
            expect(adapter.getContainer()).toBeUndefined();
            expect(adapter.getError()).toBeUndefined();
        });

        test('应该正确处理加载流程', async () => {
            expect(adapter.canHandle(config)).toBe(true);

            await adapter.load(config);
            expect(adapter.getStatus()).toBe(AdapterStatus.LOADED);
        });

        test('应该正确处理挂载流程', async () => {
            await adapter.load(config);
            await adapter.mount();
            expect(adapter.getStatus()).toBe(AdapterStatus.MOUNTED);
        });

        test('应该正确处理卸载流程', async () => {
            await adapter.load(config);
            await adapter.mount();
            await adapter.unmount();
            expect(adapter.getStatus()).toBe(AdapterStatus.UNMOUNTED);
        });

        test('应该正确处理销毁流程', async () => {
            await adapter.load(config);
            await adapter.mount();
            await adapter.destroy();
            expect(adapter.getStatus()).toBe(AdapterStatus.UNMOUNTED);
        });
    });

    describe('AdapterFactory', () => {
        let factory: EnhancedAdapterFactory;

        beforeEach(() => {
            factory = new EnhancedAdapterFactory();
            factory.registerAdapter('mock', MockAdapter);
        });

        test('应该正确注册和创建适配器', () => {
            expect(factory.isSupported('mock')).toBe(true);
            expect(factory.getSupportedTypes()).toContain('mock');

            const config: BaseAdapterConfig = {
                name: 'test-app',
                framework: 'mock'
            };

            const adapter = factory.createAdapter('mock', config, mockDependencies);
            expect(adapter).toBeInstanceOf(MockAdapter);
            expect(adapter.getConfig()).toEqual(config);
        });

        test('应该在不支持的类型时抛出错误', () => {
            const config: BaseAdapterConfig = {
                name: 'test-app',
                framework: 'unsupported'
            };

            expect(() => {
                factory.createAdapter('unsupported', config, mockDependencies);
            }).toThrow('Unsupported adapter type: unsupported');
        });

        test('应该正确验证配置', () => {
            const validConfig: BaseAdapterConfig = {
                name: 'test-app',
                framework: 'mock',
                entry: 'http://localhost:3000'
            };

            const result = factory.validateConfig('mock', validConfig);
            expect(result.valid).toBe(true);
            expect(result.errors).toHaveLength(0);

            const invalidConfig = {
                framework: 'mock'
            } as BaseAdapterConfig;

            const invalidResult = factory.validateConfig('mock', invalidConfig);
            expect(invalidResult.valid).toBe(false);
            expect(invalidResult.errors).toContain('应用名称不能为空');
        });
    });

    describe('AdapterRegistry', () => {
        let registry: AdapterRegistry;

        beforeEach(() => {
            registry = AdapterRegistry.getInstance();
        });

        test('应该是单例模式', () => {
            const registry2 = AdapterRegistry.getInstance();
            expect(registry).toBe(registry2);
        });

        test('应该正确注册和创建适配器', () => {
            registry.registerAdapter('mock', MockAdapter);

            const config: BaseAdapterConfig = {
                name: 'test-app',
                framework: 'mock'
            };

            const adapter = registry.createAdapter('mock', config, mockDependencies);
            expect(adapter).toBeInstanceOf(MockAdapter);

            const retrievedAdapter = registry.getAdapter('test-app');
            expect(retrievedAdapter).toBe(adapter);
        });

        test('应该正确移除适配器', () => {
            registry.registerAdapter('mock', MockAdapter);

            const config: BaseAdapterConfig = {
                name: 'test-app',
                framework: 'mock'
            };

            registry.createAdapter('mock', config, mockDependencies);
            expect(registry.getAdapter('test-app')).toBeDefined();

            registry.removeAdapter('test-app');
            expect(registry.getAdapter('test-app')).toBeUndefined();
        });
    });

    describe('工具函数测试', () => {
        test('detectFramework 应该正确检测框架类型', () => {
            expect(detectFramework('http://localhost:3000/react-app.js')).toBe('react');
            expect(detectFramework('http://localhost:3000/vue-app.js')).toBe('vue3');
            expect(detectFramework('http://localhost:3000/vue@2/app.js')).toBe('vue2');
            expect(detectFramework('http://localhost:3000/angular-app.js')).toBe('angular');
            expect(detectFramework('http://localhost:3000/svelte-app.js')).toBe('svelte');
            expect(detectFramework('http://localhost:3000/solid-app.js')).toBe('solid');
            expect(detectFramework('http://localhost:3000/app.html')).toBe('html');
        });

        test('mergeConfigs 应该正确合并配置', () => {
            const base = {
                name: 'app',
                framework: 'react',
                props: { a: 1, b: { c: 2 } }
            };

            const override = {
                framework: 'vue3',
                props: { b: { d: 3 }, e: 4 }
            };

            const result = mergeConfigs(base, override);
            expect(result).toEqual({
                name: 'app',
                framework: 'vue3',
                props: { a: 1, b: { c: 2, d: 3 }, e: 4 }
            });
        });

        test('validateConfig 应该正确验证配置', () => {
            const config = {
                name: 'test-app',
                framework: 'react',
                entry: 'http://localhost:3000'
            };

            const schema = {
                required: ['name', 'framework'] as (keyof typeof config)[],
                validators: {
                    name: (value: string) => value.length > 0,
                    entry: (value: string) => value.startsWith('http')
                }
            };

            const result = validateConfig(config, schema);
            expect(result.valid).toBe(true);
            expect(result.errors).toHaveLength(0);

            const invalidConfig = {
                name: '',
                framework: 'react',
                entry: 'invalid-url'
            };

            const invalidResult = validateConfig(invalidConfig, schema);
            expect(invalidResult.valid).toBe(false);
            expect(invalidResult.errors).toContain('字段验证失败: name');
            expect(invalidResult.errors).toContain('字段验证失败: entry');
        });

        test('createContainer 应该创建正确的容器', () => {
            const container = createContainer('test-app');

            expect(container.id).toBe('micro-app-test-app');
            expect(container.className).toBe('micro-app-container');
            expect(container.style.position).toBe('relative');
            expect(container.style.width).toBe('100%');
            expect(container.style.height).toBe('100%');

            // 清理
            cleanupContainer(container);
        });

        test('formatAdapterError 应该正确格式化错误', () => {
            const error = new Error('Test error');
            const context = {
                adapterName: 'ReactAdapter',
                appName: 'test-app',
                operation: 'mount'
            };

            const formatted = formatAdapterError(error, context);

            expect(formatted).toContain('[ReactAdapter]');
            expect(formatted).toContain('[test-app]');
            expect(formatted).toContain('[mount]');
            expect(formatted).toContain('Error: Test error');
        });
    });

    describe('容器管理测试', () => {
        test('应该正确创建和清理容器', () => {
            const container = createContainer('test-app');

            // 验证容器已添加到 DOM
            expect(document.body.contains(container)).toBe(true);

            // 清理容器
            cleanupContainer(container);

            // 验证容器已从 DOM 移除
            expect(document.body.contains(container)).toBe(false);
        });

        test('应该支持自定义父元素', () => {
            const parent = document.createElement('div');
            document.body.appendChild(parent);

            const container = createContainer('test-app', parent);

            // 验证容器添加到指定父元素
            expect(parent.contains(container)).toBe(true);
            expect(document.body.contains(container)).toBe(true);

            // 清理
            cleanupContainer(container);
            document.body.removeChild(parent);
        });
    });
});

describe('适配器工厂构建器测试', () => {
    test('应该支持链式配置', () => {
        const factory = createAdapterFactory({
            autoDetect: true,
            enableCache: true,
            defaultType: 'html'
        });

        expect(factory).toBeInstanceOf(EnhancedAdapterFactory);
    });
});