/**
 * @fileoverview 类型定义测试
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

import { describe, expect, it } from 'vitest';
import { AppStatus } from '../../constants/src';
import type {
    AnyFunction,
    AppEntry,
    AppLifecycle,
    AsyncFunction,
    Cache,
    Constructor,
    DeepPartial,
    DeepReadonly,
    EventEmitter,
    EventListener,
    KeyValuePair,
    Lifecycle,
    LogRecord,
    Logger,
    Message,
    MicroAppConfig,
    MicroAppInstance,
    Result,
    RouteInfo,
    // 工具类型
    Serializable,
    Task,
    TypeGuard
} from '../src/index';
import { LogLevel, TaskStatus } from '../src/index';

describe('类型定义', () => {
    describe('微前端应用相关类型', () => {
        it('MicroAppConfig 应该有正确的结构', () => {
            const config: MicroAppConfig = {
                name: 'test-app',
                entry: 'http://localhost:3000'
            };

            expect(config.name).toBe('test-app');
            expect(config.entry).toBe('http://localhost:3000');

            // 测试可选属性
            const fullConfig: MicroAppConfig = {
                name: 'test-app',
                entry: {
                    scripts: ['http://localhost:3000/main.js'],
                    styles: ['http://localhost:3000/main.css'],
                    html: 'http://localhost:3000/index.html'
                },
                container: '#app',
                activeRule: '/test-app',
                props: { theme: 'dark' },
                loader: {
                    timeout: 30000,
                    retries: 3,
                    cache: true
                },
                sandbox: {
                    enabled: true,
                    type: 'proxy',
                    strict: true
                },
                styleIsolation: {
                    enabled: true,
                    type: 'scoped'
                },
                hooks: {
                    beforeLoad: async () => { },
                    afterLoad: async () => { }
                },
                meta: {
                    version: '1.0.0',
                    description: 'Test app'
                }
            };

            expect(fullConfig).toBeDefined();
        });

        it('AppEntry 应该支持多种入口类型', () => {
            const entry: AppEntry = {
                scripts: ['main.js', 'vendor.js'],
                styles: ['main.css', 'theme.css'],
                html: 'index.html'
            };

            expect(entry.scripts).toHaveLength(2);
            expect(entry.styles).toHaveLength(2);
            expect(entry.html).toBe('index.html');
        });

        it('MicroAppInstance 应该包含应用实例信息', () => {
            const instance: MicroAppInstance = {
                name: 'test-app',
                status: AppStatus.MOUNTED,
                config: {
                    name: 'test-app',
                    entry: 'http://localhost:3000'
                },
                createdAt: new Date(),
                updatedAt: new Date()
            };

            expect(instance.name).toBe('test-app');
            expect(instance.status).toBe(AppStatus.MOUNTED);
            expect(instance.config.name).toBe('test-app');
        });
    });

    describe('生命周期相关类型', () => {
        it('AppLifecycle 应该定义应用生命周期函数', () => {
            const lifecycle: AppLifecycle = {
                bootstrap: async (props) => {
                    expect(props).toBeDefined();
                },
                mount: async (props) => {
                    expect(props).toBeDefined();
                },
                unmount: async (props) => {
                    expect(props).toBeDefined();
                },
                update: async (props) => {
                    expect(props).toBeDefined();
                }
            };

            expect(lifecycle.bootstrap).toBeTypeOf('function');
            expect(lifecycle.mount).toBeTypeOf('function');
            expect(lifecycle.unmount).toBeTypeOf('function');
            expect(lifecycle.update).toBeTypeOf('function');
        });

        it('Lifecycle 应该定义通用生命周期', () => {
            const lifecycle: Lifecycle = {
                init: async () => { },
                start: async () => { },
                stop: async () => { },
                destroy: async () => { }
            };

            expect(lifecycle.init).toBeTypeOf('function');
            expect(lifecycle.start).toBeTypeOf('function');
            expect(lifecycle.stop).toBeTypeOf('function');
            expect(lifecycle.destroy).toBeTypeOf('function');
        });
    });

    describe('通信相关类型', () => {
        it('Message 应该定义消息结构', () => {
            const message: Message<string> = {
                id: 'msg-001',
                type: 'test',
                from: 'app1',
                to: 'app2',
                data: 'hello',
                timestamp: Date.now(),
                needReply: true
            };

            expect(message.id).toBe('msg-001');
            expect(message.type).toBe('test');
            expect(message.data).toBe('hello');
            expect(typeof message.timestamp).toBe('number');
        });

        it('RouteInfo 应该定义路由信息', () => {
            const route: RouteInfo = {
                path: '/test',
                query: { id: '123' },
                hash: '#section1',
                state: { from: 'home' }
            };

            expect(route.path).toBe('/test');
            expect(route.query?.id).toBe('123');
            expect(route.hash).toBe('#section1');
        });
    });

    describe('事件相关类型', () => {
        it('EventListener 应该定义事件监听器', () => {
            const listener: EventListener<string> = (data) => {
                expect(typeof data).toBe('string');
            };

            expect(listener).toBeTypeOf('function');
        });

        it('EventEmitter 应该定义事件发射器接口', () => {
            const emitter: EventEmitter = {
                on: (event, listener) => () => { },
                once: (event, listener) => () => { },
                off: (event, listener) => { },
                emit: (event, data) => { }
            };

            expect(emitter.on).toBeTypeOf('function');
            expect(emitter.once).toBeTypeOf('function');
            expect(emitter.off).toBeTypeOf('function');
            expect(emitter.emit).toBeTypeOf('function');
        });
    });

    describe('日志相关类型', () => {
        it('LogLevel 应该定义日志级别', () => {
            expect(LogLevel.DEBUG).toBe(0);
            expect(LogLevel.INFO).toBe(1);
            expect(LogLevel.WARN).toBe(2);
            expect(LogLevel.ERROR).toBe(3);
            expect(LogLevel.SILENT).toBe(4);
        });

        it('LogRecord 应该定义日志记录', () => {
            const record: LogRecord = {
                level: LogLevel.INFO,
                message: 'Test message',
                data: ['arg1', 'arg2'],
                timestamp: new Date(),
                source: 'test-app'
            };

            expect(record.level).toBe(LogLevel.INFO);
            expect(record.message).toBe('Test message');
            expect(record.data).toEqual(['arg1', 'arg2']);
        });

        it('Logger 应该定义日志器接口', () => {
            const logger: Logger = {
                debug: (message, ...args) => { },
                info: (message, ...args) => { },
                warn: (message, ...args) => { },
                error: (message, ...args) => { },
                setLevel: (level) => { },
                child: (namespace) => ({} as Logger)
            };

            expect(logger.debug).toBeTypeOf('function');
            expect(logger.info).toBeTypeOf('function');
            expect(logger.warn).toBeTypeOf('function');
            expect(logger.error).toBeTypeOf('function');
        });
    });

    describe('缓存相关类型', () => {
        it('Cache 应该定义缓存接口', () => {
            const cache: Cache<string, number> = {
                get: (key) => undefined,
                set: (key, value, ttl) => { },
                delete: (key) => false,
                clear: () => { },
                has: (key) => false,
                size: () => 0,
                keys: () => [],
                values: () => []
            };

            expect(cache.get).toBeTypeOf('function');
            expect(cache.set).toBeTypeOf('function');
            expect(cache.delete).toBeTypeOf('function');
            expect(cache.clear).toBeTypeOf('function');
        });
    });

    describe('任务相关类型', () => {
        it('TaskStatus 应该定义任务状态', () => {
            expect(TaskStatus.PENDING).toBe('pending');
            expect(TaskStatus.RUNNING).toBe('running');
            expect(TaskStatus.COMPLETED).toBe('completed');
            expect(TaskStatus.FAILED).toBe('failed');
            expect(TaskStatus.CANCELLED).toBe('cancelled');
        });

        it('Task 应该定义任务结构', () => {
            const task: Task<string> = {
                id: 'task-001',
                name: 'Test Task',
                status: TaskStatus.PENDING,
                result: 'success',
                progress: 50,
                createdAt: new Date(),
                startedAt: new Date(),
                completedAt: new Date(),
                cancel: () => { }
            };

            expect(task.id).toBe('task-001');
            expect(task.name).toBe('Test Task');
            expect(task.status).toBe(TaskStatus.PENDING);
            expect(task.progress).toBe(50);
        });
    });

    describe('工具类型', () => {
        it('Serializable 应该支持可序列化类型', () => {
            const data: Serializable = {
                string: 'hello',
                number: 123,
                boolean: true,
                null: null,
                undefined: undefined,
                array: [1, 2, 3],
                object: { nested: 'value' }
            };

            expect(data).toBeDefined();
        });

        it('DeepReadonly 应该创建深度只读类型', () => {
            type ReadonlyConfig = DeepReadonly<{
                name: string;
                settings: {
                    theme: string;
                    debug: boolean;
                }
            }>;

            const config: ReadonlyConfig = {
                name: 'test',
                settings: {
                    theme: 'dark',
                    debug: true
                }
            };

            expect(config.name).toBe('test');
            expect(config.settings.theme).toBe('dark');
        });

        it('DeepPartial 应该创建深度可选类型', () => {
            type PartialConfig = DeepPartial<{
                name: string;
                settings: {
                    theme: string;
                    debug: boolean;
                }
            }>;

            const config: PartialConfig = {
                settings: {
                    theme: 'dark'
                }
            };

            expect(config.settings?.theme).toBe('dark');
        });

        it('Result 应该定义结果类型', () => {
            const success: Result<string> = {
                success: true,
                data: 'result'
            };

            const failure: Result<string> = {
                success: false,
                error: new Error('failed')
            };

            expect(success.success).toBe(true);
            if (success.success) {
                expect(success.data).toBe('result');
            }

            expect(failure.success).toBe(false);
            if (!failure.success) {
                expect(failure.error).toBeInstanceOf(Error);
            }
        });

        it('KeyValuePair 应该定义键值对', () => {
            const pair: KeyValuePair<string, number> = {
                key: 'count',
                value: 42
            };

            expect(pair.key).toBe('count');
            expect(pair.value).toBe(42);
        });

        it('TypeGuard 应该定义类型守卫', () => {
            const isString: TypeGuard<string> = (value): value is string => {
                return typeof value === 'string';
            };

            expect(isString('hello')).toBe(true);
            expect(isString(123)).toBe(false);
        });
    });

    describe('函数类型', () => {
        it('AnyFunction 应该接受任意函数', () => {
            const fn1: AnyFunction = () => { };
            const fn2: AnyFunction = (a: number, b: string) => a + b;
            const fn3: AnyFunction = async () => 'result';

            expect(fn1).toBeTypeOf('function');
            expect(fn2).toBeTypeOf('function');
            expect(fn3).toBeTypeOf('function');
        });

        it('AsyncFunction 应该定义异步函数', () => {
            const asyncFn: AsyncFunction<string> = async () => 'result';

            expect(asyncFn).toBeTypeOf('function');
        });

        it('Constructor 应该定义构造函数', () => {
            class TestClass {
                constructor(public name: string) { }
            }

            const ctor: Constructor<TestClass> = TestClass;
            const instance = new ctor('test');

            expect(instance.name).toBe('test');
        });
    });
});
