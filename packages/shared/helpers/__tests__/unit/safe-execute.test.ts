/**
 * @fileoverview 安全执行函数测试
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

import { beforeEach, describe, expect, it, vi } from 'vitest';
import { safeExecute } from '../src/index';

describe('安全执行函数', () => {
    beforeEach(() => {
        vi.resetAllMocks();
        vi.useFakeTimers();
    });

    afterEach(() => {
        vi.useRealTimers();
    });

    it('应该安全执行函数并返回结果', async () => {
        const fn = vi.fn().mockResolvedValue('成功');
        const result = await safeExecute(fn);

        expect(result).toBe('成功');
        expect(fn).toHaveBeenCalledOnce();
    });

    it('应该处理错误并返回备用值', async () => {
        const fn = vi.fn().mockRejectedValue(new Error('测试错误'));
        const onError = vi.fn();
        const result = await safeExecute(fn, { fallback: '备用值', onError });

        expect(result).toBe('备用值');
        expect(onError).toHaveBeenCalledWith(expect.any(Error));
    });

    it('应该支持重试机制', async () => {
        const fn = vi.fn()
            .mockRejectedValueOnce(new Error('第一次错误'))
            .mockRejectedValueOnce(new Error('第二次错误'))
            .mockResolvedValue('成功');

        const result = await safeExecute(fn, { retries: 2, retryDelay: 10 });

        expect(result).toBe('成功');
        expect(fn).toHaveBeenCalledTimes(3);
    });

    it('应该在达到最大重试次数后返回备用值', async () => {
        const fn = vi.fn().mockRejectedValue(new Error('持续错误'));
        const onError = vi.fn();

        const result = await safeExecute(fn, {
            retries: 3,
            retryDelay: 10,
            fallback: '最终备用值',
            onError
        });

        expect(result).toBe('最终备用值');
        expect(fn).toHaveBeenCalledTimes(4); // 初始调用 + 3次重试
        expect(onError).toHaveBeenCalledTimes(4);
    });

    it('应该支持超时机制', async () => {
        const fn = vi.fn(() => new Promise(resolve => {
            setTimeout(() => resolve('延迟结果'), 1000);
        }));

        const promise = safeExecute(fn, { timeout: 500, fallback: '超时备用值' });

        // 快进时间超过超时时间
        vi.advanceTimersByTime(600);

        const result = await promise;
        expect(result).toBe('超时备用值');
    });

    it('应该在超时前返回结果', async () => {
        const fn = vi.fn(() => new Promise(resolve => {
            setTimeout(() => resolve('延迟结果'), 300);
        }));

        const promise = safeExecute(fn, { timeout: 500, fallback: '超时备用值' });

        // 快进时间但不超过超时时间
        vi.advanceTimersByTime(400);

        const result = await promise;
        expect(result).toBe('延迟结果');
    });

    it('应该支持自定义验证函数', async () => {
        const fn = vi.fn().mockResolvedValue('无效结果');
        const validate = vi.fn(result => result === '有效结果');

        const result = await safeExecute(fn, {
            validate,
            fallback: '验证失败备用值'
        });

        expect(result).toBe('验证失败备用值');
        expect(validate).toHaveBeenCalledWith('无效结果');
    });

    it('应该在验证通过时返回原始结果', async () => {
        const fn = vi.fn().mockResolvedValue('有效结果');
        const validate = vi.fn(result => result === '有效结果');

        const result = await safeExecute(fn, { validate });

        expect(result).toBe('有效结果');
        expect(validate).toHaveBeenCalledWith('有效结果');
    });

    it('应该支持同步函数', async () => {
        const fn = vi.fn(() => '同步结果');
        const result = await safeExecute(fn);

        expect(result).toBe('同步结果');
    });

    it('应该处理同步函数抛出的错误', async () => {
        const fn = vi.fn(() => {
            throw new Error('同步错误');
        });

        const onError = vi.fn();
        const result = await safeExecute(fn, {
            fallback: '同步错误备用值',
            onError
        });

        expect(result).toBe('同步错误备用值');
        expect(onError).toHaveBeenCalledWith(expect.any(Error));
    });
});