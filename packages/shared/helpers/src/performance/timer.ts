/**
 * @fileoverview 性能计时器
 * @description 提供高精度的性能计时功能
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

import { createLogger } from '../../../utils/src/logger';

const logger = createLogger('micro-core:performance');

/**
 * 性能计时器接口
 */
export interface PerformanceTimer {
    start(): void;
    end(): number;
    reset(): void;
    getElapsed(): number;
}

/**
 * 创建性能计时器
 */
export function createPerformanceTimer(label?: string): PerformanceTimer {
    let startTime = 0;
    let endTime = 0;
    const timerLabel = label || `timer-${Date.now()}`;

    return {
        start() {
            startTime = performance.now();
            if (label) {
                logger.debug(`性能计时开始: ${timerLabel}`);
            }
        },

        end() {
            endTime = performance.now();
            const elapsed = endTime - startTime;
            if (label) {
                logger.debug(`性能计时结束: ${timerLabel}, 耗时: ${elapsed.toFixed(2)}ms`);
            }
            return elapsed;
        },

        reset() {
            startTime = 0;
            endTime = 0;
        },

        getElapsed() {
            const currentTime = endTime || performance.now();
            return startTime ? currentTime - startTime : 0;
        }
    };
}