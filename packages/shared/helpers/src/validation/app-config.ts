/**
 * @fileoverview 应用配置验证器
 * @description 提供应用配置的验证和规范化功能
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

import { isFunction, isObject, isString } from '../../../utils/src/type-check';

/**
 * 应用配置验证器
 */
export function validateAppConfig(config: any): string[] {
    const errors: string[] = [];

    if (!isObject(config)) {
        errors.push('配置必须是一个对象');
        return errors;
    }

    if (!isString(config.name) || config.name.trim() === '') {
        errors.push('应用名称(name)必须是非空字符串');
    }

    if (!isString(config.entry) || config.entry.trim() === '') {
        errors.push('应用入口(entry)必须是非空字符串');
    }

    if (config.container && !isString(config.container)) {
        errors.push('容器选择器(container)必须是字符串');
    }

    if (config.activeRule && !isString(config.activeRule) && !isFunction(config.activeRule)) {
        errors.push('激活规则(activeRule)必须是字符串或函数');
    }

    if (config.props && !isObject(config.props)) {
        errors.push('应用属性(props)必须是对象');
    }

    return errors;
}

/**
 * 应用配置规范化器
 */
export function normalizeAppConfig(config: any): any {
    const normalizedName = config.name.trim();
    return {
        name: normalizedName,
        entry: typeof config.entry === 'string' ? config.entry.trim() : config.entry,
        container: config.container || `#${normalizedName}`,
        activeRule: typeof config.activeRule === 'string' ? config.activeRule : `/${normalizedName}`,
        props: config.props || {},
        loader: config.loader,
        sandbox: config.sandbox
    };
}
