/**
 * @fileoverview 内存监控工具 - 负责监控内存使用和检测内存泄漏
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

import { createLogger } from '../../../utils';

/**
 * 内存监控器
 * 负责监控微前端应用的内存使用情况，检测潜在的内存泄漏
 */
export class MemoryMonitor {
    private logger = createLogger('[MemoryMonitor]');
    private memoryLeakDetector: WeakSet<object> = new WeakSet();
    private cleanupTasks: Array<() => void> = [];
    private monitoringInterval?: NodeJS.Timeout;

    constructor(private config: { enableMonitoring?: boolean; monitorInterval?: number } = {}) {
        this.logger.debug('内存监控器初始化完成');
    }

    /**
     * 开始内存监控
     */
    start(): void {
        if (this.monitoringInterval) {
            this.logger.warn('内存监控已启动，忽略重复启动请求');
            return;
        }

        if (!this.config.enableMonitoring) {
            this.logger.debug('内存监控已禁用');
            return;
        }

        const interval = this.config.monitorInterval || 30000; // 默认30秒
        this.monitoringInterval = setInterval(() => {
            this.checkMemoryUsage();
        }, interval);

        this.logger.info('内存监控启动完成');
    }

    /**
     * 停止内存监控
     */
    stop(): void {
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
            this.monitoringInterval = undefined;
            this.logger.info('内存监控停止完成');
        }
    }

    /**
     * 添加对象到内存泄漏检测器
     */
    track(obj: object): void {
        this.memoryLeakDetector.add(obj);
    }

    /**
     * 添加清理任务
     */
    addCleanupTask(task: () => void): void {
        this.cleanupTasks.push(task);
    }

    /**
     * 执行所有清理任务
     */
    cleanup(): void {
        this.logger.debug('执行内存清理任务');

        for (const task of this.cleanupTasks) {
            try {
                task();
            } catch (error) {
                this.logger.error('清理任务执行失败:', error);
            }
        }

        this.cleanupTasks.length = 0;
        this.logger.info('内存清理完成');
    }

    /**
     * 检查内存使用情况
     */
    private checkMemoryUsage(): void {
        if (typeof performance !== 'undefined' && performance.memory) {
            const memory = performance.memory;
            const usedMB = Math.round(memory.usedJSHeapSize / 1024 / 1024);
            const totalMB = Math.round(memory.totalJSHeapSize / 1024 / 1024);
            const limitMB = Math.round(memory.jsHeapSizeLimit / 1024 / 1024);

            this.logger.debug(`内存使用情况: ${usedMB}MB / ${totalMB}MB (限制: ${limitMB}MB)`);

            // 如果内存使用超过80%，发出警告
            const usagePercent = (usedMB / limitMB) * 100;
            if (usagePercent > 80) {
                this.logger.warn(`内存使用率过高: ${usagePercent.toFixed(1)}%`);
            }
        }
    }

    /**
     * 获取内存使用统计
     */
    getMemoryStats(): { used: number; total: number; limit: number } | null {
        if (typeof performance !== 'undefined' && performance.memory) {
            const memory = performance.memory;
            return {
                used: Math.round(memory.usedJSHeapSize / 1024 / 1024),
                total: Math.round(memory.totalJSHeapSize / 1024 / 1024),
                limit: Math.round(memory.jsHeapSizeLimit / 1024 / 1024)
            };
        }
        return null;
    }
}