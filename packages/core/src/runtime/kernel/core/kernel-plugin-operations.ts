/**
 * @fileoverview 内核插件操作管理 - 负责插件的安装、获取和管理
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

import type { Plugin } from '../../../types';
import type { PluginManager } from '../managers/plugin-manager';

/**
 * 内核插件操作管理器
 * 负责插件的安装、获取、检查等操作
 */
export class KernelPluginOperations {
    constructor(
        private pluginManager: PluginManager,
        private checkDestroyed: () => void
    ) { }

    /**
     * 使用插件
     */
    use(plugin: Plugin, options?: any): void {
        this.checkDestroyed();
        this.pluginManager.use(plugin, options);
    }

    /**
     * 获取插件
     */
    getPlugin(name: string): Plugin | null {
        this.checkDestroyed();
        return this.pluginManager.get(name);
    }

    /**
     * 获取所有已安装的插件名称列表
     */
    getPlugins(): string[] {
        this.checkDestroyed();
        return this.pluginManager.getAll();
    }

    /**
     * 检查插件是否已安装
     */
    hasPlugin(name: string): boolean {
        this.checkDestroyed();
        return this.pluginManager.has(name);
    }
}