/**
 * @fileoverview 内核生命周期管理 - 负责内核的启动、停止和销毁
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

import type { EventBus } from '../../../communication/event-bus';
import { ERROR_CODES, MicroCoreError } from '../../../errors';
import { createLogger } from '../../../utils';
import type { AppManager } from '../managers/app-manager';
import type { PluginManager } from '../managers/plugin-manager';
import type { RouteListener } from '../routing/route-listener';
import type { MemoryMonitor } from '../utils/memory-monitor';

/**
 * 内核生命周期管理器
 * 负责内核的启动、停止和销毁流程
 */
export class KernelLifecycle {
    private logger = createLogger('[KernelLifecycle]');
    private isStarted = false;
    private isDestroyed = false;

    constructor(
        private eventBus: EventBus,
        private appManager: AppManager,
        private pluginManager: PluginManager,
        private routeListener: RouteListener,
        private memoryMonitor: MemoryMonitor
    ) { }

    /**
     * 启动内核
     */
    async start(): Promise<void> {
        this.checkDestroyed();

        if (this.isStarted) {
            this.logger.warn('内核已启动，忽略重复启动请求');
            return;
        }

        this.logger.info('启动微前端内核...');

        try {
            // 发送启动事件
            this.eventBus.emit('kernel:starting');

            // 启动路由监听
            this.routeListener.start();

            // 启动内存监控
            this.memoryMonitor.start();

            this.isStarted = true;

            // 发送启动完成事件
            this.eventBus.emit('kernel:started');

            this.logger.info('微前端内核启动完成');
        } catch (error) {
            this.logger.error('内核启动失败:', error);
            throw new MicroCoreError(
                ERROR_CODES.OPERATION_FAILED,
                '内核启动失败',
                {},
                error instanceof Error ? error : new Error(String(error))
            );
        }
    }

    /**
     * 停止内核
     */
    async stop(): Promise<void> {
        this.checkDestroyed();

        if (!this.isStarted) {
            this.logger.warn('内核未启动，忽略停止请求');
            return;
        }

        this.logger.info('停止微前端内核...');

        try {
            // 发送停止事件
            this.eventBus.emit('kernel:stopping');

            // 卸载所有已挂载的应用
            const mountedApps = this.appManager.getByStatus('MOUNTED');
            for (const app of mountedApps) {
                try {
                    await this.unmountApplication(app.name);
                } catch (error) {
                    this.logger.error(`卸载应用 ${app.name} 失败:`, error);
                }
            }

            // 停止路由监听
            this.routeListener.stop();

            // 停止内存监控
            this.memoryMonitor.stop();

            this.isStarted = false;

            // 发送停止完成事件
            this.eventBus.emit('kernel:stopped');

            this.logger.info('微前端内核停止完成');
        } catch (error) {
            this.logger.error('内核停止失败:', error);
            throw new MicroCoreError(
                ERROR_CODES.OPERATION_FAILED,
                '内核停止失败',
                {},
                error instanceof Error ? error : new Error(String(error))
            );
        }
    }

    /**
     * 销毁内核
     */
    async destroy(): Promise<void> {
        if (this.isDestroyed) {
            this.logger.warn('内核已销毁，忽略重复销毁请求');
            return;
        }

        this.logger.info('销毁微前端内核...');

        try {
            // 先停止内核
            if (this.isStarted) {
                await this.stop();
            }

            // 发送销毁事件
            this.eventBus.emit('kernel:destroying');

            // 清理各个管理器
            this.appManager.clear();
            this.pluginManager.clear();
            this.memoryMonitor.cleanup();

            // 清理事件监听器
            this.eventBus.removeAllListeners();

            // 发送销毁完成事件
            this.eventBus.emit('kernel:destroyed');

            this.logger.info('微前端内核销毁完成');
        } catch (error) {
            this.logger.error('内核销毁失败:', error);
            // 不抛出异常，让销毁过程继续
        } finally {
            // 确保无论如何都标记为已销毁
            this.isDestroyed = true;
        }
    }

    /**
     * 检查内核是否已启动
     */
    isKernelStarted(): boolean {
        return this.isStarted;
    }

    /**
     * 检查内核是否已销毁
     */
    isKernelDestroyed(): boolean {
        return this.isDestroyed;
    }

    /**
     * 卸载应用的私有方法
     */
    private async unmountApplication(name: string): Promise<void> {
        // 这里需要访问生命周期管理器，暂时留空
        // 实际实现需要在重构完成后补充
        this.logger.debug(`卸载应用: ${name}`);
    }

    /**
     * 检查内核是否已销毁
     */
    private checkDestroyed(): void {
        if (this.isDestroyed) {
            throw new MicroCoreError(
                ERROR_CODES.OPERATION_FAILED,
                '内核已销毁，无法执行操作'
            );
        }
    }
}