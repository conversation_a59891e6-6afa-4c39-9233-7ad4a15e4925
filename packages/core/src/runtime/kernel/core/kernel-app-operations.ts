/**
 * @fileoverview 内核应用操作管理 - 负责应用的加载、挂载和卸载操作
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

import { ERROR_CODES, MicroCoreError } from '../../../errors';
import type { AppInstance } from '../../../types';
import { createLogger } from '../../../utils';
import type { LifecycleManager } from '../../lifecycle-manager';
import type { AppManager } from '../managers/app-manager';
import type { ResourceManager } from '../managers/resource-manager';

/**
 * 内核应用操作管理器
 * 负责应用的加载、挂载、卸载等核心操作
 */
export class KernelAppOperations {
    private logger = createLogger('[KernelAppOperations]');

    constructor(
        private appManager: AppManager,
        private lifecycleManager: LifecycleManager,
        private getResourceManager: () => ResourceManager
    ) { }

    /**
     * 加载应用
     */
    async loadApplication(name: string): Promise<void> {
        const app = this.getApplicationOrThrow(name);
        await this.getResourceManager().loadApplication(app);
    }

    /**
     * 挂载应用
     */
    async mountApplication(name: string): Promise<void> {
        const app = this.getApplicationOrThrow(name);

        try {
            // 如果应用未加载，先加载
            if (app.status === 'NOT_LOADED') {
                await this.loadApplication(name);
            }

            // 如果应用未引导，先引导
            if (app.status === 'NOT_BOOTSTRAPPED') {
                await this.lifecycleManager.bootstrap(app);
            }

            // 挂载应用
            if (app.status === 'NOT_MOUNTED') {
                await this.lifecycleManager.mount(app);
            }

            this.logger.info(`应用 ${name} 挂载完成`);
        } catch (error) {
            this.appManager.setError(name, error instanceof Error ? error : new Error(String(error)));
            throw error;
        }
    }

    /**
     * 卸载应用
     */
    async unmountApplication(name: string): Promise<void> {
        const app = this.getApplicationOrThrow(name);

        try {
            if (app.status === 'MOUNTED') {
                await this.lifecycleManager.unmount(app);
            }
            this.logger.info(`应用 ${name} 卸载完成`);
        } catch (error) {
            this.appManager.setError(name, error instanceof Error ? error : new Error(String(error)));
            throw error;
        }
    }

    /**
     * 获取应用或抛出异常
     */
    private getApplicationOrThrow(name: string): AppInstance {
        const app = this.appManager.get(name);
        if (!app) {
            throw new MicroCoreError(
                ERROR_CODES.APPLICATION_NOT_FOUND,
                `应用 ${name} 未找到`
            );
        }
        return app;
    }
}