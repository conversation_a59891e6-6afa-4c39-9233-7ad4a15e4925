/**
 * @fileoverview 路由监听器 - 负责监听路由变化并触发应用切换
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

import { EventBus } from '../../../communication/event-bus';
import type { MicroCoreOptions } from '../../../types';
import { createLogger } from '../../../utils';

/**
 * 路由监听器
 * 负责监听浏览器路由变化，触发微前端应用的加载和卸载
 */
export class RouteListener {
    private logger = createLogger('[RouteListener]');
    private isListening = false;
    private originalPushState: typeof history.pushState;
    private originalReplaceState: typeof history.replaceState;

    constructor(
        private config: MicroCoreOptions,
        private eventBus: EventBus
    ) {
        this.originalPushState = history.pushState;
        this.originalReplaceState = history.replaceState;
        this.logger.debug('路由监听器初始化完成');
    }

    /**
     * 开始监听路由变化
     */
    start(): void {
        if (this.isListening) {
            this.logger.warn('路由监听器已启动，忽略重复启动请求');
            return;
        }

        this.logger.debug('启动路由监听');

        // 监听 popstate 事件（浏览器前进后退）
        window.addEventListener('popstate', this.handlePopState);

        // 劫持 pushState 和 replaceState
        this.hijackHistoryMethods();

        // 触发初始路由检查
        this.handleRouteChange();

        this.isListening = true;
        this.logger.info('路由监听器启动完成');
    }

    /**
     * 停止监听路由变化
     */
    stop(): void {
        if (!this.isListening) {
            this.logger.warn('路由监听器未启动，忽略停止请求');
            return;
        }

        this.logger.debug('停止路由监听');

        // 移除事件监听
        window.removeEventListener('popstate', this.handlePopState);

        // 恢复原始的 history 方法
        this.restoreHistoryMethods();

        this.isListening = false;
        this.logger.info('路由监听器停止完成');
    }

    /**
     * 处理 popstate 事件
     */
    private handlePopState = (event: PopStateEvent): void => {
        this.logger.debug('检测到 popstate 事件', event.state);
        this.handleRouteChange();
    };

    /**
     * 劫持 history 方法
     */
    private hijackHistoryMethods(): void {
        const self = this;

        history.pushState = function (state: any, title: string, url?: string | URL | null) {
            const result = self.originalPushState.call(this, state, title, url);
            self.handleRouteChange();
            return result;
        };

        history.replaceState = function (state: any, title: string, url?: string | URL | null) {
            const result = self.originalReplaceState.call(this, state, title, url);
            self.handleRouteChange();
            return result;
        };
    }

    /**
     * 恢复原始的 history 方法
     */
    private restoreHistoryMethods(): void {
        history.pushState = this.originalPushState;
        history.replaceState = this.originalReplaceState;
    }

    /**
     * 处理路由变化
     */
    private handleRouteChange(): void {
        const currentLocation = window.location;
        this.logger.debug('路由变化:', currentLocation.pathname + currentLocation.search + currentLocation.hash);

        // 发送路由变化事件
        this.eventBus.emit('route:changed', {
            pathname: currentLocation.pathname,
            search: currentLocation.search,
            hash: currentLocation.hash,
            href: currentLocation.href
        });
    }

    /**
     * 检查监听状态
     */
    isRouteListening(): boolean {
        return this.isListening;
    }
}