/**
 * @fileoverview 微前端内核主类 - 协调各个子系统的核心调度器
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 * @performance 优化内存使用和启动性能，支持延迟加载
 */

import { EventBus } from '../../communication/event-bus';
import { ERROR_CODES, MicroCoreError } from '../../errors';
import type { AppConfig, AppInstance, MicroCoreOptions, Plugin } from '../../types';
import { createLogger } from '../../utils';
import { LifecycleManager } from '../lifecycle-manager';
import { AppManager } from './managers/app-manager';
import { PluginManager } from './managers/plugin-manager';
import { ResourceManager } from './managers/resource-manager';
import { RouteListener } from './routing/route-listener';
import { MemoryMonitor } from './utils/memory-monitor';

/**
 * 微前端内核
 * 整个微前端系统的核心，负责协调各个管理器和插件的工作
 * @performance 优化内存使用和启动性能
 */
export class MicroCoreKernel {
    private config: MicroCoreOptions;
    private eventBus: EventBus;
    private appManager: AppManager;
    private pluginManager: PluginManager;
    private lifecycleManager: LifecycleManager;
    private routeListener: RouteListener;
    private memoryMonitor: MemoryMonitor;
    private logger = createLogger('[MicroCoreKernel]');
    private isStarted = false;
    private isDestroyed = false;

    // 性能优化：延迟初始化重型组件
    private _resourceManager?: ResourceManager;
    private _pluginOperations?: any;

    constructor(config: MicroCoreOptions = {}) {
        this.config = this.initializeConfig(config);
        this.initializeComponents();
        this.logger.info('微前端内核初始化完成');
    }

    /**
     * 初始化配置
     */
    private initializeConfig(config: MicroCoreOptions): MicroCoreOptions {
        return {
            development: false,
            logLevel: 'INFO',
            defaultSandbox: 'proxy',
            ...config
        };
    }

    /**
     * 初始化核心组件
     */
    private initializeComponents(): void {
        this.eventBus = new EventBus();
        this.appManager = new AppManager(this.config, this.eventBus);
        this.pluginManager = new PluginManager(this.config, this.eventBus);
        this.lifecycleManager = new LifecycleManager(this.eventBus);
        this.routeListener = new RouteListener(this.config, this.eventBus);
        this.memoryMonitor = new MemoryMonitor({
            enableMonitoring: this.config.development,
            monitorInterval: 30000
        });
    }

    registerApplication(config: AppConfig): void {
        this.checkDestroyed();
        this.appManager.register(config);
    }

    unregisterApplication(name: string): void {
        this.checkDestroyed();
        this.appManager.unregister(name);
    }

    getApplication(name: string): AppInstance | null {
        this.checkDestroyed();
        return this.appManager.get(name);
    }

    getApplications(): AppInstance[] {
        this.checkDestroyed();
        return this.appManager.getAll();
    }

    async start(): Promise<void> {
        this.checkDestroyed();
        if (this.isStarted) {
            this.logger.warn('内核已启动，忽略重复启动请求');
            return;
        }
        this.logger.info('启动微前端内核...');
        try {
            await this.performStartup();
            this.logger.info('微前端内核启动完成');
        } catch (error) {
            this.logger.error('内核启动失败:', error);
            throw this.createOperationError('内核启动失败', error);
        }
    }

    private async performStartup(): Promise<void> {
        this.eventBus.emit('kernel:starting');
        this.routeListener.start();
        this.memoryMonitor.start();
        this.isStarted = true;
        this.eventBus.emit('kernel:started');
    }

    async stop(): Promise<void> {
        this.checkDestroyed();
        if (!this.isStarted) {
            this.logger.warn('内核未启动，忽略停止请求');
            return;
        }
        this.logger.info('停止微前端内核...');
        try {
            await this.performShutdown();
            this.logger.info('微前端内核停止完成');
        } catch (error) {
            this.logger.error('内核停止失败:', error);
            throw this.createOperationError('内核停止失败', error);
        }
    }

    private async performShutdown(): Promise<void> {
        this.eventBus.emit('kernel:stopping');
        await this.unmountAllApps();
        this.routeListener.stop();
        this.memoryMonitor.stop();
        this.isStarted = false;
        this.eventBus.emit('kernel:stopped');
    }

    private async unmountAllApps(): Promise<void> {
        const mountedApps = this.appManager.getByStatus('MOUNTED');
        for (const app of mountedApps) {
            try {
                await this.unmountApplication(app.name);
            } catch (error) {
                this.logger.error(`卸载应用 ${app.name} 失败:`, error);
            }
        }
    }

    async destroy(): Promise<void> {
        if (this.isDestroyed) {
            this.logger.warn('内核已销毁，忽略重复销毁请求');
            return;
        }
        this.logger.info('销毁微前端内核...');
        try {
            await this.performDestroy();
            this.logger.info('微前端内核销毁完成');
        } catch (error) {
            this.logger.error('内核销毁失败:', error);
        } finally {
            this.isDestroyed = true;
        }
    }

    private async performDestroy(): Promise<void> {
        if (this.isStarted) {
            await this.stop();
        }
        this.eventBus.emit('kernel:destroying');
        this.cleanupManagers();
        this.eventBus.removeAllListeners();
        this.eventBus.emit('kernel:destroyed');
    }

    private cleanupManagers(): void {
        this.appManager.clear();
        this.pluginManager.clear();
        this.lifecycleManager.clearHooks();
        this.memoryMonitor.cleanup();
    }

    async loadApplication(name: string): Promise<void> {
        this.checkDestroyed();
        const app = this.getApplicationOrThrow(name);
        await this.getResourceManager().loadApplication(app);
    }

    async mountApplication(name: string): Promise<void> {
        this.checkDestroyed();
        const app = this.getApplicationOrThrow(name);
        try {
            await this.performAppMount(app, name);
            this.logger.info(`应用 ${name} 挂载完成`);
        } catch (error) {
            this.appManager.setError(name, error instanceof Error ? error : new Error(String(error)));
            throw error;
        }
    }

    async unmountApplication(name: string): Promise<void> {
        this.checkDestroyed();
        const app = this.getApplicationOrThrow(name);
        try {
            if (app.status === 'MOUNTED') {
                await this.lifecycleManager.unmount(app);
            }
            this.logger.info(`应用 ${name} 卸载完成`);
        } catch (error) {
            this.appManager.setError(name, error instanceof Error ? error : new Error(String(error)));
            throw error;
        }
    }

    private async performAppMount(app: AppInstance, name: string): Promise<void> {
        if (app.status === 'NOT_LOADED') {
            await this.loadApplication(name);
        }
        if (app.status === 'NOT_BOOTSTRAPPED') {
            await this.lifecycleManager.bootstrap(app);
        }
        if (app.status === 'NOT_MOUNTED') {
            await this.lifecycleManager.mount(app);
        }
    }

    use(plugin: Plugin, options?: any): void {
        this.getPluginOperations().use(plugin, options);
    }

    getPlugin(name: string): Plugin | null {
        return this.getPluginOperations().getPlugin(name);
    }

    getPlugins(): string[] {
        return this.getPluginOperations().getPlugins();
    }

    hasPlugin(name: string): boolean {
        return this.getPluginOperations().hasPlugin(name);
    }

    getEventBus(): EventBus {
        this.checkDestroyed();
        return this.eventBus;
    }

    getAppRegistry() {
        this.checkDestroyed();
        return this.appManager.getRegistry();
    }

    getLifecycleManager(): LifecycleManager {
        this.checkDestroyed();
        return this.lifecycleManager;
    }

    isKernelStarted(): boolean {
        return this.isStarted;
    }

    isKernelDestroyed(): boolean {
        return this.isDestroyed;
    }

    /**
     * 获取资源管理器（延迟初始化）
     */
    private getResourceManager(): ResourceManager {
        if (!this._resourceManager) {
            this._resourceManager = new ResourceManager(this.config);
        }
        return this._resourceManager;
    }

    /**
     * 获取插件操作管理器（延迟初始化）
     */
    private getPluginOperations(): any {
        if (!this._pluginOperations) {
            // 临时使用直接委托，避免循环导入
            this._pluginOperations = {
                use: (plugin: Plugin, options?: any) => {
                    this.checkDestroyed();
                    this.pluginManager.use(plugin, options);
                },
                getPlugin: (name: string) => {
                    this.checkDestroyed();
                    return this.pluginManager.get(name);
                },
                getPlugins: () => {
                    this.checkDestroyed();
                    return this.pluginManager.getAll();
                },
                hasPlugin: (name: string) => {
                    this.checkDestroyed();
                    return this.pluginManager.has(name);
                }
            };
        }
        return this._pluginOperations;
    }

    /**
     * 获取应用或抛出异常
     */
    private getApplicationOrThrow(name: string): AppInstance {
        const app = this.appManager.get(name);
        if (!app) {
            throw new MicroCoreError(
                ERROR_CODES.APPLICATION_NOT_FOUND,
                `应用 ${name} 未找到`
            );
        }
        return app;
    }

    /**
     * 检查内核是否已销毁
     */
    private checkDestroyed(): void {
        if (this.isDestroyed) {
            throw new MicroCoreError(
                ERROR_CODES.OPERATION_FAILED,
                '内核已销毁，无法执行操作'
            );
        }
    }

    /**
     * 创建操作错误
     */
    private createOperationError(message: string, error: unknown): MicroCoreError {
        return new MicroCoreError(
            ERROR_CODES.OPERATION_FAILED,
            message,
            {},
            error instanceof Error ? error : new Error(String(error))
        );
    }
}