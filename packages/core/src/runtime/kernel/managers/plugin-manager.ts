/**
 * @fileoverview 插件管理器 - 负责插件的安装、管理和生命周期
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

import { EventBus } from '../../../communication/event-bus';
import { ERROR_CODES, MicroCoreError } from '../../../errors';
import type { MicroCoreOptions, Plugin } from '../../../types';
import { createLogger } from '../../../utils';

/**
 * 插件管理器
 * 负责插件的安装、卸载、管理等功能
 */
export class PluginManager {
    private plugins = new Map<string, Plugin>();
    private logger = createLogger('[PluginManager]');

    constructor(
        private config: MicroCoreOptions,
        private eventBus: EventBus
    ) {
        this.logger.debug('插件管理器初始化完成');
    }

    /**
     * 安装插件
     */
    use(plugin: Plugin, options?: any): void {
        this.logger.debug('安装插件:', plugin.name);

        if (this.plugins.has(plugin.name)) {
            throw new MicroCoreError(
                ERROR_CODES.PLUGIN_ALREADY_EXISTS,
                `插件 ${plugin.name} 已存在`
            );
        }

        try {
            if (!plugin || !plugin.name) {
                throw new MicroCoreError(ERROR_CODES.INVALID_ARGUMENT, '插件无效');
            }

            // 安装插件
            if (plugin.install) {
                plugin.install(this);
            }

            this.plugins.set(plugin.name, plugin);

            // 发送插件安装事件
            this.eventBus.emit('plugin:installed', { plugin, options });

            this.logger.info(`插件 ${plugin.name} 安装完成`);
        } catch (error) {
            this.logger.error(`插件 ${plugin.name} 安装失败:`, error);
            throw new MicroCoreError(
                ERROR_CODES.PLUGIN_INSTALL_FAILED,
                `插件 ${plugin.name} 安装失败`,
                { pluginName: plugin.name },
                error instanceof Error ? error : new Error(String(error))
            );
        }
    }

    /**
     * 获取插件
     */
    get(name: string): Plugin | null {
        return this.plugins.get(name) || null;
    }

    /**
     * 获取所有已安装的插件名称列表
     */
    getAll(): string[] {
        return Array.from(this.plugins.keys());
    }

    /**
     * 检查插件是否已安装
     */
    has(name: string): boolean {
        return this.plugins.has(name);
    }

    /**
     * 卸载插件
     */
    uninstall(name: string): void {
        this.logger.debug('卸载插件:', name);

        const plugin = this.plugins.get(name);
        if (!plugin) {
            throw new MicroCoreError(
                ERROR_CODES.PLUGIN_NOT_FOUND,
                `插件 ${name} 未找到`
            );
        }

        try {
            // 卸载插件
            if (plugin.uninstall) {
                plugin.uninstall();
            }

            this.plugins.delete(name);

            // 发送插件卸载事件
            this.eventBus.emit('plugin:uninstalled', { plugin });

            this.logger.info(`插件 ${name} 卸载完成`);
        } catch (error) {
            this.logger.error(`插件 ${name} 卸载失败:`, error);
            throw new MicroCoreError(
                ERROR_CODES.PLUGIN_UNINSTALL_FAILED,
                `插件 ${name} 卸载失败`,
                { pluginName: name },
                error instanceof Error ? error : new Error(String(error))
            );
        }
    }

    /**
     * 清理所有插件
     */
    clear(): void {
        const pluginNames = Array.from(this.plugins.keys());

        for (const name of pluginNames) {
            try {
                this.uninstall(name);
            } catch (error) {
                this.logger.error(`清理插件 ${name} 失败:`, error);
            }
        }

        this.plugins.clear();
        this.logger.info('所有插件已清理');
    }
}