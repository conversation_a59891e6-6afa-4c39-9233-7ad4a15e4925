/**
 * @fileoverview 资源管理器 - 负责应用资源的加载和管理
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

import { APP_STATUS } from '../../../constants';
import type { AppInstance, MicroCoreOptions } from '../../../types';
import { createLogger } from '../../../utils';

/**
 * 资源管理器
 * 负责微前端应用资源的加载、缓存和清理
 */
export class ResourceManager {
    private logger = createLogger('[ResourceManager]');
    private loadedResources = new Map<string, Set<string>>();

    constructor(private config: MicroCoreOptions) {
        this.logger.debug('资源管理器初始化完成');
    }

    /**
     * 加载应用资源
     */
    async loadApplication(app: AppInstance): Promise<void> {
        this.logger.debug('加载应用资源:', app.name);

        if (app.status !== APP_STATUS.NOT_LOADED) {
            this.logger.warn(`应用 ${app.name} 已加载，当前状态: ${app.status}`);
            return;
        }

        try {
            // 更新状态为加载中
            app.status = APP_STATUS.LOADING_SOURCE_CODE;

            // TODO: 实现具体的资源加载逻辑
            // 这里应该加载应用的 JS/CSS 资源
            await this.loadJavaScriptResources(app);
            await this.loadStylesheetResources(app);

            // 记录已加载的资源
            this.recordLoadedResources(app.name, app.config.entry);

            // 更新状态为已加载
            app.status = APP_STATUS.NOT_BOOTSTRAPPED;

            this.logger.info(`应用 ${app.name} 资源加载完成`);
        } catch (error) {
            app.error = error instanceof Error ? error : new Error(String(error));
            app.status = APP_STATUS.LOAD_ERROR;
            throw error;
        }
    }

    /**
     * 加载JavaScript资源
     */
    private async loadJavaScriptResources(app: AppInstance): Promise<void> {
        // TODO: 实现JS资源加载
        this.logger.debug(`加载 ${app.name} 的JavaScript资源`);
    }

    /**
     * 加载样式表资源
     */
    private async loadStylesheetResources(app: AppInstance): Promise<void> {
        // TODO: 实现CSS资源加载
        this.logger.debug(`加载 ${app.name} 的样式表资源`);
    }

    /**
     * 记录已加载的资源
     */
    private recordLoadedResources(appName: string, entry: string): void {
        if (!this.loadedResources.has(appName)) {
            this.loadedResources.set(appName, new Set());
        }
        this.loadedResources.get(appName)!.add(entry);
    }

    /**
     * 清理应用资源
     */
    async unloadApplication(appName: string): Promise<void> {
        this.logger.debug('清理应用资源:', appName);

        const resources = this.loadedResources.get(appName);
        if (resources) {
            // TODO: 实现资源清理逻辑
            resources.clear();
            this.loadedResources.delete(appName);
        }

        this.logger.info(`应用 ${appName} 资源清理完成`);
    }

    /**
     * 获取已加载的资源列表
     */
    getLoadedResources(appName: string): string[] {
        const resources = this.loadedResources.get(appName);
        return resources ? Array.from(resources) : [];
    }

    /**
     * 清理所有资源
     */
    clear(): void {
        this.loadedResources.clear();
        this.logger.info('所有资源已清理');
    }
}