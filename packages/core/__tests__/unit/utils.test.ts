import { describe, expect, it } from 'vitest';
import {
    generateId,
    isFunction,
    isObject,
    isString
} from '../src/utils';

describe('Utils 工具函数测试', () => {
    describe('generateId', () => {
        it('应该生成唯一ID', () => {
            const id1 = generateId();
            const id2 = generateId();
            expect(id1).not.toBe(id2);
            expect(id1).toMatch(/^micro-app_\d+_[a-z0-9]+$/);
        });

        it('应该支持自定义前缀', () => {
            const id = generateId('test');
            expect(id).toMatch(/^test_\d+_[a-z0-9]+$/);
        });
    });

    describe('类型检查函数', () => {
        it('isFunction 应该正确识别函数', () => {
            expect(isFunction(() => { })).toBe(true);
            expect(isFunction(function () { })).toBe(true);
            expect(isFunction('string')).toBe(false);
            expect(isFunction({})).toBe(false);
            expect(isFunction(null)).toBe(false);
        });

        it('isString 应该正确识别字符串', () => {
            expect(isString('hello')).toBe(true);
            expect(isString('')).toBe(true);
            expect(isString(123)).toBe(false);
            expect(isString({})).toBe(false);
            expect(isString(null)).toBe(false);
        });

        it('isObject 应该正确识别对象', () => {
            expect(isObject({})).toBe(true);
            expect(isObject({ a: 1 })).toBe(true);
            expect(isObject([])).toBe(false);
            expect(isObject(null)).toBe(false);
            expect(isObject('string')).toBe(false);
            expect(isObject(123)).toBe(false);
        });
    });




});