/**
 * @fileoverview 性能基准测试
 * <AUTHOR> <<EMAIL>>
 * @version 2.0.0
 */

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { EventBus } from '../src/communication/event-bus';
import { AppRegistry } from '../src/runtime/app-registry';
import { MicroCoreKernel } from '../src/runtime/kernel';
import { LifecycleManager } from '../src/runtime/lifecycle-manager';
import { PluginSystem } from '../src/runtime/plugin-system';
import type { AppConfig } from '../src/types';

describe('性能基准测试', () => {
    let kernel: MicroCoreKernel;
    let eventBus: EventBus;
    let appRegistry: AppRegistry;
    let lifecycleManager: LifecycleManager;
    let pluginSystem: PluginSystem;

    const mockConfig = {
        development: false, // 生产模式测试
        logLevel: 'ERROR' as const // 减少日志输出
    };

    beforeEach(() => {
        eventBus = new EventBus();
        appRegistry = new AppRegistry(eventBus);
        lifecycleManager = new LifecycleManager(eventBus);
        pluginSystem = new PluginSystem();
        kernel = new MicroCoreKernel(mockConfig);
    });

    afterEach(() => {
        try {
            if (eventBus) {
                eventBus.destroy();
            }
        } catch (error) {
            // 忽略销毁错误
        }
    });

    /**
     * 性能测试辅助函数
     */
    function measurePerformance<T>(fn: () => T, name: string): { result: T; duration: number } {
        const start = performance.now();
        const result = fn();
        const end = performance.now();
        const duration = end - start;
        
        console.log(`${name}: ${duration.toFixed(2)}ms`);
        return { result, duration };
    }

    async function measureAsyncPerformance<T>(
        fn: () => Promise<T>, 
        name: string
    ): Promise<{ result: T; duration: number }> {
        const start = performance.now();
        const result = await fn();
        const end = performance.now();
        const duration = end - start;
        
        console.log(`${name}: ${duration.toFixed(2)}ms`);
        return { result, duration };
    }

    describe('EventBus 性能测试', () => {
        it('应该能够快速处理大量事件发布', () => {
            const eventCount = 10000;
            const listeners: any[] = [];
            
            // 添加多个监听器
            for (let i = 0; i < 100; i++) {
                listeners.push(vi.fn());
                eventBus.on('performance-test', listeners[i]);
            }
            
            const { duration } = measurePerformance(() => {
                for (let i = 0; i < eventCount; i++) {
                    eventBus.emit('performance-test', { data: i });
                }
            }, `发布 ${eventCount} 个事件`);
            
            // 性能基准：10000个事件应该在100ms内完成
            expect(duration).toBeLessThan(100);
            
            // 验证所有监听器都被调用
            listeners.forEach(listener => {
                expect(listener).toHaveBeenCalledTimes(eventCount);
            });
        });

        it('应该能够快速添加和移除大量监听器', () => {
            const listenerCount = 1000;
            const listeners: any[] = [];
            
            // 测试添加监听器的性能
            const { duration: addDuration } = measurePerformance(() => {
                for (let i = 0; i < listenerCount; i++) {
                    const listener = vi.fn();
                    listeners.push(listener);
                    eventBus.on(`test-event-${i}`, listener);
                }
            }, `添加 ${listenerCount} 个监听器`);
            
            // 测试移除监听器的性能
            const { duration: removeDuration } = measurePerformance(() => {
                for (let i = 0; i < listenerCount; i++) {
                    eventBus.off(`test-event-${i}`, listeners[i]);
                }
            }, `移除 ${listenerCount} 个监听器`);
            
            // 性能基准：1000个监听器的添加和移除都应该在50ms内完成
            expect(addDuration).toBeLessThan(50);
            expect(removeDuration).toBeLessThan(50);
        });
    });

    describe('AppRegistry 性能测试', () => {
        it('应该能够快速注册大量应用', () => {
            const appCount = 1000;
            const apps: AppConfig[] = [];
            
            // 准备应用配置
            for (let i = 0; i < appCount; i++) {
                apps.push({
                    name: `perf-app-${i}`,
                    entry: `http://localhost:3000/app-${i}.js`,
                    activeWhen: `/perf-app-${i}`,
                    container: `#perf-container-${i}`
                });
            }
            
            const { duration } = measurePerformance(() => {
                apps.forEach(app => {
                    appRegistry.register(app);
                });
            }, `注册 ${appCount} 个应用`);
            
            // 性能基准：1000个应用注册应该在100ms内完成
            expect(duration).toBeLessThan(100);
            
            // 验证所有应用都已注册
            expect(appRegistry.getAll()).toHaveLength(appCount);
        });

        it('应该能够快速查询大量应用', () => {
            const appCount = 1000;
            
            // 先注册大量应用
            for (let i = 0; i < appCount; i++) {
                appRegistry.register({
                    name: `query-app-${i}`,
                    entry: `http://localhost:3000/app-${i}.js`,
                    activeWhen: `/query-app-${i}`,
                    container: `#query-container-${i}`
                });
            }
            
            const { duration } = measurePerformance(() => {
                for (let i = 0; i < appCount; i++) {
                    const app = appRegistry.get(`query-app-${i}`);
                    expect(app).toBeDefined();
                }
            }, `查询 ${appCount} 个应用`);
            
            // 性能基准：1000次查询应该在50ms内完成
            expect(duration).toBeLessThan(50);
        });
    });

    describe('LifecycleManager 性能测试', () => {
        it('应该能够快速执行大量生命周期操作', async () => {
            const appCount = 100;
            const apps: any[] = [];
            
            // 准备应用实例
            for (let i = 0; i < appCount; i++) {
                const appConfig: AppConfig = {
                    name: `lifecycle-app-${i}`,
                    entry: `http://localhost:3000/app-${i}.js`,
                    activeWhen: `/lifecycle-app-${i}`,
                    container: `#lifecycle-container-${i}`
                };
                
                appRegistry.register(appConfig);
                const app = appRegistry.get(appConfig.name);
                if (app) {
                    apps.push(app);
                }
            }
            
            // 测试并发生命周期操作性能
            const { duration } = await measureAsyncPerformance(async () => {
                const promises = apps.map(app => lifecycleManager.bootstrap(app));
                const results = await Promise.all(promises);
                
                // 验证所有操作都成功
                results.forEach(result => {
                    expect(result.success).toBe(true);
                });
                
                return results;
            }, `并发执行 ${appCount} 个 bootstrap 操作`);
            
            // 性能基准：100个并发操作应该在1000ms内完成
            expect(duration).toBeLessThan(1000);
        });
    });

    describe('PluginSystem 性能测试', () => {
        it('应该能够快速安装和卸载大量插件', () => {
            const pluginCount = 500;
            const plugins: any[] = [];
            
            // 准备插件
            for (let i = 0; i < pluginCount; i++) {
                plugins.push({
                    name: `perf-plugin-${i}`,
                    version: '1.0.0',
                    install: vi.fn(),
                    uninstall: vi.fn()
                });
            }
            
            // 测试安装性能
            const { duration: installDuration } = measurePerformance(() => {
                plugins.forEach(plugin => {
                    pluginSystem.install(plugin);
                });
            }, `安装 ${pluginCount} 个插件`);
            
            // 测试卸载性能
            const { duration: uninstallDuration } = measurePerformance(() => {
                plugins.forEach(plugin => {
                    pluginSystem.uninstall(plugin.name);
                });
            }, `卸载 ${pluginCount} 个插件`);
            
            // 性能基准：500个插件的安装和卸载都应该在100ms内完成
            expect(installDuration).toBeLessThan(100);
            expect(uninstallDuration).toBeLessThan(100);
        });
    });

    describe('内存使用测试', () => {
        it('应该能够有效管理内存，避免内存泄漏', () => {
            const initialMemory = process.memoryUsage().heapUsed;
            
            // 执行大量操作
            for (let cycle = 0; cycle < 10; cycle++) {
                // 创建和销毁大量对象
                const tempEventBus = new EventBus();
                const tempAppRegistry = new AppRegistry(tempEventBus);
                
                // 注册大量应用
                for (let i = 0; i < 100; i++) {
                    tempAppRegistry.register({
                        name: `temp-app-${cycle}-${i}`,
                        entry: `http://localhost:3000/temp-${i}.js`,
                        activeWhen: `/temp-${i}`,
                        container: `#temp-${i}`
                    });
                }
                
                // 清理
                tempEventBus.destroy();
            }
            
            // 强制垃圾回收（如果可用）
            if (global.gc) {
                global.gc();
            }
            
            const finalMemory = process.memoryUsage().heapUsed;
            const memoryIncrease = finalMemory - initialMemory;
            
            console.log(`内存增长: ${(memoryIncrease / 1024 / 1024).toFixed(2)}MB`);
            
            // 内存增长应该控制在合理范围内（小于10MB）
            expect(memoryIncrease).toBeLessThan(10 * 1024 * 1024);
        });
    });

    describe('整体系统性能测试', () => {
        it('应该能够快速完成完整的微前端应用管理流程', async () => {
            const appCount = 50;
            
            const { duration } = await measureAsyncPerformance(async () => {
                const results = [];
                
                for (let i = 0; i < appCount; i++) {
                    const appConfig: AppConfig = {
                        name: `system-app-${i}`,
                        entry: `http://localhost:3000/system-${i}.js`,
                        activeWhen: `/system-${i}`,
                        container: `#system-${i}`
                    };
                    
                    // 完整流程：注册 -> 生命周期 -> 卸载
                    appRegistry.register(appConfig);
                    const app = appRegistry.get(appConfig.name);
                    
                    if (app) {
                        const bootstrapResult = await lifecycleManager.bootstrap(app);
                        const mountResult = await lifecycleManager.mount(app);
                        await lifecycleManager.unmount(app);
                        appRegistry.unregister(appConfig.name);
                        
                        results.push({ bootstrap: bootstrapResult, mount: mountResult });
                    }
                }
                
                return results;
            }, `完整处理 ${appCount} 个应用的生命周期`);
            
            // 性能基准：50个应用的完整流程应该在2000ms内完成
            expect(duration).toBeLessThan(2000);
        });
    });
});
