/**
 * @fileoverview 集成测试 - 测试组件间的协作和数据流
 * <AUTHOR> <<EMAIL>>
 * @version 2.0.0
 */

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { EventBus } from '../src/communication/event-bus';
import { AppRegistry } from '../src/runtime/app-registry';
import { MicroCoreKernel } from '../src/runtime/kernel';
import { LifecycleManager } from '../src/runtime/lifecycle-manager';
import { PluginSystem } from '../src/runtime/plugin-system';
import type { AppConfig } from '../src/types';

describe('集成测试 - 组件协作', () => {
    let kernel: MicroCoreKernel;
    let eventBus: EventBus;
    let appRegistry: AppRegistry;
    let lifecycleManager: LifecycleManager;
    let pluginSystem: PluginSystem;

    const mockConfig = {
        development: true,
        logLevel: 'DEBUG' as const
    };

    const mockAppConfig: AppConfig = {
        name: 'integration-test-app',
        entry: 'http://localhost:3000/app.js',
        activeWhen: '/integration-test',
        container: '#integration-test-container'
    };

    beforeEach(() => {
        eventBus = new EventBus();
        appRegistry = new AppRegistry(eventBus);
        lifecycleManager = new LifecycleManager(eventBus);
        pluginSystem = new PluginSystem();
        kernel = new MicroCoreKernel(mockConfig);
    });

    afterEach(() => {
        try {
            if (eventBus) {
                eventBus.destroy();
            }
        } catch (error) {
            // 忽略销毁错误
        }
    });

    describe('事件驱动的组件通信', () => {
        it('应该能够通过事件总线进行组件间通信', async () => {
            const mockListener = vi.fn();
            
            // 监听应用注册事件
            eventBus.on('app:registered', mockListener);
            
            // 注册应用
            appRegistry.register(mockAppConfig);
            
            // 验证事件被触发
            expect(mockListener).toHaveBeenCalledWith(
                expect.objectContaining({
                    name: mockAppConfig.name
                })
            );
        });

        it('应该能够处理应用生命周期事件流', async () => {
            const events: string[] = [];
            
            // 监听所有生命周期事件
            eventBus.on('app:registered', () => events.push('registered'));
            eventBus.on('app:bootstrapped', () => events.push('bootstrapped'));
            eventBus.on('app:mounted', () => events.push('mounted'));
            eventBus.on('app:unmounted', () => events.push('unmounted'));
            
            // 注册应用
            appRegistry.register(mockAppConfig);
            const app = appRegistry.get(mockAppConfig.name);
            
            if (app) {
                // 执行生命周期操作
                await lifecycleManager.bootstrap(app);
                await lifecycleManager.mount(app);
                await lifecycleManager.unmount(app);
            }
            
            // 验证事件顺序
            expect(events).toEqual(['registered', 'bootstrapped', 'mounted', 'unmounted']);
        });
    });

    describe('插件系统集成', () => {
        it('应该能够通过插件扩展核心功能', () => {
            const mockPlugin = {
                name: 'integration-test-plugin',
                version: '1.0.0',
                install: vi.fn(),
                uninstall: vi.fn()
            };

            // 安装插件
            kernel.use(mockPlugin);
            
            // 验证插件被安装
            expect(kernel.hasPlugin(mockPlugin.name)).toBe(true);
            expect(mockPlugin.install).toHaveBeenCalled();
            
            // 卸载插件
            kernel.uninstall(mockPlugin.name);
            
            // 验证插件被卸载
            expect(kernel.hasPlugin(mockPlugin.name)).toBe(false);
            expect(mockPlugin.uninstall).toHaveBeenCalled();
        });

        it('应该能够通过插件监听和处理应用事件', () => {
            const eventHandler = vi.fn();
            
            const mockPlugin = {
                name: 'event-handler-plugin',
                version: '1.0.0',
                install: (pluginSystem: any) => {
                    // 插件监听应用注册事件
                    eventBus.on('app:registered', eventHandler);
                }
            };

            // 安装插件
            kernel.use(mockPlugin);
            
            // 注册应用
            appRegistry.register(mockAppConfig);
            
            // 验证插件处理了事件
            expect(eventHandler).toHaveBeenCalled();
        });
    });

    describe('完整的应用管理流程', () => {
        it('应该能够完成完整的应用注册、生命周期管理和卸载流程', async () => {
            const flowEvents: string[] = [];
            
            // 监听所有相关事件
            eventBus.on('app:registered', () => flowEvents.push('registered'));
            eventBus.on('app:bootstrapped', () => flowEvents.push('bootstrapped'));
            eventBus.on('app:mounted', () => flowEvents.push('mounted'));
            eventBus.on('app:unmounted', () => flowEvents.push('unmounted'));
            eventBus.on('app:unregistered', () => flowEvents.push('unregistered'));
            
            // 1. 注册应用
            appRegistry.register(mockAppConfig);
            expect(appRegistry.has(mockAppConfig.name)).toBe(true);
            
            // 2. 获取应用实例
            const app = appRegistry.get(mockAppConfig.name);
            expect(app).toBeDefined();
            
            if (app) {
                // 3. 执行生命周期
                const bootstrapResult = await lifecycleManager.bootstrap(app);
                expect(bootstrapResult.success).toBe(true);
                
                const mountResult = await lifecycleManager.mount(app);
                expect(mountResult.success).toBe(true);
                
                // 4. 卸载应用
                await lifecycleManager.unmount(app);
                
                // 5. 注销应用
                appRegistry.unregister(mockAppConfig.name);
                expect(appRegistry.has(mockAppConfig.name)).toBe(false);
            }
            
            // 验证完整流程的事件顺序
            expect(flowEvents).toEqual([
                'registered',
                'bootstrapped', 
                'mounted',
                'unmounted',
                'unregistered'
            ]);
        });
    });

    describe('错误处理和恢复', () => {
        it('应该能够处理应用注册冲突', () => {
            // 注册应用
            appRegistry.register(mockAppConfig);
            
            // 尝试重复注册
            expect(() => {
                appRegistry.register(mockAppConfig);
            }).toThrow(`应用 ${mockAppConfig.name} 已存在`);
        });

        it('应该能够处理生命周期操作失败', async () => {
            // 创建一个会失败的应用配置
            const failingApp: AppConfig = {
                name: 'failing-app',
                entry: 'http://localhost:3000/failing-app.js',
                activeWhen: '/failing',
                container: '#failing-container'
            };
            
            appRegistry.register(failingApp);
            const app = appRegistry.get(failingApp.name);
            
            if (app) {
                // 模拟生命周期失败
                app.lifecycle = {
                    bootstrap: vi.fn().mockRejectedValue(new Error('Bootstrap failed'))
                };
                
                const result = await lifecycleManager.bootstrap(app);
                expect(result.success).toBe(false);
                expect(result.error).toBeDefined();
                expect(result.error?.message).toContain('Bootstrap failed');
            }
        });
    });

    describe('性能和资源管理', () => {
        it('应该能够管理多个应用的并发操作', async () => {
            const apps: AppConfig[] = [];
            const results: any[] = [];
            
            // 创建多个应用配置
            for (let i = 0; i < 5; i++) {
                apps.push({
                    name: `concurrent-app-${i}`,
                    entry: `http://localhost:300${i}/app.js`,
                    activeWhen: `/concurrent-${i}`,
                    container: `#concurrent-container-${i}`
                });
            }
            
            // 并发注册所有应用
            apps.forEach(appConfig => {
                appRegistry.register(appConfig);
            });
            
            // 并发执行生命周期操作
            const promises = apps.map(async (appConfig) => {
                const app = appRegistry.get(appConfig.name);
                if (app) {
                    const bootstrapResult = await lifecycleManager.bootstrap(app);
                    const mountResult = await lifecycleManager.mount(app);
                    return { bootstrap: bootstrapResult, mount: mountResult };
                }
                return null;
            });
            
            const concurrentResults = await Promise.all(promises);
            
            // 验证所有操作都成功
            concurrentResults.forEach(result => {
                if (result) {
                    expect(result.bootstrap.success).toBe(true);
                    expect(result.mount.success).toBe(true);
                }
            });
            
            // 验证所有应用都已注册
            expect(appRegistry.getAll()).toHaveLength(5);
        });
    });
});
