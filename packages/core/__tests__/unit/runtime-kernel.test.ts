/**
 * @fileoverview MicroCoreKernel 运行时内核测试套件
 * @description 重新导出模块化的测试文件
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

// 重新导出所有模块化的测试
export * from './runtime-kernel/app-lifecycle.test';
export * from './runtime-kernel/app-management.test';
export * from './runtime-kernel/getters-and-edge-cases.test';
export * from './runtime-kernel/kernel-lifecycle.test';
export * from './runtime-kernel/plugin-system.test';

/**
 * @deprecated 原始的大型测试文件已被拆分为多个模块化测试文件
 * 请使用以下文件进行测试：
 * - kernel-lifecycle.test.ts - 内核生命周期测试
 * - app-management.test.ts - 应用管理测试
 * - app-lifecycle.test.ts - 应用生命周期测试
 * - plugin-system.test.ts - 插件系统测试
 * - getters-and-edge-cases.test.ts - 获取器方法和边界情况测试
 */