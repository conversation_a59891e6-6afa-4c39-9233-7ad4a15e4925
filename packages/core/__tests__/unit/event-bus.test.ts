/**
 * 事件总线测试用例
 */

import { beforeEach, describe, expect, it, vi } from 'vitest';
import { EventBus } from '../src/communication/event-bus';

describe('EventBus', () => {
    let eventBus: EventBus;

    beforeEach(() => {
        eventBus = new EventBus();
    });

    describe('基本事件订阅和发布', () => {
        it('应该能够订阅和触发事件', () => {
            const handler = vi.fn();
            eventBus.on('test-event', handler);

            eventBus.emit('test-event', { data: 'test' });

            expect(handler).toHaveBeenCalledTimes(1);
            expect(handler).toHaveBeenCalledWith({ data: 'test' });
        });

        it('应该能够订阅多个事件', () => {
            const handler1 = vi.fn();
            const handler2 = vi.fn();

            eventBus.on('test-event', handler1);
            eventBus.on('test-event', handler2);

            eventBus.emit('test-event', { data: 'test' });

            expect(handler1).toHaveBeenCalledTimes(1);
            expect(handler2).toHaveBeenCalledTimes(1);
        });

        it('应该能够取消订阅事件', () => {
            const handler = vi.fn();

            eventBus.on('test-event', handler);
            eventBus.off('test-event', handler);

            eventBus.emit('test-event', { data: 'test' });

            expect(handler).not.toHaveBeenCalled();
        });

        it('应该能够取消订阅特定事件的所有处理函数', () => {
            const handler1 = vi.fn();
            const handler2 = vi.fn();

            eventBus.on('test-event', handler1);
            eventBus.on('test-event', handler2);

            eventBus.off('test-event');

            eventBus.emit('test-event', { data: 'test' });

            expect(handler1).not.toHaveBeenCalled();
            expect(handler2).not.toHaveBeenCalled();
        });
    });

    describe('一次性事件订阅', () => {
        it('应该能够订阅一次性事件', () => {
            const handler = vi.fn();

            eventBus.once('test-event', handler);

            eventBus.emit('test-event', { data: 'test1' });
            eventBus.emit('test-event', { data: 'test2' });

            expect(handler).toHaveBeenCalledTimes(1);
            expect(handler).toHaveBeenCalledWith({ data: 'test1' });
        });

        it('应该能够取消一次性事件订阅', () => {
            const handler = vi.fn();

            eventBus.once('test-event', handler);
            eventBus.off('test-event', handler);

            eventBus.emit('test-event', { data: 'test' });

            expect(handler).not.toHaveBeenCalled();
        });
    });

    describe('通配符事件订阅', () => {
        it('应该支持通配符事件订阅', () => {
            const handler = vi.fn();

            eventBus.on('namespace:*', handler);

            eventBus.emit('namespace:event1', { data: 'test1' });
            eventBus.emit('namespace:event2', { data: 'test2' });

            expect(handler).toHaveBeenCalledTimes(2);
            expect(handler).toHaveBeenNthCalledWith(1, { data: 'test1' });
            expect(handler).toHaveBeenNthCalledWith(2, { data: 'test2' });
        });

        it('应该能够取消通配符事件订阅', () => {
            const handler = vi.fn();

            eventBus.on('namespace:*', handler);
            eventBus.off('namespace:*', handler);

            eventBus.emit('namespace:event', { data: 'test' });

            expect(handler).not.toHaveBeenCalled();
        });
    });

    describe('事件命名空间', () => {
        it('应该支持事件命名空间', () => {
            const handler1 = vi.fn();
            const handler2 = vi.fn();

            eventBus.on('namespace1:event', handler1);
            eventBus.on('namespace2:event', handler2);

            eventBus.emit('namespace1:event', { data: 'test' });

            expect(handler1).toHaveBeenCalledTimes(1);
            expect(handler2).not.toHaveBeenCalled();
        });
    });

    describe('错误处理', () => {
        it('应该捕获事件处理函数中的错误', () => {
            const errorHandler = vi.fn();
            const handler = vi.fn(() => {
                throw new Error('测试错误');
            });

            eventBus.onError(errorHandler);
            eventBus.on('test-event', handler);

            eventBus.emit('test-event', { data: 'test' });

            expect(errorHandler).toHaveBeenCalledTimes(1);
            expect(errorHandler.mock.calls[0][0]).toBeInstanceOf(Error);
            expect(errorHandler.mock.calls[0][0].message).toBe('测试错误');
        });

        it('应该在没有错误处理函数时不抛出错误', () => {
            const handler = vi.fn(() => {
                throw new Error('测试错误');
            });

            eventBus.on('test-event', handler);

            expect(() => {
                eventBus.emit('test-event', { data: 'test' });
            }).not.toThrow();
        });
    });

    describe('事件优先级', () => {
        it('应该按照优先级顺序执行事件处理函数', () => {
            const result: number[] = [];

            const handler1 = vi.fn(() => result.push(1));
            const handler2 = vi.fn(() => result.push(2));
            const handler3 = vi.fn(() => result.push(3));

            eventBus.on('test-event', handler2, { priority: 2 });
            eventBus.on('test-event', handler3, { priority: 3 });
            eventBus.on('test-event', handler1, { priority: 1 });

            eventBus.emit('test-event');

            expect(result).toEqual([3, 2, 1]);
        });
    });

    describe('异步事件处理', () => {
        it('应该支持异步事件处理函数', async () => {
            const result: string[] = [];

            const asyncHandler = vi.fn(async () => {
                await new Promise(resolve => setTimeout(resolve, 10));
                result.push('async');
            });

            const syncHandler = vi.fn(() => {
                result.push('sync');
            });

            eventBus.on('test-event', asyncHandler);
            eventBus.on('test-event', syncHandler);

            await eventBus.emitAsync('test-event');

            expect(asyncHandler).toHaveBeenCalledTimes(1);
            expect(syncHandler).toHaveBeenCalledTimes(1);
            expect(result).toContain('async');
            expect(result).toContain('sync');
        });
    });

    describe('事件历史记录', () => {
        it('应该能够记录事件历史', () => {
            eventBus.enableHistory();

            eventBus.emit('event1', { data: 'test1' });
            eventBus.emit('event2', { data: 'test2' });

            const history = eventBus.getHistory();

            expect(history).toHaveLength(2);
            expect(history[0].event).toBe('event1');
            expect(history[0].data).toEqual({ data: 'test1' });
            expect(history[1].event).toBe('event2');
            expect(history[1].data).toEqual({ data: 'test2' });
        });

        it('应该能够清除事件历史', () => {
            eventBus.enableHistory();

            eventBus.emit('event1', { data: 'test1' });
            eventBus.clear();

            const history = eventBus.getHistory();

            expect(history).toHaveLength(0);
        });

        it('应该能够禁用事件历史记录', () => {
            eventBus.enableHistory();
            eventBus.emit('event1', { data: 'test1' });

            eventBus.disableHistory();
            eventBus.emit('event2', { data: 'test2' });

            const history = eventBus.getHistory();

            expect(history).toHaveLength(1);
            expect(history[0].event).toBe('event1');
        });
    });
});