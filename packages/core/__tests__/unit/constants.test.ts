/**
 * @fileoverview Core 包 constants.ts 单元测试
 * @description 测试核心常量定义的完整性和正确性
 */

import { describe, expect, it } from 'vitest';
import {
    APP_STATUS,
    DEFAULT_CONFIG,
    EVENT_NAMES,
    LIFECYCLE_HOOKS,
    LOG_LEVEL,
    RESOURCE_STATUS,
    RESOURCE_TYPE,
    SANDBOX_TYPE,
    SANDBOX_TYPES,
    SandboxType,
    type AppStatusType,
    type EventNameType,
    type LifecycleHookType,
    type LogLevelType,
    type ResourceStatusType,
    type ResourceTypeType,
    type SandboxTypeType
} from '../src/constants';

describe('Core Constants', () => {
    describe('APP_STATUS', () => {
        it('应该包含所有必需的应用状态', () => {
            expect(APP_STATUS).toEqual({
                NOT_LOADED: 'NOT_LOADED',
                LOADING_SOURCE_CODE: 'LOADING_SOURCE_CODE',
                NOT_BOOTSTRAPPED: 'NOT_BOOTSTRAPPED',
                BOOTSTRAPPING: 'BOOTSTRAPPING',
                NOT_MOUNTED: 'NOT_MOUNTED',
                MOUNTING: 'MOUNTING',
                MOUNTED: 'MOUNTED',
                UPDATING: 'UPDATING',
                UNMOUNTING: 'UNMOUNTING',
                UNMOUNTED: 'UNMOUNTED',
                LOADED: 'LOADED',
                UNLOADING: 'UNLOADING',
                LOAD_ERROR: 'LOAD_ERROR',
                SKIP_BECAUSE_BROKEN: 'SKIP_BECAUSE_BROKEN'
            });
        });

        it('应该是只读对象', () => {
            expect(() => {
                (APP_STATUS as any).NEW_STATUS = 'NEW_STATUS';
            }).toThrow();
        });

        it('所有状态值应该是字符串类型', () => {
            Object.values(APP_STATUS).forEach(status => {
                expect(typeof status).toBe('string');
            });
        });

        it('状态值应该与键名相同', () => {
            Object.entries(APP_STATUS).forEach(([key, value]) => {
                expect(value).toBe(key);
            });
        });
    });

    describe('SANDBOX_TYPES', () => {
        it('应该包含所有支持的沙箱类型', () => {
            expect(SANDBOX_TYPES).toEqual({
                PROXY: 'proxy',
                DEFINE_PROPERTY: 'define-property',
                WEB_COMPONENT: 'web-component',
                IFRAME: 'iframe',
                NAMESPACE: 'namespace',
                FEDERATION: 'federation'
            });
        });

        it('应该是只读对象', () => {
            expect(() => {
                (SANDBOX_TYPES as any).NEW_TYPE = 'new-type';
            }).toThrow();
        });

        it('所有沙箱类型值应该是字符串类型', () => {
            Object.values(SANDBOX_TYPES).forEach(type => {
                expect(typeof type).toBe('string');
            });
        });

        it('沙箱类型值应该使用 kebab-case 格式', () => {
            Object.values(SANDBOX_TYPES).forEach(type => {
                expect(type).toMatch(/^[a-z]+(-[a-z]+)*$/);
            });
        });
    });

    describe('向后兼容的沙箱类型导出', () => {
        it('SANDBOX_TYPE 应该与 SANDBOX_TYPES 相同', () => {
            expect(SANDBOX_TYPE).toBe(SANDBOX_TYPES);
        });

        it('SandboxType 应该与 SANDBOX_TYPES 相同', () => {
            expect(SandboxType).toBe(SANDBOX_TYPES);
        });
    });

    describe('RESOURCE_TYPE', () => {
        it('应该包含所有支持的资源类型', () => {
            expect(RESOURCE_TYPE).toEqual({
                SCRIPT: 'script',
                STYLE: 'style',
                HTML: 'html',
                IMAGE: 'image',
                FONT: 'font',
                OTHER: 'other'
            });
        });

        it('应该是只读对象', () => {
            expect(() => {
                (RESOURCE_TYPE as any).NEW_TYPE = 'new-type';
            }).toThrow();
        });

        it('所有资源类型值应该是字符串类型', () => {
            Object.values(RESOURCE_TYPE).forEach(type => {
                expect(typeof type).toBe('string');
            });
        });
    });

    describe('RESOURCE_STATUS', () => {
        it('应该包含所有资源状态', () => {
            expect(RESOURCE_STATUS).toEqual({
                NOT_LOADED: 'NOT_LOADED',
                LOADING: 'LOADING',
                LOADED: 'LOADED',
                LOAD_FAILED: 'LOAD_FAILED',
                CACHED: 'CACHED'
            });
        });

        it('应该是只读对象', () => {
            expect(() => {
                (RESOURCE_STATUS as any).NEW_STATUS = 'NEW_STATUS';
            }).toThrow();
        });

        it('所有资源状态值应该是字符串类型', () => {
            Object.values(RESOURCE_STATUS).forEach(status => {
                expect(typeof status).toBe('string');
            });
        });
    });

    describe('LOG_LEVEL', () => {
        it('应该包含所有日志级别', () => {
            expect(LOG_LEVEL).toEqual({
                DEBUG: 'DEBUG',
                INFO: 'INFO',
                WARN: 'WARN',
                ERROR: 'ERROR',
                SILENT: 'SILENT'
            });
        });

        it('应该是只读对象', () => {
            expect(() => {
                (LOG_LEVEL as any).NEW_LEVEL = 'NEW_LEVEL';
            }).toThrow();
        });

        it('日志级别应该按严重程度排序', () => {
            const levels = Object.keys(LOG_LEVEL);
            const expectedOrder = ['DEBUG', 'INFO', 'WARN', 'ERROR', 'SILENT'];
            expect(levels).toEqual(expectedOrder);
        });
    });

    describe('LIFECYCLE_HOOKS', () => {
        it('应该包含所有生命周期钩子', () => {
            expect(LIFECYCLE_HOOKS).toEqual({
                BEFORE_BOOTSTRAP: 'beforeBootstrap',
                AFTER_BOOTSTRAP: 'afterBootstrap',
                BEFORE_MOUNT: 'beforeMount',
                AFTER_MOUNT: 'afterMount',
                BEFORE_UNMOUNT: 'beforeUnmount',
                AFTER_UNMOUNT: 'afterUnmount',
                BEFORE_UPDATE: 'beforeUpdate',
                AFTER_UPDATE: 'afterUpdate'
            });
        });

        it('应该是只读对象', () => {
            expect(() => {
                (LIFECYCLE_HOOKS as any).NEW_HOOK = 'newHook';
            }).toThrow();
        });

        it('钩子名称应该使用 camelCase 格式', () => {
            Object.values(LIFECYCLE_HOOKS).forEach(hook => {
                expect(hook).toMatch(/^[a-z][a-zA-Z]*$/);
            });
        });

        it('应该包含成对的 before/after 钩子', () => {
            const hooks = Object.values(LIFECYCLE_HOOKS);
            const beforeHooks = hooks.filter(hook => hook.startsWith('before'));
            const afterHooks = hooks.filter(hook => hook.startsWith('after'));

            expect(beforeHooks.length).toBe(afterHooks.length);

            beforeHooks.forEach(beforeHook => {
                const afterHook = beforeHook.replace('before', 'after');
                expect(hooks).toContain(afterHook);
            });
        });
    });

    describe('EVENT_NAMES', () => {
        it('应该包含所有核心事件名称', () => {
            expect(EVENT_NAMES).toEqual({
                KERNEL_STARTING: 'kernel:starting',
                KERNEL_STARTED: 'kernel:started',
                KERNEL_STOPPING: 'kernel:stopping',
                KERNEL_STOPPED: 'kernel:stopped',
                KERNEL_DESTROYING: 'kernel:destroying',
                KERNEL_DESTROYED: 'kernel:destroyed',
                APP_REGISTERED: 'app:registered',
                APP_UNREGISTERED: 'app:unregistered',
                APP_LOADED: 'app:loaded',
                APP_MOUNTED: 'app:mounted',
                APP_UNMOUNTED: 'app:unmounted',
                APP_ERROR: 'app:error',
                PLUGIN_INSTALLED: 'plugin:installed',
                PLUGIN_UNINSTALLED: 'plugin:uninstalled',
                ROUTE_CHANGED: 'route:changed',
                STATE_CHANGED: 'state:changed'
            });
        });

        it('应该是只读对象', () => {
            expect(() => {
                (EVENT_NAMES as any).NEW_EVENT = 'new:event';
            }).toThrow();
        });

        it('事件名称应该使用命名空间格式', () => {
            Object.values(EVENT_NAMES).forEach(eventName => {
                expect(eventName).toMatch(/^[a-z]+:[a-z]+$/);
            });
        });

        it('应该按事件类型分组', () => {
            const eventNames = Object.values(EVENT_NAMES);
            const kernelEvents = eventNames.filter(name => name.startsWith('kernel:'));
            const appEvents = eventNames.filter(name => name.startsWith('app:'));
            const pluginEvents = eventNames.filter(name => name.startsWith('plugin:'));

            expect(kernelEvents.length).toBeGreaterThan(0);
            expect(appEvents.length).toBeGreaterThan(0);
            expect(pluginEvents.length).toBeGreaterThan(0);
        });
    });

    describe('DEFAULT_CONFIG', () => {
        it('应该包含所有默认配置', () => {
            expect(DEFAULT_CONFIG).toEqual({
                LOG_LEVEL: LOG_LEVEL.INFO,
                SANDBOX_TYPE: SANDBOX_TYPES.PROXY,
                LIFECYCLE_TIMEOUT: 15000,
                RESOURCE_TIMEOUT: 10000
            });
        });

        it('应该是只读对象', () => {
            expect(() => {
                (DEFAULT_CONFIG as any).NEW_CONFIG = 'new-config';
            }).toThrow();
        });

        it('超时时间应该是合理的数值', () => {
            expect(DEFAULT_CONFIG.LIFECYCLE_TIMEOUT).toBeGreaterThan(0);
            expect(DEFAULT_CONFIG.RESOURCE_TIMEOUT).toBeGreaterThan(0);
            expect(DEFAULT_CONFIG.LIFECYCLE_TIMEOUT).toBeGreaterThan(DEFAULT_CONFIG.RESOURCE_TIMEOUT);
        });

        it('默认日志级别应该是 INFO', () => {
            expect(DEFAULT_CONFIG.LOG_LEVEL).toBe(LOG_LEVEL.INFO);
        });

        it('默认沙箱类型应该是 PROXY', () => {
            expect(DEFAULT_CONFIG.SANDBOX_TYPE).toBe(SANDBOX_TYPES.PROXY);
        });
    });

    describe('类型导出测试', () => {
        it('AppStatusType 应该包含所有应用状态', () => {
            const testStatus: AppStatusType = 'MOUNTED';
            expect(Object.values(APP_STATUS)).toContain(testStatus);
        });

        it('SandboxTypeType 应该包含所有沙箱类型', () => {
            const testType: SandboxTypeType = 'proxy';
            expect(Object.values(SANDBOX_TYPES)).toContain(testType);
        });

        it('ResourceTypeType 应该包含所有资源类型', () => {
            const testType: ResourceTypeType = 'script';
            expect(Object.values(RESOURCE_TYPE)).toContain(testType);
        });

        it('ResourceStatusType 应该包含所有资源状态', () => {
            const testStatus: ResourceStatusType = 'LOADED';
            expect(Object.values(RESOURCE_STATUS)).toContain(testStatus);
        });

        it('LogLevelType 应该包含所有日志级别', () => {
            const testLevel: LogLevelType = 'INFO';
            expect(Object.values(LOG_LEVEL)).toContain(testLevel);
        });

        it('LifecycleHookType 应该包含所有生命周期钩子', () => {
            const testHook: LifecycleHookType = 'beforeMount';
            expect(Object.values(LIFECYCLE_HOOKS)).toContain(testHook);
        });

        it('EventNameType 应该包含所有事件名称', () => {
            const testEvent: EventNameType = 'app:mounted';
            expect(Object.values(EVENT_NAMES)).toContain(testEvent);
        });
    });

    describe('常量完整性检查', () => {
        it('所有导出的常量对象都应该存在', () => {
            expect(APP_STATUS).toBeDefined();
            expect(SANDBOX_TYPES).toBeDefined();
            expect(RESOURCE_TYPE).toBeDefined();
            expect(RESOURCE_STATUS).toBeDefined();
            expect(LOG_LEVEL).toBeDefined();
            expect(LIFECYCLE_HOOKS).toBeDefined();
            expect(EVENT_NAMES).toBeDefined();
            expect(DEFAULT_CONFIG).toBeDefined();
        });

        it('所有常量对象都不应该为空', () => {
            expect(Object.keys(APP_STATUS).length).toBeGreaterThan(0);
            expect(Object.keys(SANDBOX_TYPES).length).toBeGreaterThan(0);
            expect(Object.keys(RESOURCE_TYPE).length).toBeGreaterThan(0);
            expect(Object.keys(RESOURCE_STATUS).length).toBeGreaterThan(0);
            expect(Object.keys(LOG_LEVEL).length).toBeGreaterThan(0);
            expect(Object.keys(LIFECYCLE_HOOKS).length).toBeGreaterThan(0);
            expect(Object.keys(EVENT_NAMES).length).toBeGreaterThan(0);
            expect(Object.keys(DEFAULT_CONFIG).length).toBeGreaterThan(0);
        });

        it('常量值不应该有重复', () => {
            const checkUnique = (obj: Record<string, any>) => {
                const values = Object.values(obj);
                const uniqueValues = [...new Set(values)];
                expect(values.length).toBe(uniqueValues.length);
            };

            checkUnique(APP_STATUS);
            checkUnique(SANDBOX_TYPES);
            checkUnique(RESOURCE_TYPE);
            checkUnique(RESOURCE_STATUS);
            checkUnique(LOG_LEVEL);
            checkUnique(LIFECYCLE_HOOKS);
            checkUnique(EVENT_NAMES);
        });
    });

    describe('边界情况测试', () => {
        it('常量对象应该支持 Object.freeze', () => {
            expect(() => Object.freeze(APP_STATUS)).not.toThrow();
            expect(() => Object.freeze(SANDBOX_TYPES)).not.toThrow();
            expect(() => Object.freeze(RESOURCE_TYPE)).not.toThrow();
        });

        it('常量对象应该支持 JSON 序列化', () => {
            expect(() => JSON.stringify(APP_STATUS)).not.toThrow();
            expect(() => JSON.stringify(SANDBOX_TYPES)).not.toThrow();
            expect(() => JSON.stringify(DEFAULT_CONFIG)).not.toThrow();
        });

        it('常量值应该是原始类型', () => {
            Object.values(APP_STATUS).forEach(value => {
                expect(typeof value).toBe('string');
            });

            Object.values(SANDBOX_TYPES).forEach(value => {
                expect(typeof value).toBe('string');
            });

            expect(typeof DEFAULT_CONFIG.LIFECYCLE_TIMEOUT).toBe('number');
            expect(typeof DEFAULT_CONFIG.RESOURCE_TIMEOUT).toBe('number');
        });
    });
});