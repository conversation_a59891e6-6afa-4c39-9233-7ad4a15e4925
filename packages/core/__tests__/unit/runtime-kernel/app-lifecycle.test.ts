/**
 * @fileoverview MicroCoreKernel 应用生命周期测试
 * @description 测试内核的应用加载、挂载、卸载等生命周期功能
 */

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { APP_STATUS } from '../../../src/constants';
import { MicroCoreError } from '../../../src/errors';
import { MicroCoreKernel } from '../../../src/runtime/kernel';
import type { AppInstance } from '../../../src/types';

// Mock 依赖模块
vi.mock('../../../src/communication/event-bus');
vi.mock('../../../src/runtime/app-registry');
vi.mock('../../../src/runtime/lifecycle-manager');
vi.mock('../../../src/utils');

describe('MicroCoreKernel 应用生命周期管理', () => {
    let kernel: MicroCoreKernel;
    let mockEventBus: any;
    let mockAppRegistry: any;
    let mockLifecycleManager: any;
    let mockLogger: any;

    beforeEach(() => {
        // 创建模拟对象
        mockEventBus = {
            emit: vi.fn(),
            on: vi.fn(),
            off: vi.fn(),
            clear: vi.fn()
        };

        mockAppRegistry = {
            register: vi.fn(),
            unregister: vi.fn(),
            get: vi.fn(),
            getAll: vi.fn(),
            getByStatus: vi.fn(),
            updateStatus: vi.fn(),
            setError: vi.fn(),
            clear: vi.fn()
        };

        mockLifecycleManager = {
            bootstrap: vi.fn(),
            mount: vi.fn(),
            unmount: vi.fn(),
            clearHooks: vi.fn()
        };

        mockLogger = {
            debug: vi.fn(),
            info: vi.fn(),
            warn: vi.fn(),
            error: vi.fn()
        };

        // Mock 构造函数
        const { EventBus } = require('../../../src/communication/event-bus');
        const { AppRegistry } = require('../../../src/runtime/app-registry');
        const { LifecycleManager } = require('../../../src/runtime/lifecycle-manager');
        const { createLogger } = require('../../../src/utils');

        EventBus.mockImplementation(() => mockEventBus);
        AppRegistry.mockImplementation(() => mockAppRegistry);
        LifecycleManager.mockImplementation(() => mockLifecycleManager);
        createLogger.mockReturnValue(mockLogger);

        kernel = new MicroCoreKernel();
    });

    afterEach(() => {
        vi.clearAllMocks();
        vi.resetModules();
    });

    describe('loadApplication', () => {
        it('应该成功加载应用', async () => {
            const mockApp: AppInstance = {
                name: 'test-app',
                status: APP_STATUS.NOT_LOADED,
                config: {
                    name: 'test-app',
                    entry: 'http://localhost:3000',
                    container: '#app',
                    activeWhen: '/test'
                }
            };

            mockAppRegistry.get.mockReturnValue(mockApp);

            await kernel.loadApplication('test-app');

            expect(mockLogger.debug).toHaveBeenCalledWith('加载应用:', 'test-app');
            expect(mockAppRegistry.updateStatus).toHaveBeenCalledWith('test-app', APP_STATUS.LOADING_SOURCE_CODE);
            expect(mockAppRegistry.updateStatus).toHaveBeenCalledWith('test-app', APP_STATUS.NOT_BOOTSTRAPPED);
            expect(mockLogger.info).toHaveBeenCalledWith('应用 test-app 加载完成');
        });

        it('应该在应用不存在时抛出错误', async () => {
            mockAppRegistry.get.mockReturnValue(null);

            await expect(kernel.loadApplication('non-existent-app')).rejects.toThrow(MicroCoreError);
        });

        it('应该忽略已加载的应用', async () => {
            const mockApp: AppInstance = {
                name: 'test-app',
                status: APP_STATUS.NOT_BOOTSTRAPPED,
                config: {
                    name: 'test-app',
                    entry: 'http://localhost:3000',
                    container: '#app',
                    activeWhen: '/test'
                }
            };

            mockAppRegistry.get.mockReturnValue(mockApp);

            await kernel.loadApplication('test-app');

            expect(mockLogger.warn).toHaveBeenCalledWith('应用 test-app 已加载，当前状态: NOT_BOOTSTRAPPED');
        });

        it('应该处理加载失败的情况', async () => {
            const mockApp: AppInstance = {
                name: 'test-app',
                status: APP_STATUS.NOT_LOADED,
                config: {
                    name: 'test-app',
                    entry: 'http://localhost:3000',
                    container: '#app',
                    activeWhen: '/test'
                }
            };

            mockAppRegistry.get.mockReturnValue(mockApp);

            const error = new Error('加载失败');
            mockAppRegistry.updateStatus.mockImplementation(() => {
                throw error;
            });

            await expect(kernel.loadApplication('test-app')).rejects.toThrow(error);
            expect(mockAppRegistry.setError).toHaveBeenCalledWith('test-app', error);
        });

        it('应该在内核销毁后抛出错误', async () => {
            await kernel.destroy();

            await expect(kernel.loadApplication('test-app')).rejects.toThrow(MicroCoreError);
        });

        it('应该处理字符串类型的错误', async () => {
            const mockApp: any = {
                name: 'test-app',
                status: APP_STATUS.NOT_LOADED
            };

            mockAppRegistry.get.mockReturnValue(mockApp);
            mockAppRegistry.updateStatus.mockImplementation(() => {
                throw 'string error';
            });

            await expect(kernel.loadApplication('test-app')).rejects.toThrow();
            expect(mockAppRegistry.setError).toHaveBeenCalledWith('test-app', expect.any(Error));
        });
    });

    describe('mountApplication', () => {
        it('应该成功挂载应用', async () => {
            const mockApp: AppInstance = {
                name: 'test-app',
                status: APP_STATUS.NOT_MOUNTED,
                config: {
                    name: 'test-app',
                    entry: 'http://localhost:3000',
                    container: '#app',
                    activeWhen: '/test'
                }
            };

            mockAppRegistry.get.mockReturnValue(mockApp);

            await kernel.mountApplication('test-app');

            expect(mockLogger.debug).toHaveBeenCalledWith('挂载应用:', 'test-app');
            expect(mockLifecycleManager.mount).toHaveBeenCalledWith(mockApp);
            expect(mockLogger.info).toHaveBeenCalledWith('应用 test-app 挂载完成');
        });

        it('应该在应用不存在时抛出错误', async () => {
            mockAppRegistry.get.mockReturnValue(null);

            await expect(kernel.mountApplication('non-existent-app')).rejects.toThrow(MicroCoreError);
        });

        it('应该先加载未加载的应用', async () => {
            const mockApp: AppInstance = {
                name: 'test-app',
                status: APP_STATUS.NOT_LOADED,
                config: {
                    name: 'test-app',
                    entry: 'http://localhost:3000',
                    container: '#app',
                    activeWhen: '/test'
                }
            };

            mockAppRegistry.get.mockReturnValue(mockApp);

            await kernel.mountApplication('test-app');

            expect(mockAppRegistry.updateStatus).toHaveBeenCalledWith('test-app', APP_STATUS.LOADING_SOURCE_CODE);
        });

        it('应该先引导未引导的应用', async () => {
            const mockApp: AppInstance = {
                name: 'test-app',
                status: APP_STATUS.NOT_BOOTSTRAPPED,
                config: {
                    name: 'test-app',
                    entry: 'http://localhost:3000',
                    container: '#app',
                    activeWhen: '/test'
                }
            };

            mockAppRegistry.get.mockReturnValue(mockApp);

            await kernel.mountApplication('test-app');

            expect(mockLifecycleManager.bootstrap).toHaveBeenCalledWith(mockApp);
        });

        it('应该处理挂载失败的情况', async () => {
            const mockApp: AppInstance = {
                name: 'test-app',
                status: APP_STATUS.NOT_MOUNTED,
                config: {
                    name: 'test-app',
                    entry: 'http://localhost:3000',
                    container: '#app',
                    activeWhen: '/test'
                }
            };

            mockAppRegistry.get.mockReturnValue(mockApp);

            const error = new Error('挂载失败');
            mockLifecycleManager.mount.mockRejectedValue(error);

            await expect(kernel.mountApplication('test-app')).rejects.toThrow(error);
            expect(mockAppRegistry.setError).toHaveBeenCalledWith('test-app', error);
        });

        it('应该在内核销毁后抛出错误', async () => {
            await kernel.destroy();

            await expect(kernel.mountApplication('test-app')).rejects.toThrow(MicroCoreError);
        });

        it('应该处理已挂载的应用', async () => {
            const mockApp: AppInstance = {
                name: 'test-app',
                status: APP_STATUS.MOUNTED,
                config: {
                    name: 'test-app',
                    entry: 'http://localhost:3000',
                    container: '#app',
                    activeWhen: '/test'
                }
            };

            mockAppRegistry.get.mockReturnValue(mockApp);

            await kernel.mountApplication('test-app');

            expect(mockLifecycleManager.mount).not.toHaveBeenCalled();
            expect(mockLogger.info).toHaveBeenCalledWith('应用 test-app 挂载完成');
        });
    });

    describe('unmountApplication', () => {
        it('应该成功卸载应用', async () => {
            const mockApp: AppInstance = {
                name: 'test-app',
                status: APP_STATUS.MOUNTED,
                config: {
                    name: 'test-app',
                    entry: 'http://localhost:3000',
                    container: '#app',
                    activeWhen: '/test'
                }
            };

            mockAppRegistry.get.mockReturnValue(mockApp);

            await kernel.unmountApplication('test-app');

            expect(mockLogger.debug).toHaveBeenCalledWith('卸载应用:', 'test-app');
            expect(mockLifecycleManager.unmount).toHaveBeenCalledWith(mockApp);
            expect(mockLogger.info).toHaveBeenCalledWith('应用 test-app 卸载完成');
        });

        it('应该在应用不存在时抛出错误', async () => {
            mockAppRegistry.get.mockReturnValue(null);

            await expect(kernel.unmountApplication('non-existent-app')).rejects.toThrow(MicroCoreError);
        });

        it('应该忽略未挂载的应用', async () => {
            const mockApp: AppInstance = {
                name: 'test-app',
                status: APP_STATUS.NOT_MOUNTED,
                config: {
                    name: 'test-app',
                    entry: 'http://localhost:3000',
                    container: '#app',
                    activeWhen: '/test'
                }
            };

            mockAppRegistry.get.mockReturnValue(mockApp);

            await kernel.unmountApplication('test-app');

            expect(mockLifecycleManager.unmount).not.toHaveBeenCalled();
            expect(mockLogger.info).toHaveBeenCalledWith('应用 test-app 卸载完成');
        });

        it('应该处理卸载失败的情况', async () => {
            const mockApp: AppInstance = {
                name: 'test-app',
                status: APP_STATUS.MOUNTED,
                config: {
                    name: 'test-app',
                    entry: 'http://localhost:3000',
                    container: '#app',
                    activeWhen: '/test'
                }
            };

            mockAppRegistry.get.mockReturnValue(mockApp);

            const error = new Error('卸载失败');
            mockLifecycleManager.unmount.mockRejectedValue(error);

            await expect(kernel.unmountApplication('test-app')).rejects.toThrow(error);
            expect(mockAppRegistry.setError).toHaveBeenCalledWith('test-app', error);
        });

        it('应该在内核销毁后抛出错误', async () => {
            await kernel.destroy();

            await expect(kernel.unmountApplication('test-app')).rejects.toThrow(MicroCoreError);
        });

        it('应该处理错误状态的应用', async () => {
            const mockApp: AppInstance = {
                name: 'test-app',
                status: APP_STATUS.LOAD_ERROR,
                config: {
                    name: 'test-app',
                    entry: 'http://localhost:3000',
                    container: '#app',
                    activeWhen: '/test'
                }
            };

            mockAppRegistry.get.mockReturnValue(mockApp);

            await kernel.unmountApplication('test-app');

            expect(mockLifecycleManager.unmount).not.toHaveBeenCalled();
            expect(mockLogger.info).toHaveBeenCalledWith('应用 test-app 卸载完成');
        });
    });

    describe('应用状态转换', () => {
        it('应该正确处理完整的生命周期', async () => {
            const mockApp: AppInstance = {
                name: 'test-app',
                status: APP_STATUS.NOT_LOADED,
                config: {
                    name: 'test-app',
                    entry: 'http://localhost:3000',
                    container: '#app',
                    activeWhen: '/test'
                }
            };

            mockAppRegistry.get.mockReturnValue(mockApp);

            // 加载应用
            await kernel.loadApplication('test-app');
            expect(mockAppRegistry.updateStatus).toHaveBeenCalledWith('test-app', APP_STATUS.LOADING_SOURCE_CODE);
            expect(mockAppRegistry.updateStatus).toHaveBeenCalledWith('test-app', APP_STATUS.NOT_BOOTSTRAPPED);

            // 更新应用状态为已引导
            mockApp.status = APP_STATUS.NOT_MOUNTED;

            // 挂载应用
            await kernel.mountApplication('test-app');
            expect(mockLifecycleManager.mount).toHaveBeenCalledWith(mockApp);

            // 更新应用状态为已挂载
            mockApp.status = APP_STATUS.MOUNTED;

            // 卸载应用
            await kernel.unmountApplication('test-app');
            expect(mockLifecycleManager.unmount).toHaveBeenCalledWith(mockApp);
        });

        it('应该处理跳跃式状态转换', async () => {
            const mockApp: AppInstance = {
                name: 'test-app',
                status: APP_STATUS.NOT_LOADED,
                config: {
                    name: 'test-app',
                    entry: 'http://localhost:3000',
                    container: '#app',
                    activeWhen: '/test'
                }
            };

            mockAppRegistry.get.mockReturnValue(mockApp);

            // 直接挂载未加载的应用
            await kernel.mountApplication('test-app');

            // 应该先加载
            expect(mockAppRegistry.updateStatus).toHaveBeenCalledWith('test-app', APP_STATUS.LOADING_SOURCE_CODE);
            // 然后引导
            expect(mockLifecycleManager.bootstrap).toHaveBeenCalledWith(mockApp);
            // 最后挂载
            expect(mockLifecycleManager.mount).toHaveBeenCalledWith(mockApp);
        });
    });

    describe('并发处理', () => {
        it('应该处理并发加载请求', async () => {
            const mockApp: AppInstance = {
                name: 'test-app',
                status: APP_STATUS.NOT_LOADED,
                config: {
                    name: 'test-app',
                    entry: 'http://localhost:3000',
                    container: '#app',
                    activeWhen: '/test'
                }
            };

            mockAppRegistry.get.mockReturnValue(mockApp);

            // 并发加载同一个应用
            const promises = [
                kernel.loadApplication('test-app'),
                kernel.loadApplication('test-app'),
                kernel.loadApplication('test-app')
            ];

            await Promise.all(promises);

            // 应该只加载一次
            expect(mockAppRegistry.updateStatus).toHaveBeenCalledWith('test-app', APP_STATUS.LOADING_SOURCE_CODE);
        });

        it('应该处理并发挂载请求', async () => {
            const mockApp: AppInstance = {
                name: 'test-app',
                status: APP_STATUS.NOT_MOUNTED,
                config: {
                    name: 'test-app',
                    entry: 'http://localhost:3000',
                    container: '#app',
                    activeWhen: '/test'
                }
            };

            mockAppRegistry.get.mockReturnValue(mockApp);

            // 并发挂载同一个应用
            const promises = [
                kernel.mountApplication('test-app'),
                kernel.mountApplication('test-app'),
                kernel.mountApplication('test-app')
            ];

            await Promise.all(promises);

            // 应该只挂载一次
            expect(mockLifecycleManager.mount).toHaveBeenCalledWith(mockApp);
        });
    });

    describe('性能测试', () => {
        it('应该高效处理大量生命周期操作', async () => {
            const apps = Array.from({ length: 100 }, (_, i) => ({
                name: `app-${i}`,
                status: APP_STATUS.NOT_LOADED,
                config: {
                    name: `app-${i}`,
                    entry: `http://localhost:${3000 + i}`,
                    container: `#app-${i}`,
                    activeWhen: `/app-${i}`
                }
            }));

            mockAppRegistry.get.mockImplementation((name: string) =>
                apps.find(app => app.name === name)
            );

            const start = performance.now();

            // 批量加载应用
            await Promise.all(apps.map(app => kernel.loadApplication(app.name)));

            const end = performance.now();
            expect(end - start).toBeLessThan(2000); // 应该在2秒内完成
        });
    });
});