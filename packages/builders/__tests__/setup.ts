/**
 * @fileoverview Test Setup for Builders Package
 * <AUTHOR> <<EMAIL>>
 * @copyright 2025 Micro-Core. All rights reserved.
 */

import { vi, beforeAll, afterAll, beforeEach, afterEach, expect } from 'vitest';

// Global test environment setup
beforeAll(() => {
  // Set test environment variables
  process.env.NODE_ENV = 'test';
  process.env.VITEST = 'true';
  
  // Mock console methods in CI to reduce noise
  if (process.env.CI) {
    vi.spyOn(console, 'log').mockImplementation(() => {});
    vi.spyOn(console, 'info').mockImplementation(() => {});
    vi.spyOn(console, 'warn').mockImplementation(() => {});
    vi.spyOn(console, 'debug').mockImplementation(() => {});
  }
  
  // Set up global test timeouts
  vi.setConfig({
    testTimeout: 30000,
    hookTimeout: 10000
  });
});

afterAll(() => {
  // Cleanup global mocks
  vi.restoreAllMocks();
  vi.clearAllTimers();
});

beforeEach(() => {
  // Clear all mocks before each test
  vi.clearAllMocks();
  vi.clearAllTimers();
  
  // Reset modules to ensure clean state
  vi.resetModules();
});

afterEach(() => {
  // Cleanup after each test
  vi.restoreAllMocks();
  vi.clearAllTimers();
});

// Global mock implementations for common dependencies
vi.mock('fs', () => ({
  promises: {
    readFile: vi.fn(),
    writeFile: vi.fn(),
    mkdir: vi.fn(),
    readdir: vi.fn(),
    stat: vi.fn(),
    access: vi.fn()
  },
  existsSync: vi.fn(),
  readFileSync: vi.fn(),
  writeFileSync: vi.fn(),
  mkdirSync: vi.fn()
}));

vi.mock('path', async () => {
  const actual = await vi.importActual('path');
  return {
    ...actual,
    resolve: vi.fn().mockImplementation((...args) => args.join('/')),
    join: vi.fn().mockImplementation((...args) => args.join('/')),
    dirname: vi.fn().mockImplementation((path) => path.split('/').slice(0, -1).join('/')),
    basename: vi.fn().mockImplementation((path) => path.split('/').pop()),
    extname: vi.fn().mockImplementation((path) => {
      const parts = path.split('.');
      return parts.length > 1 ? `.${parts.pop()}` : '';
    })
  };
});

// Mock process.cwd for consistent test environment
vi.mock('process', () => ({
  cwd: vi.fn().mockReturnValue('/test/workspace'),
  env: {
    NODE_ENV: 'test',
    VITEST: 'true'
  },
  exit: vi.fn(),
  platform: 'linux'
}));

// Global test utilities
global.testUtils = {
  // Create mock config for builders
  createMockConfig: (overrides = {}) => ({
    entry: './src/index.ts',
    outDir: './dist',
    mode: 'development',
    ...overrides
  }),
  
  // Create mock build result
  createMockBuildResult: (overrides = {}) => ({
    success: true,
    outputs: [
      {
        type: 'chunk',
        fileName: 'main.js',
        size: 1000,
        code: '// mock code'
      }
    ],
    stats: {
      duration: 1000,
      fileCount: 1,
      totalSize: 1000,
      errors: 0,
      warnings: 0
    },
    ...overrides
  }),
  
  // Create mock dev server config
  createMockDevServerConfig: (overrides = {}) => ({
    port: 3000,
    host: 'localhost',
    hot: true,
    ...overrides
  }),
  
  // Wait for async operations
  waitFor: (ms: number) => new Promise(resolve => setTimeout(resolve, ms)),
  
  // Create mock file system structure
  createMockFS: (structure: Record<string, any>) => {
    const mockFS = new Map();
    
    const addToFS = (path: string, content: any) => {
      mockFS.set(path, content);
    };
    
    const traverse = (obj: any, currentPath = '') => {
      for (const [key, value] of Object.entries(obj)) {
        const fullPath = currentPath ? `${currentPath}/${key}` : key;
        if (typeof value === 'object' && value !== null) {
          traverse(value, fullPath);
        } else {
          addToFS(fullPath, value);
        }
      }
    };
    
    traverse(structure);
    return mockFS;
  }
};

// Extend expect with custom matchers
expect.extend({
  toBeValidBuildResult(received) {
    const pass = received && 
      typeof received.success === 'boolean' &&
      Array.isArray(received.outputs) &&
      received.stats &&
      typeof received.stats.duration === 'number';
    
    return {
      pass,
      message: () => pass 
        ? `Expected ${received} not to be a valid build result`
        : `Expected ${received} to be a valid build result with success, outputs, and stats properties`
    };
  },
  
  toBeValidBuilderConfig(received) {
    const pass = received &&
      (typeof received.entry === 'string' || typeof received.entry === 'object') &&
      typeof received.outDir === 'string';
    
    return {
      pass,
      message: () => pass
        ? `Expected ${received} not to be a valid builder config`
        : `Expected ${received} to be a valid builder config with entry and outDir properties`
    };
  }
});

// Global error handling for unhandled promises
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
});

// Export test utilities for use in test files
export { testUtils } from './test-utils';
