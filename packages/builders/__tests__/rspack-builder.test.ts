/**
 * RSPack 构建器测试
 */

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { RSPackBuilder } from '../builder-rspack/src';
import { BuilderOptions } from '../shared/types';
import * as testUtils from './test-utils';

// 模拟 @rspack/core 依赖
vi.mock('@rspack/core', () => ({
    rspack: vi.fn(() => ({
        run: vi.fn((callback) => callback(null, {
            toJson: () => ({
                errors: [],
                warnings: []
            })
        }))
    }))
}));

// 模拟 @rspack/dev-server 依赖
vi.mock('@rspack/dev-server', () => ({
    default: class MockRSPackDevServer {
        constructor() { }
        start = vi.fn().mockResolvedValue(undefined);
        stop = vi.fn().mockResolvedValue(undefined);
    }
}));


describe('RSPackBuilder', () => {
    let builder: RSPackBuilder;
    let mockOptions: BuilderOptions;

    beforeEach(() => {
        // 重置所有模拟
        vi.resetAllMocks();

        // 创建模拟选项
        mockOptions = {
            entry: './src/index.js',
            output: {
                path: './dist',
                filename: 'bundle.js'
            },
            mode: 'development',
            devServer: {
                port: 8080
            }
        };

        // 创建构建器实例
        builder = new RSPackBuilder();
    });

    afterEach(() => {
        vi.clearAllMocks();
    });

    describe('构建配置', () => {
        it('应该能够创建有效的 RSPack 配置', () => {
            const config = builder.createConfig(mockOptions);
            expect(config).toBeDefined();
            expect(config.entry).toBe(mockOptions.entry);
            expect(config.output).toBeDefined();
            expect(config.mode).toBe(mockOptions.mode);
        });

        it('应该能够处理自定义配置', () => {
            const customOptions = {
                ...mockOptions,
                customConfig: {
                    resolve: {
                        extensions: ['.js', '.jsx', '.ts', '.tsx']
                    }
                }
            };

            const config = builder.createConfig(customOptions);
            expect(config.resolve).toBeDefined();
            expect(config.resolve.extensions).toEqual(['.js', '.jsx', '.ts', '.tsx']);
        });

        it('应该能够处理插件配置', () => {
            const pluginOptions = {
                ...mockOptions,
                plugins: [
                    { name: 'HtmlRspackPlugin', options: { template: 'index.html' } }
                ]
            };

            const config = builder.createConfig(pluginOptions);
            expect(config.plugins).toBeDefined();
            expect(config.plugins.length).toBeGreaterThan(0);
        });
    });

    describe('构建过程', () => {
        it('应该能够执行构建', async () => {
            const buildSpy = vi.spyOn(builder as any, 'runRSPack').mockResolvedValue({
                errors: [],
                warnings: []
            });

            await builder.build(mockOptions);
            expect(buildSpy).toHaveBeenCalled();
        });

        it('应该在构建失败时抛出错误', async () => {
            vi.spyOn(builder as any, 'runRSPack').mockResolvedValue({
                errors: ['构建错误'],
                warnings: []
            });

            await expect(builder.build(mockOptions)).rejects.toThrow();
        });

        it('应该能够处理构建警告', async () => {
            const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => { });

            vi.spyOn(builder as any, 'runRSPack').mockResolvedValue({
                errors: [],
                warnings: ['构建警告']
            });

            await builder.build(mockOptions);
            expect(consoleSpy).toHaveBeenCalled();
        });
    });

    describe('开发服务器', () => {
        it('应该能够启动开发服务器', async () => {
            const startSpy = vi.spyOn(builder as any, 'startDevServer').mockResolvedValue(undefined);

            await builder.serve(mockOptions);
            expect(startSpy).toHaveBeenCalled();
        });

        it('应该能够停止开发服务器', async () => {
            const stopSpy = vi.fn().mockResolvedValue(undefined);
            (builder as any).devServer = { stop: stopSpy };

            await builder.stop();
            expect(stopSpy).toHaveBeenCalled();
        });

        it('应该在开发服务器启动失败时抛出错误', async () => {
            vi.spyOn(builder as any, 'startDevServer').mockRejectedValue(new Error('服务器启动失败'));

            await expect(builder.serve(mockOptions)).rejects.toThrow('服务器启动失败');
        });
    });

    describe('构建验证', () => {
        it('应该能够验证构建产物', async () => {
            const validateSpy = vi.spyOn(testUtils, 'validateBuildOutput').mockResolvedValue(true);

            await builder.build(mockOptions);
            expect(validateSpy).toHaveBeenCalled();
        });

        it('应该能够检测构建产物完整性', async () => {
            const checkIntegritySpy = vi.spyOn(testUtils, 'checkBuildIntegrity').mockResolvedValue(true);

            await builder.build(mockOptions);
            expect(checkIntegritySpy).toHaveBeenCalled();
        });
    });

    describe('错误处理', () => {
        it('应该能够处理配置错误', () => {
            const invalidOptions = {
                ...mockOptions,
                entry: undefined
            } as any;

            expect(() => builder.createConfig(invalidOptions)).toThrow();
        });

        it('应该能够处理构建过程错误', async () => {
            vi.spyOn(builder as any, 'runRSPack').mockRejectedValue(new Error('构建过程错误'));

            await expect(builder.build(mockOptions)).rejects.toThrow('构建过程错误');
        });

        it('应该能够处理开发服务器错误', async () => {
            vi.spyOn(builder as any, 'startDevServer').mockRejectedValue(new Error('服务器错误'));

            await expect(builder.serve(mockOptions)).rejects.toThrow('服务器错误');
        });
    });

    describe('性能优化', () => {
        it('应该能够配置性能优化选项', () => {
            const optimizeOptions = {
                ...mockOptions,
                optimize: {
                    minify: true,
                    treeShaking: true
                }
            };

            const config = builder.createConfig(optimizeOptions);
            expect(config.optimization).toBeDefined();
            expect(config.optimization.minimize).toBe(true);
        });

        it('应该能够配置代码分割', () => {
            const splitChunksOptions = {
                ...mockOptions,
                optimize: {
                    splitChunks: {
                        chunks: 'all'
                    }
                }
            };

            const config = builder.createConfig(splitChunksOptions);
            expect(config.optimization).toBeDefined();
            expect(config.optimization.splitChunks).toBeDefined();
            expect(config.optimization.splitChunks.chunks).toBe('all');
        });
    });

    describe('Rust 特性支持', () => {
        it('应该能够配置 Rust 特性', () => {
            const rustOptions = {
                ...mockOptions,
                rustFeatures: {
                    enabled: true,
                    threads: 4
                }
            };

            const config = builder.createConfig(rustOptions);
            expect(config.builtins).toBeDefined();
            expect(config.builtins.threading).toBeDefined();
            expect(config.builtins.threading.threads).toBe(4);
        });

        it('应该能够配置 Rust 编译器选项', () => {
            const rustCompilerOptions = {
                ...mockOptions,
                rustCompiler: {
                    target: 'wasm32-unknown-unknown'
                }
            };

            const config = builder.createConfig(rustCompilerOptions);
            expect(config.builtins).toBeDefined();
            expect(config.builtins.rustc).toBeDefined();
            expect(config.builtins.rustc.target).toBe('wasm32-unknown-unknown');
        });
    });
});