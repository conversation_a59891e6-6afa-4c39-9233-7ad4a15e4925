/**
 * @fileoverview Test Utilities for Builders Package
 * <AUTHOR> <<EMAIL>>
 * @copyright 2025 Micro-Core. All rights reserved.
 */

import { vi } from 'vitest';
import type { BaseBuilderConfig, BuildResult, DevServerConfig } from '../shared/types';

/**
 * Test utilities for builder testing
 */
export const testUtils = {
  /**
   * Create mock configuration for builders
   */
  createMockConfig: (overrides: Partial<BaseBuilderConfig> = {}): BaseBuilderConfig => ({
    id: 'test-builder',
    name: 'test-app',
    entry: './src/index.ts',
    outDir: './dist',
    ...overrides
  }),

  /**
   * Create mock build result
   */
  createMockBuildResult: (overrides: Partial<BuildResult> = {}): BuildResult => ({
    success: true,
    outputs: [
      {
        type: 'chunk',
        fileName: 'main.js',
        size: 1000,
        code: '// mock compiled code'
      }
    ],
    stats: {
      duration: 1000,
      fileCount: 1,
      totalSize: 1000,
      errors: 0,
      warnings: 0
    },
    ...overrides
  }),

  /**
   * Create mock dev server configuration
   */
  createMockDevServerConfig: (overrides: Partial<DevServerConfig> = {}): DevServerConfig => ({
    port: 3000,
    host: 'localhost',
    hot: true,
    ...overrides
  }),

  /**
   * Create mock error result
   */
  createMockErrorResult: (errorMessage = 'Build failed'): BuildResult => ({
    success: false,
    outputs: [],
    errors: [{ message: errorMessage }],
    stats: {
      duration: 500,
      fileCount: 0,
      totalSize: 0,
      errors: 1,
      warnings: 0
    }
  }),

  /**
   * Create mock warning result
   */
  createMockWarningResult: (warnings: string[] = ['Warning message']): BuildResult => ({
    success: true,
    outputs: [
      {
        type: 'chunk',
        fileName: 'main.js',
        size: 1000,
        code: '// code with warnings'
      }
    ],
    warnings: warnings.map(message => ({ message })),
    stats: {
      duration: 1200,
      fileCount: 1,
      totalSize: 1000,
      errors: 0,
      warnings: warnings.length
    }
  }),

  /**
   * Wait for async operations
   */
  waitFor: (ms: number): Promise<void> => 
    new Promise(resolve => setTimeout(resolve, ms)),

  /**
   * Create mock file system structure
   */
  createMockFS: (structure: Record<string, any>): Map<string, any> => {
    const mockFS = new Map();
    
    const addToFS = (path: string, content: any) => {
      mockFS.set(path, content);
    };
    
    const traverse = (obj: any, currentPath = '') => {
      for (const [key, value] of Object.entries(obj)) {
        const fullPath = currentPath ? `${currentPath}/${key}` : key;
        if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
          traverse(value, fullPath);
        } else {
          addToFS(fullPath, value);
        }
      }
    };
    
    traverse(structure);
    return mockFS;
  },

  /**
   * Create mock compiler instance
   */
  createMockCompiler: () => ({
    run: vi.fn(),
    watch: vi.fn(),
    close: vi.fn(),
    hooks: {
      done: { tap: vi.fn() },
      failed: { tap: vi.fn() },
      invalid: { tap: vi.fn() }
    }
  }),

  /**
   * Create mock dev server instance
   */
  createMockDevServer: () => ({
    start: vi.fn().mockResolvedValue(undefined),
    stop: vi.fn().mockResolvedValue(undefined),
    close: vi.fn().mockResolvedValue(undefined),
    listen: vi.fn().mockResolvedValue(undefined),
    port: 3000,
    host: 'localhost'
  }),

  /**
   * Create mock stats object
   */
  createMockStats: (overrides: any = {}) => ({
    hasErrors: vi.fn().mockReturnValue(false),
    hasWarnings: vi.fn().mockReturnValue(false),
    toJson: vi.fn().mockReturnValue({
      assets: [
        { name: 'main.js', size: 1000 }
      ],
      errors: [],
      warnings: [],
      ...overrides
    })
  }),

  /**
   * Create performance test data
   */
  createPerformanceTestData: (count: number): Array<{ name: string; size: number; type: string }> => {
    const assets: Array<{ name: string; size: number; type: string }> = [];
    for (let i = 0; i < count; i++) {
      assets.push({
        name: `chunk-${i}.js`,
        size: Math.floor(Math.random() * 5000) + 500,
        type: 'chunk'
      });
    }
    return assets;
  },

  /**
   * Measure execution time
   */
  measureTime: async <T>(fn: () => Promise<T>): Promise<{ result: T; duration: number }> => {
    const start = Date.now();
    const result = await fn();
    const duration = Date.now() - start;
    return { result, duration };
  },

  /**
   * Create mock plugin
   */
  createMockPlugin: (name: string) => ({
    name,
    setup: vi.fn(),
    apply: vi.fn(),
    configResolved: vi.fn(),
    buildStart: vi.fn(),
    buildEnd: vi.fn()
  }),

  /**
   * Simulate build process
   */
  simulateBuild: async (duration = 1000, shouldFail = false): Promise<BuildResult> => {
    await testUtils.waitFor(duration);
    
    if (shouldFail) {
      return testUtils.createMockErrorResult('Simulated build failure');
    }
    
    return testUtils.createMockBuildResult({
      stats: {
        duration,
        fileCount: Math.floor(Math.random() * 10) + 1,
        totalSize: Math.floor(Math.random() * 50000) + 1000,
        errors: 0,
        warnings: 0
      }
    });
  },

  /**
   * Create mock bundle graph (for Parcel)
   */
  createMockBundleGraph: () => ({
    getBundles: vi.fn().mockReturnValue([
      {
        filePath: '/dist/index.html',
        stats: { size: 800 },
        type: 'html'
      },
      {
        filePath: '/dist/main.js',
        stats: { size: 2000 },
        type: 'js'
      }
    ])
  }),

  /**
   * Create mock Rollup bundle
   */
  createMockRollupBundle: () => ({
    generate: vi.fn().mockResolvedValue({
      output: [
        {
          type: 'chunk',
          fileName: 'main.js',
          code: '// Rollup compiled code'
        }
      ]
    }),
    write: vi.fn().mockResolvedValue(undefined),
    close: vi.fn().mockResolvedValue(undefined)
  }),

  /**
   * Validate builder implementation
   */
  validateBuilderImplementation: (builder: any) => {
    const requiredMethods = [
      'createBuilderConfig',
      'executeBuild', 
      'startDevServer',
      'stopDevServer'
    ];
    
    const requiredProperties = ['name', 'version'];
    
    const missingMethods = requiredMethods.filter(method => 
      typeof builder[method] !== 'function'
    );
    
    const missingProperties = requiredProperties.filter(prop => 
      !builder[prop]
    );
    
    return {
      isValid: missingMethods.length === 0 && missingProperties.length === 0,
      missingMethods,
      missingProperties
    };
  },

  /**
   * Create test timeout wrapper
   */
  withTimeout: <T>(promise: Promise<T>, timeout = 5000): Promise<T> => {
    return Promise.race([
      promise,
      new Promise<never>((_, reject) => 
        setTimeout(() => reject(new Error(`Operation timed out after ${timeout}ms`)), timeout)
      )
    ]);
  }
};

export default testUtils;
