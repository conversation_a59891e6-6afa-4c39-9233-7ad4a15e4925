/**
 * 构建过程错误处理和验证测试
 */

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { ESBuildBuilder } from '../builder-esbuild/src';
import { ParcelBuilder } from '../builder-parcel/src';
import { RollupBuilder } from '../builder-rollup/src';
import { RSPackBuilder } from '../builder-rspack/src';
import { TurbopackBuilder } from '../builder-turbopack/src';
import { ViteBuilder } from '../builder-vite/src';
import { WebpackBuilder } from '../builder-webpack/src';
import { BuilderOptions } from '../shared/types';
import * as testUtils from './test-utils';

// 模拟所有构建工具依赖
vi.mock('webpack', () => ({
    default: vi.fn(() => ({
        run: vi.fn((callback) => callback(null, {
            toJson: () => ({
                errors: [],
                warnings: []
            })
        }))
    }))
}));

vi.mock('vite', () => ({
    build: vi.fn().mockResolvedValue(undefined),
    createServer: vi.fn().mockResolvedValue({
        listen: vi.fn().mockResolvedValue(undefined),
        close: vi.fn().mockResolvedValue(undefined)
    })
}));

vi.mock('rollup', () => ({
    rollup: vi.fn().mockResolvedValue({
        write: vi.fn().mockResolvedValue({
            output: [{ fileName: 'bundle.js' }]
        }),
        close: vi.fn().mockResolvedValue(undefined)
    }),
    watch: vi.fn().mockReturnValue({
        on: vi.fn(),
        close: vi.fn()
    })
}));

vi.mock('esbuild', () => ({
    build: vi.fn().mockResolvedValue({
        errors: [],
        warnings: []
    }),
    context: vi.fn().mockResolvedValue({
        watch: vi.fn().mockResolvedValue(undefined),
        dispose: vi.fn().mockResolvedValue(undefined),
        serve: vi.fn().mockResolvedValue({
            stop: vi.fn().mockResolvedValue(undefined)
        })
    })
}));

vi.mock('@parcel/core', () => {
    return {
        default: class MockParcel {
            constructor() { }
            run() {
                return Promise.resolve({
                    bundleGraph: {},
                    buildTime: 100
                });
            }
        }
    };
});

vi.mock('@rspack/core', () => ({
    rspack: vi.fn(() => ({
        run: vi.fn((callback) => callback(null, {
            toJson: () => ({
                errors: [],
                warnings: []
            })
        }))
    }))
}));

vi.mock('@vercel/turbopack', () => ({
    build: vi.fn().mockResolvedValue({
        errors: [],
        warnings: []
    }),
    createServer: vi.fn().mockResolvedValue({
        start: vi.fn().mockResolvedValue(undefined),
        stop: vi.fn().mockResolvedValue(undefined)
    })
}));

describe('构建过程错误处理和验证', () => {
    // 测试所有构建器
    const builders = [
        { name: 'Webpack', builder: new WebpackBuilder() },
        { name: 'Vite', builder: new ViteBuilder() },
        { name: 'Rollup', builder: new RollupBuilder() },
        { name: 'ESBuild', builder: new ESBuildBuilder() },
        { name: 'Parcel', builder: new ParcelBuilder() },
        { name: 'RSPack', builder: new RSPackBuilder() },
        { name: 'Turbopack', builder: new TurbopackBuilder() }
    ];

    let mockOptions: BuilderOptions;

    beforeEach(() => {
        // 重置所有模拟
        vi.resetAllMocks();

        // 创建模拟选项
        mockOptions = {
            entry: './src/index.js',
            output: {
                path: './dist',
                filename: 'bundle.js'
            },
            mode: 'development'
        };
    });

    afterEach(() => {
        vi.clearAllMocks();
    });

    describe('构建失败场景', () => {
        builders.forEach(({ name, builder }) => {
            it(`${name}构建器应该能够处理构建失败`, async () => {
                // 模拟构建失败
                vi.spyOn(builder, 'build').mockRejectedValue(new Error('构建失败'));

                // 测试错误处理
                await expect(builder.build(mockOptions)).rejects.toThrow('构建失败');
            });
        });

        it('应该能够处理配置验证错误', () => {
            builders.forEach(({ name, builder }) => {
                const invalidOptions = {
                    ...mockOptions,
                    entry: undefined
                } as any;

                expect(() => builder.createConfig(invalidOptions)).toThrow();
            });
        });

        it('应该能够处理输出路径错误', async () => {
            const validateSpy = vi.spyOn(testUtils, 'validateBuildOutput').mockImplementation((path) => {
                if (path === './invalid-path') {
                    return Promise.reject(new Error('无效的输出路径'));
                }
                return Promise.resolve(true);
            });

            const invalidOptions = {
                ...mockOptions,
                output: {
                    path: './invalid-path',
                    filename: 'bundle.js'
                }
            };

            for (const { builder } of builders) {
                try {
                    await builder.build(invalidOptions);
                } catch (error) {
                    expect(error).toBeDefined();
                }
            }

            expect(validateSpy).toHaveBeenCalled();
        });
    });

    describe('错误报告', () => {
        it('应该能够提供详细的错误报告', async () => {
            const errorReportSpy = vi.spyOn(testUtils, 'generateErrorReport').mockResolvedValue({
                errors: ['详细错误信息'],
                warnings: ['警告信息']
            });

            for (const { builder } of builders) {
                vi.spyOn(builder, 'build').mockRejectedValue(new Error('构建错误'));

                try {
                    await builder.build(mockOptions);
                } catch (error) {
                    // 忽略错误
                }
            }

            expect(errorReportSpy).toHaveBeenCalled();
        });
    });

    describe('构建产物验证', () => {
        it('应该能够验证构建产物完整性', async () => {
            const checkIntegritySpy = vi.spyOn(testUtils, 'checkBuildIntegrity').mockResolvedValue(true);

            for (const { builder } of builders) {
                vi.spyOn(builder, 'build').mockResolvedValue(undefined);
                await builder.build(mockOptions);
            }

            expect(checkIntegritySpy).toHaveBeenCalled();
        });

        it('应该能够检测构建产物大小', async () => {
            const checkSizeSpy = vi.spyOn(testUtils, 'checkBuildSize').mockResolvedValue({
                totalSize: 1024,
                files: [{ name: 'bundle.js', size: 1024 }]
            });

            for (const { builder } of builders) {
                vi.spyOn(builder, 'build').mockResolvedValue(undefined);
                await builder.build(mockOptions);
            }

            expect(checkSizeSpy).toHaveBeenCalled();
        });

        it('应该能够检测构建产物兼容性', async () => {
            const checkCompatibilitySpy = vi.spyOn(testUtils, 'checkBuildCompatibility').mockResolvedValue({
                compatible: true,
                targets: ['es2015', 'chrome80']
            });

            for (const { builder } of builders) {
                vi.spyOn(builder, 'build').mockResolvedValue(undefined);
                await builder.build(mockOptions);
            }

            expect(checkCompatibilitySpy).toHaveBeenCalled();
        });
    });

    describe('构建性能监控', () => {
        it('应该能够监控构建性能', async () => {
            const monitorPerformanceSpy = vi.spyOn(testUtils, 'monitorBuildPerformance').mockResolvedValue({
                duration: 1000,
                memoryUsage: 100,
                cpuUsage: 50
            });

            for (const { builder } of builders) {
                vi.spyOn(builder, 'build').mockResolvedValue(undefined);
                await builder.build(mockOptions);
            }

            expect(monitorPerformanceSpy).toHaveBeenCalled();
        });
    });

    describe('构建缓存管理', () => {
        it('应该能够管理构建缓存', async () => {
            const manageCacheSpy = vi.spyOn(testUtils, 'manageBuildCache').mockResolvedValue({
                cacheSize: 1024,
                cacheHits: 10,
                cacheMisses: 2
            });

            const cacheOptions = {
                ...mockOptions,
                cache: {
                    enabled: true,
                    directory: '.cache'
                }
            };

            for (const { builder } of builders) {
                vi.spyOn(builder, 'build').mockResolvedValue(undefined);
                await builder.build(cacheOptions);
            }

            expect(manageCacheSpy).toHaveBeenCalled();
        });
    });
});