/**
 * @fileoverview DevTools 插件单元测试
 * @description 测试开发者工具插件的完整功能，确保100%代码覆盖率
 */

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { DevToolsPlugin } from '../../src/devtools-plugin';
import type { DevToolsConfig } from '../../src/types';

// Mock DOM 环境
Object.defineProperty(window, 'PerformanceObserver', {
    writable: true,
    value: vi.fn().mockImplementation((callback) => ({
        observe: vi.fn(),
        disconnect: vi.fn(),
        callback
    }))
});

// Mock 内核
const createMockKernel = () => ({
    registerPlugin: vi.fn(),
    on: vi.fn(),
    off: vi.fn(),
    emit: vi.fn()
});

describe('DevToolsPlugin 开发者工具插件', () => {
    let plugin: DevToolsPlugin;
    let mockKernel: ReturnType<typeof createMockKernel>;

    beforeEach(() => {
        // 清理 DOM
        document.body.innerHTML = '';

        // 重置环境变量
        vi.stubEnv('NODE_ENV', 'development');

        // 创建模拟内核
        mockKernel = createMockKernel();

        // 创建插件实例
        plugin = new DevToolsPlugin();
    });

    afterEach(() => {
        vi.clearAllMocks();
        vi.unstubAllEnvs();

        // 清理 DOM
        document.body.innerHTML = '';

        // 移除事件监听器
        document.removeEventListener('keydown', (plugin as any).handleKeydown);
    });

    describe('构造函数', () => {
        it('应该使用默认配置创建插件实例', () => {
            const newPlugin = new DevToolsPlugin();

            expect(newPlugin.name).toBe('devtools');
            expect(newPlugin.version).toBe('0.1.0');
            expect((newPlugin as any).config.enabled).toBe(true);
            expect((newPlugin as any).config.showPanel).toBe(false);
            expect((newPlugin as any).config.panelPosition).toBe('bottom');
        });

        it('应该使用自定义配置创建插件实例', () => {
            const config: DevToolsConfig = {
                enabled: false,
                showPanel: true,
                panelPosition: 'top',
                enablePerformanceMonitor: false,
                logLevel: 'error'
            };

            const newPlugin = new DevToolsPlugin(config);

            expect((newPlugin as any).config.enabled).toBe(false);
            expect((newPlugin as any).config.showPanel).toBe(true);
            expect((newPlugin as any).config.panelPosition).toBe('top');
            expect((newPlugin as any).config.enablePerformanceMonitor).toBe(false);
            expect((newPlugin as any).config.logLevel).toBe('error');
        });

        it('应该合并默认配置和自定义配置', () => {
            const config: DevToolsConfig = {
                showPanel: true,
                logLevel: 'warn'
            };

            const newPlugin = new DevToolsPlugin(config);

            expect((newPlugin as any).config.enabled).toBe(true); // 默认值
            expect((newPlugin as any).config.showPanel).toBe(true); // 自定义值
            expect((newPlugin as any).config.panelPosition).toBe('bottom'); // 默认值
            expect((newPlugin as any).config.logLevel).toBe('warn'); // 自定义值
        });
    });

    describe('插件生命周期', () => {
        describe('install', () => {
            it('应该成功安装插件', () => {
                plugin.install(mockKernel as any);

                expect(mockKernel.registerPlugin).toHaveBeenCalledWith(plugin);
                expect((plugin as any).kernel).toBe(mockKernel);
                expect(mockKernel.on).toHaveBeenCalledWith('app:registered', expect.any(Function));
                expect(mockKernel.on).toHaveBeenCalledWith('app:loading', expect.any(Function));
                expect(mockKernel.on).toHaveBeenCalledWith('app:mounted', expect.any(Function));
                expect(mockKernel.on).toHaveBeenCalledWith('app:unmounted', expect.any(Function));
                expect(mockKernel.on).toHaveBeenCalledWith('app:error', expect.any(Function));
            });

            it('应该在禁用时跳过安装', () => {
                const disabledPlugin = new DevToolsPlugin({ enabled: false });

                disabledPlugin.install(mockKernel as any);

                expect(mockKernel.registerPlugin).not.toHaveBeenCalled();
                expect((disabledPlugin as any).kernel).toBeNull();
            });

            it('应该在配置显示面板时创建面板', () => {
                const pluginWithPanel = new DevToolsPlugin({ showPanel: true });

                pluginWithPanel.install(mockKernel as any);

                const panel = document.getElementById('micro-core-devtools');
                expect(panel).toBeTruthy();
                expect(panel?.style.display).toBe('flex');
            });

            it('应该设置性能监控', () => {
                const mockObserver = vi.fn().mockImplementation(() => ({
                    observe: vi.fn(),
                    disconnect: vi.fn()
                }));
                (window as any).PerformanceObserver = mockObserver;

                plugin.install(mockKernel as any);

                expect(mockObserver).toHaveBeenCalled();
            });

            it('应该添加 devtools 方法到内核', () => {
                plugin.install(mockKernel as any);

                expect((mockKernel as any).devtools).toBeDefined();
                expect((mockKernel as any).devtools.show).toBeInstanceOf(Function);
                expect((mockKernel as any).devtools.hide).toBeInstanceOf(Function);
                expect((mockKernel as any).devtools.toggle).toBeInstanceOf(Function);
                expect((mockKernel as any).devtools.getApps).toBeInstanceOf(Function);
                expect((mockKernel as any).devtools.getLogs).toBeInstanceOf(Function);
                expect((mockKernel as any).devtools.clearLogs).toBeInstanceOf(Function);
            });
        });

        describe('uninstall', () => {
            beforeEach(() => {
                plugin.install(mockKernel as any);
            });

            it('应该成功卸载插件', () => {
                plugin.uninstall(mockKernel as any);

                expect((plugin as any).kernel).toBeNull();
                expect((plugin as any).apps.size).toBe(0);
                expect((plugin as any).logs.length).toBe(0);
                expect((mockKernel as any).devtools).toBeUndefined();
            });

            it('应该移除面板', () => {
                // 先创建面板
                plugin.showPanel();
                const panel = document.getElementById('micro-core-devtools');
                expect(panel).toBeTruthy();

                plugin.uninstall(mockKernel as any);

                const panelAfterUninstall = document.getElementById('micro-core-devtools');
                expect(panelAfterUninstall).toBeNull();
            });

            it('应该断开性能监控', () => {
                const mockDisconnect = vi.fn();
                (plugin as any).performanceObserver = { disconnect: mockDisconnect };

                plugin.uninstall(mockKernel as any);

                expect(mockDisconnect).toHaveBeenCalled();
                expect((plugin as any).performanceObserver).toBeNull();
            });
        });
    });

    describe('应用生命周期监听', () => {
        beforeEach(() => {
            plugin.install(mockKernel as any);
        });

        it('应该处理应用注册事件', () => {
            const appConfig = {
                name: 'test-app',
                version: '1.0.0',
                entry: 'http://localhost:3000',
                container: '#app',
                activeWhen: '/test'
            };

            // 获取注册的事件监听器
            const registeredListener = mockKernel.on.mock.calls.find(
                call => call[0] === 'app:registered'
            )?.[1];

            expect(registeredListener).toBeDefined();
            registeredListener(appConfig);

            const apps = plugin.getApps();
            expect(apps).toHaveLength(1);
            expect(apps[0].name).toBe('test-app');
            expect(apps[0].status).toBe('registered');
        });

        it('应该处理应用加载事件', () => {
            // 先注册应用
            const appConfig = { name: 'test-app' };
            const registeredListener = mockKernel.on.mock.calls.find(
                call => call[0] === 'app:registered'
            )?.[1];
            registeredListener(appConfig);

            // 处理加载事件
            const loadingListener = mockKernel.on.mock.calls.find(
                call => call[0] === 'app:loading'
            )?.[1];
            loadingListener('test-app');

            const apps = plugin.getApps();
            expect(apps[0].status).toBe('loading');
        });

        it('应该处理应用挂载事件', () => {
            // 先注册应用
            const appConfig = { name: 'test-app' };
            const registeredListener = mockKernel.on.mock.calls.find(
                call => call[0] === 'app:registered'
            )?.[1];
            registeredListener(appConfig);

            // 处理挂载事件
            const mountedListener = mockKernel.on.mock.calls.find(
                call => call[0] === 'app:mounted'
            )?.[1];
            mountedListener('test-app');

            const apps = plugin.getApps();
            expect(apps[0].status).toBe('mounted');
        });

        it('应该处理应用卸载事件', () => {
            // 先注册应用
            const appConfig = { name: 'test-app' };
            const registeredListener = mockKernel.on.mock.calls.find(
                call => call[0] === 'app:registered'
            )?.[1];
            registeredListener(appConfig);

            // 处理卸载事件
            const unmountedListener = mockKernel.on.mock.calls.find(
                call => call[0] === 'app:unmounted'
            )?.[1];
            unmountedListener('test-app');

            const apps = plugin.getApps();
            expect(apps[0].status).toBe('unmounted');
        });

        it('应该处理应用错误事件', () => {
            // 先注册应用
            const appConfig = { name: 'test-app' };
            const registeredListener = mockKernel.on.mock.calls.find(
                call => call[0] === 'app:registered'
            )?.[1];
            registeredListener(appConfig);

            // 处理错误事件
            const errorListener = mockKernel.on.mock.calls.find(
                call => call[0] === 'app:error'
            )?.[1];
            const error = new Error('Test error');
            errorListener('test-app', error);

            const apps = plugin.getApps();
            expect(apps[0].status).toBe('error');
        });
    });

    describe('面板管理', () => {
        beforeEach(() => {
            plugin.install(mockKernel as any);
        });

        describe('showPanel', () => {
            it('应该显示面板', () => {
                plugin.showPanel();

                const panel = document.getElementById('micro-core-devtools');
                expect(panel).toBeTruthy();
                expect(panel?.style.display).toBe('flex');
                expect((plugin as any).isVisible).toBe(true);
            });

            it('应该在面板不存在时创建面板', () => {
                expect(document.getElementById('micro-core-devtools')).toBeNull();

                plugin.showPanel();

                const panel = document.getElementById('micro-core-devtools');
                expect(panel).toBeTruthy();
            });
        });

        describe('hidePanel', () => {
            it('应该隐藏面板', () => {
                plugin.showPanel();
                expect((plugin as any).isVisible).toBe(true);

                plugin.hidePanel();

                const panel = document.getElementById('micro-core-devtools');
                expect(panel?.style.display).toBe('none');
                expect((plugin as any).isVisible).toBe(false);
            });
        });

        describe('togglePanel', () => {
            it('应该切换面板显示状态', () => {
                expect((plugin as any).isVisible).toBe(false);

                plugin.togglePanel();
                expect((plugin as any).isVisible).toBe(true);

                plugin.togglePanel();
                expect((plugin as any).isVisible).toBe(false);
            });
        });
    });

    describe('快捷键处理', () => {
        beforeEach(() => {
            plugin.install(mockKernel as any);
        });

        it('应该处理切换面板快捷键', () => {
            const event = new KeyboardEvent('keydown', {
                key: 'd',
                ctrlKey: true,
                shiftKey: true
            });

            expect((plugin as any).isVisible).toBe(false);

            document.dispatchEvent(event);

            expect((plugin as any).isVisible).toBe(true);
        });

        it('应该处理切换检查器快捷键', () => {
            const consoleSpy = vi.spyOn(console, 'info').mockImplementation(() => { });

            const event = new KeyboardEvent('keydown', {
                key: 'i',
                ctrlKey: true,
                shiftKey: true
            });

            document.dispatchEvent(event);

            expect(consoleSpy).toHaveBeenCalledWith(
                expect.stringContaining('[Micro-Core DevTools] 应用检查器功能待实现')
            );

            consoleSpy.mockRestore();
        });

        it('应该正确匹配快捷键', () => {
            const matchHotkey = (plugin as any).matchHotkey;

            // 测试正确的快捷键
            const correctEvent = new KeyboardEvent('keydown', {
                key: 'd',
                ctrlKey: true,
                shiftKey: true
            });
            expect(matchHotkey(correctEvent, 'Ctrl+Shift+D')).toBe(true);

            // 测试错误的快捷键
            const wrongEvent = new KeyboardEvent('keydown', {
                key: 'd',
                ctrlKey: true
            });
            expect(matchHotkey(wrongEvent, 'Ctrl+Shift+D')).toBe(false);
        });

        it('应该处理自定义快捷键', () => {
            const customPlugin = new DevToolsPlugin({
                hotkeys: {
                    togglePanel: 'Alt+F12',
                    toggleInspector: 'Ctrl+Alt+I'
                }
            });
            customPlugin.install(mockKernel as any);

            const event = new KeyboardEvent('keydown', {
                key: 'f12',
                altKey: true
            });

            expect((customPlugin as any).isVisible).toBe(false);

            document.dispatchEvent(event);

            expect((customPlugin as any).isVisible).toBe(true);
        });
    });

    describe('日志系统', () => {
        beforeEach(() => {
            plugin.install(mockKernel as any);
        });

        it('应该记录不同级别的日志', () => {
            const consoleSpy = vi.spyOn(console, 'info').mockImplementation(() => { });

            (plugin as any).log('info', 'Test info message');

            const logs = plugin.getLogs();
            expect(logs).toHaveLength(1);
            expect(logs[0].level).toBe('info');
            expect(logs[0].message).toBe('Test info message');
            expect(consoleSpy).toHaveBeenCalled();

            consoleSpy.mockRestore();
        });

        it('应该根据日志级别过滤日志', () => {
            const warnPlugin = new DevToolsPlugin({ logLevel: 'warn' });
            warnPlugin.install(mockKernel as any);

            (warnPlugin as any).log('debug', 'Debug message');
            (warnPlugin as any).log('info', 'Info message');
            (warnPlugin as any).log('warn', 'Warn message');
            (warnPlugin as any).log('error', 'Error message');

            const logs = warnPlugin.getLogs();
            expect(logs).toHaveLength(2); // 只有 warn 和 error
            expect(logs[0].level).toBe('warn');
            expect(logs[1].level).toBe('error');
        });

        it('应该限制日志数量', () => {
            // 添加大量日志
            for (let i = 0; i < 1100; i++) {
                (plugin as any).log('info', `Message ${i}`);
            }

            const logs = plugin.getLogs();
            expect(logs.length).toBeLessThanOrEqual(500);
        });

        it('应该清空日志', () => {
            (plugin as any).log('info', 'Test message');
            expect(plugin.getLogs()).toHaveLength(1);

            plugin.clearLogs();
            expect(plugin.getLogs()).toHaveLength(0);
        });

        it('应该在禁用日志时不记录', () => {
            const noLogPlugin = new DevToolsPlugin({ enableLogger: false });
            noLogPlugin.install(mockKernel as any);

            (noLogPlugin as any).log('info', 'Test message');

            expect(noLogPlugin.getLogs()).toHaveLength(0);
        });
    });

    describe('性能监控', () => {
        it('应该设置性能监控', () => {
            const mockObserver = vi.fn().mockImplementation((callback) => ({
                observe: vi.fn(),
                disconnect: vi.fn(),
                callback
            }));
            (window as any).PerformanceObserver = mockObserver;

            plugin.install(mockKernel as any);

            expect(mockObserver).toHaveBeenCalled();
            expect((plugin as any).performanceObserver).toBeDefined();
        });

        it('应该在不支持 PerformanceObserver 时记录警告', () => {
            const originalPerformanceObserver = (window as any).PerformanceObserver;
            delete (window as any).PerformanceObserver;

            const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => { });

            plugin.install(mockKernel as any);

            expect(consoleSpy).toHaveBeenCalledWith(
                expect.stringContaining('[Micro-Core DevTools] 浏览器不支持 PerformanceObserver')
            );

            // 恢复
            (window as any).PerformanceObserver = originalPerformanceObserver;
            consoleSpy.mockRestore();
        });

        it('应该处理性能条目', () => {
            const mockCallback = vi.fn();
            const mockObserver = vi.fn().mockImplementation((callback) => ({
                observe: vi.fn(),
                disconnect: vi.fn(),
                callback
            }));
            (window as any).PerformanceObserver = mockObserver;

            plugin.install(mockKernel as any);

            // 模拟性能条目
            const mockEntries = [{
                entryType: 'navigation',
                duration: 1500.5
            }];

            const callback = mockObserver.mock.calls[0][0];
            callback({
                getEntries: () => mockEntries
            });

            const logs = plugin.getLogs();
            expect(logs.some(log => log.message.includes('页面加载时间: 1500.50ms'))).toBe(true);
        });
    });

    describe('事件系统', () => {
        beforeEach(() => {
            plugin.install(mockKernel as any);
        });

        it('应该添加和触发事件监听器', () => {
            const mockListener = vi.fn();

            plugin.on('panelToggle', mockListener);
            (plugin as any).emit('panelToggle', true);

            expect(mockListener).toHaveBeenCalledWith(true);
        });

        it('应该移除事件监听器', () => {
            const mockListener = vi.fn();

            plugin.on('panelToggle', mockListener);
            plugin.off('panelToggle');
            (plugin as any).emit('panelToggle', true);

            expect(mockListener).not.toHaveBeenCalled();
        });

        it('应该处理事件监听器错误', () => {
            const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => { });
            const errorListener = vi.fn().mockImplementation(() => {
                throw new Error('Listener error');
            });

            plugin.on('panelToggle', errorListener);
            (plugin as any).emit('panelToggle', true);

            expect(consoleSpy).toHaveBeenCalledWith(
                expect.stringContaining('DevTools 事件监听器 panelToggle 执行失败:'),
                expect.any(Error)
            );

            consoleSpy.mockRestore();
        });
    });

    describe('静态方法', () => {
        describe('isEnabled', () => {
            it('应该在开发环境中启用', () => {
                vi.stubEnv('NODE_ENV', 'development');
                expect(DevToolsPlugin.isEnabled()).toBe(true);
            });

            it('应该在生产环境中禁用', () => {
                vi.stubEnv('NODE_ENV', 'production');
                expect(DevToolsPlugin.isEnabled()).toBe(false);
            });

            it('应该在 URL 包含 devtools=true 时启用', () => {
                vi.stubEnv('NODE_ENV', 'production');

                // Mock window.location
                Object.defineProperty(window, 'location', {
                    value: { search: '?devtools=true' },
                    writable: true
                });

                expect(DevToolsPlugin.isEnabled()).toBe(true);
            });
        });

        describe('create', () => {
            it('应该创建启用的插件实例', () => {
                vi.stubEnv('NODE_ENV', 'development');

                const plugin = DevToolsPlugin.create();

                expect((plugin as any).config.enabled).toBe(true);
            });

            it('应该使用自定义配置创建插件实例', () => {
                vi.stubEnv('NODE_ENV', 'production');

                const plugin = DevToolsPlugin.create({
                    showPanel: true,
                    logLevel: 'error'
                });

                expect((plugin as any).config.enabled).toBe(false); // 因为是生产环境
                expect((plugin as any).config.showPanel).toBe(true);
                expect((plugin as any).config.logLevel).toBe('error');
            });
        });
    });

    describe('API 方法', () => {
        beforeEach(() => {
            plugin.install(mockKernel as any);
        });

        it('应该返回应用列表', () => {
            // 添加测试应用
            const appConfig = { name: 'test-app' };
            const registeredListener = mockKernel.on.mock.calls.find(
                call => call[0] === 'app:registered'
            )?.[1];
            registeredListener(appConfig);

            const apps = plugin.getApps();
            expect(apps).toHaveLength(1);
            expect(apps[0].name).toBe('test-app');
        });

        it('应该返回日志列表', () => {
            (plugin as any).log('info', 'Test message');

            const logs = plugin.getLogs();
            expect(logs).toHaveLength(1);
            expect(logs[0].message).toBe('Test message');
        });

        it('应该通过内核 API 访问功能', () => {
            const kernelApi = (mockKernel as any).devtools;

            expect(kernelApi.show).toBeInstanceOf(Function);
            expect(kernelApi.hide).toBeInstanceOf(Function);
            expect(kernelApi.toggle).toBeInstanceOf(Function);
            expect(kernelApi.getApps).toBeInstanceOf(Function);
            expect(kernelApi.getLogs).toBeInstanceOf(Function);
            expect(kernelApi.clearLogs).toBeInstanceOf(Function);

            // 测试 API 调用
            kernelApi.show();
            expect((plugin as any).isVisible).toBe(true);

            kernelApi.hide();
            expect((plugin as any).isVisible).toBe(false);
        });
    });

    describe('边界情况和错误处理', () => {
        beforeEach(() => {
            plugin.install(mockKernel as any);
        });

        it('应该处理不存在的应用状态更新', () => {
            const loadingListener = mockKernel.on.mock.calls.find(
                call => call[0] === 'app:loading'
            )?.[1];

            // 尝试更新不存在的应用
            expect(() => {
                loadingListener('non-existent-app');
            }).not.toThrow();

            const apps = plugin.getApps();
            expect(apps).toHaveLength(0);
        });

        it('应该处理面板不存在时的更新操作', () => {
            expect(() => {
                (plugin as any).updatePanel();
            }).not.toThrow();
        });

        it('应该处理大量日志的性能', () => {
            const startTime = performance.now();

            // 添加大量日志
            for (let i = 0; i < 1000; i++) {
                (plugin as any).log('info', `Performance test message ${i}`);
            }

            const endTime = performance.now();
            const duration = endTime - startTime;

            // 应该在合理时间内完成（小于100ms）
            expect(duration).toBeLessThan(100);
            expect(plugin.getLogs().length).toBeLessThanOrEqual(500); // 应该被限制
        });

        it('应该处理循环引用的日志数据', () => {
            const circularData: any = { name: 'test' };
            circularData.self = circularData;

            expect(() => {
                (plugin as any).log('info', 'Circular data test', circularData);
            }).not.toThrow();

            const logs = plugin.getLogs();
            expect(logs).toHaveLength(1);
        });
    });

    describe('内存管理', () => {
        it('应该正确清理资源', () => {
            plugin.install(mockKernel as any);

            // 添加一些数据
            const appConfig = { name: 'test-app' };
            const registeredListener = mockKernel.on.mock.calls.find(
                call => call[0] === 'app:registered'
            )?.[1];
            registeredListener(appConfig);

            (plugin as any).log('info', 'Test message');
            plugin.showPanel();

            // 验证数据存在
            expect(plugin.getApps()).toHaveLength(1);
            expect(plugin.getLogs()).toHaveLength(1);
            expect(document.getElementById('micro-core-devtools')).toBeTruthy();

            // 卸载插件
            plugin.uninstall(mockKernel as any);

            // 验证资源被清理
            expect(plugin.getApps()).toHaveLength(0);
            expect(plugin.getLogs()).toHaveLength(0);
            expect(document.getElementById('micro-core-devtools')).toBeNull();
            expect((plugin as any).kernel).toBeNull();
            expect((plugin as any).panel).toBeNull();
            expect((plugin as any).performanceObserver).toBeNull();
        });

        it('应该处理重复安装', () => {
            plugin.install(mockKernel as any);
            const firstKernel = (plugin as any).kernel;

            // 重复安装
            plugin.install(mockKernel as any);
            const secondKernel = (plugin as any).kernel;

            expect(firstKernel).toBe(secondKernel);
            expect(mockKernel.registerPlugin).toHaveBeenCalledTimes(2);
        });

        it('应该处理重复卸载', () => {
            plugin.install(mockKernel as any);
            plugin.uninstall(mockKernel as any);

            // 重复卸载不应该出错
            expect(() => {
                plugin.uninstall(mockKernel as any);
            }).not.toThrow();
        });
    });
});