/**
 * @fileoverview DevTools 插件基础功能测试
 * @description 测试插件的基本功能和构造函数
 */

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { DevToolsPlugin } from '../../../src/devtools-plugin';
import type { DevToolsConfig } from '../../../src/types';

// Mock DOM 环境
Object.defineProperty(window, 'PerformanceObserver', {
    writable: true,
    value: vi.fn().mockImplementation((callback) => ({
        observe: vi.fn(),
        disconnect: vi.fn(),
        callback
    }))
});

// Mock 内核
const createMockKernel = () => ({
    registerPlugin: vi.fn(),
    on: vi.fn(),
    off: vi.fn(),
    emit: vi.fn()
});

describe('DevToolsPlugin 基础功能', () => {
    let plugin: DevToolsPlugin;
    let mockKernel: ReturnType<typeof createMockKernel>;

    beforeEach(() => {
        // 清理 DOM
        document.body.innerHTML = '';

        // 重置环境变量
        vi.stubEnv('NODE_ENV', 'development');

        // 创建模拟内核
        mockKernel = createMockKernel();

        // 创建插件实例
        plugin = new DevToolsPlugin();
    });

    afterEach(() => {
        vi.clearAllMocks();
        vi.unstubAllEnvs();

        // 清理 DOM
        document.body.innerHTML = '';

        // 移除事件监听器
        document.removeEventListener('keydown', (plugin as any).handleKeydown);
    });

    describe('构造函数', () => {
        it('应该使用默认配置创建插件实例', () => {
            const newPlugin = new DevToolsPlugin();

            expect(newPlugin.name).toBe('devtools');
            expect(newPlugin.version).toBe('0.1.0');
            expect((newPlugin as any).config.enabled).toBe(true);
            expect((newPlugin as any).config.showPanel).toBe(false);
            expect((newPlugin as any).config.panelPosition).toBe('bottom');
        });

        it('应该使用自定义配置创建插件实例', () => {
            const config: DevToolsConfig = {
                enabled: false,
                showPanel: true,
                panelPosition: 'top',
                enablePerformanceMonitor: false,
                logLevel: 'error'
            };

            const newPlugin = new DevToolsPlugin(config);

            expect((newPlugin as any).config.enabled).toBe(false);
            expect((newPlugin as any).config.showPanel).toBe(true);
            expect((newPlugin as any).config.panelPosition).toBe('top');
            expect((newPlugin as any).config.enablePerformanceMonitor).toBe(false);
            expect((newPlugin as any).config.logLevel).toBe('error');
        });

        it('应该合并默认配置和自定义配置', () => {
            const config: DevToolsConfig = {
                showPanel: true,
                logLevel: 'warn'
            };

            const newPlugin = new DevToolsPlugin(config);

            expect((newPlugin as any).config.enabled).toBe(true); // 默认值
            expect((newPlugin as any).config.showPanel).toBe(true); // 自定义值
            expect((newPlugin as any).config.panelPosition).toBe('bottom'); // 默认值
            expect((newPlugin as any).config.logLevel).toBe('warn'); // 自定义值
        });
    });

    describe('静态方法', () => {
        describe('isEnabled', () => {
            it('应该在开发环境中启用', () => {
                vi.stubEnv('NODE_ENV', 'development');
                expect(DevToolsPlugin.isEnabled()).toBe(true);
            });

            it('应该在生产环境中禁用', () => {
                vi.stubEnv('NODE_ENV', 'production');
                expect(DevToolsPlugin.isEnabled()).toBe(false);
            });

            it('应该在 URL 包含 devtools=true 时启用', () => {
                vi.stubEnv('NODE_ENV', 'production');

                // Mock window.location
                Object.defineProperty(window, 'location', {
                    value: { search: '?devtools=true' },
                    writable: true
                });

                expect(DevToolsPlugin.isEnabled()).toBe(true);
            });
        });

        describe('create', () => {
            it('应该创建启用的插件实例', () => {
                vi.stubEnv('NODE_ENV', 'development');

                const plugin = DevToolsPlugin.create();

                expect((plugin as any).config.enabled).toBe(true);
            });

            it('应该使用自定义配置创建插件实例', () => {
                vi.stubEnv('NODE_ENV', 'production');

                const plugin = DevToolsPlugin.create({
                    showPanel: true,
                    logLevel: 'error'
                });

                expect((plugin as any).config.enabled).toBe(false); // 因为是生产环境
                expect((plugin as any).config.showPanel).toBe(true);
                expect((plugin as any).config.logLevel).toBe('error');
            });
        });
    });
});