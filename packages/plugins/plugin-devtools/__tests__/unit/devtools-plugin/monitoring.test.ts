/**
 * @fileoverview DevTools 插件监控功能测试
 * @description 测试插件的性能监控、日志记录等功能
 */

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { DevToolsPlugin } from '../../../src/devtools-plugin';

// Mock Performance Observer
const mockPerformanceObserver = vi.fn().mockImplementation((callback) => ({
    observe: vi.fn(),
    disconnect: vi.fn(),
    callback
}));

Object.defineProperty(window, 'PerformanceObserver', {
    writable: true,
    value: mockPerformanceObserver
});

// Mock 内核
const createMockKernel = () => ({
    registerPlugin: vi.fn(),
    on: vi.fn(),
    off: vi.fn(),
    emit: vi.fn(),
    getApps: vi.fn(() => [
        {
            name: 'test-app-1',
            status: 'mounted',
            loadTime: 150,
            mountTime: 50
        },
        {
            name: 'test-app-2',
            status: 'loading',
            loadTime: 0,
            mountTime: 0
        }
    ]),
    getPlugins: vi.fn(() => [
        { name: 'plugin-1', version: '1.0.0' },
        { name: 'plugin-2', version: '2.0.0' }
    ])
});

describe('DevToolsPlugin 监控功能', () => {
    let plugin: DevToolsPlugin;
    let mockKernel: ReturnType<typeof createMockKernel>;
    let consoleSpy: any;

    beforeEach(() => {
        // 清理 DOM
        document.body.innerHTML = '';

        // 重置环境变量
        vi.stubEnv('NODE_ENV', 'development');

        // 创建模拟内核
        mockKernel = createMockKernel();

        // 监听 console 方法
        consoleSpy = {
            log: vi.spyOn(console, 'log').mockImplementation(() => { }),
            warn: vi.spyOn(console, 'warn').mockImplementation(() => { }),
            error: vi.spyOn(console, 'error').mockImplementation(() => { })
        };

        // 创建插件实例
        plugin = new DevToolsPlugin({
            enabled: true,
            enablePerformanceMonitor: true,
            logLevel: 'info'
        });
    });

    afterEach(() => {
        vi.clearAllMocks();
        vi.unstubAllEnvs();
        consoleSpy.log.mockRestore();
        consoleSpy.warn.mockRestore();
        consoleSpy.error.mockRestore();

        // 清理 DOM
        document.body.innerHTML = '';
    });

    describe('性能监控', () => {
        beforeEach(async () => {
            await plugin.install(mockKernel);
        });

        it('应该启动性能监控', () => {
            expect(mockPerformanceObserver).toHaveBeenCalled();

            const observerInstance = mockPerformanceObserver.mock.results[0].value;
            expect(observerInstance.observe).toHaveBeenCalledWith({
                entryTypes: ['measure', 'navigation', 'resource']
            });
        });

        it('应该记录应用加载性能', () => {
            const performanceEntry = {
                name: 'app-load',
                entryType: 'measure',
                startTime: 100,
                duration: 200
            };

            // 模拟性能条目回调
            const callback = mockPerformanceObserver.mock.calls[0][0];
            callback({
                getEntries: () => [performanceEntry]
            });

            // 验证性能数据是否被记录
            const performanceData = (plugin as any).performanceData;
            expect(performanceData).toContain(performanceEntry);
        });

        it('应该监控内存使用情况', () => {
            // Mock performance.memory
            Object.defineProperty(performance, 'memory', {
                value: {
                    usedJSHeapSize: 1000000,
                    totalJSHeapSize: 2000000,
                    jsHeapSizeLimit: 4000000
                },
                configurable: true
            });

            const memoryInfo = (plugin as any).getMemoryInfo();

            expect(memoryInfo).toEqual({
                used: 1000000,
                total: 2000000,
                limit: 4000000
            });
        });

        it('应该在性能监控禁用时不启动监控', async () => {
            const disabledPlugin = new DevToolsPlugin({
                enabled: true,
                enablePerformanceMonitor: false
            });

            await disabledPlugin.install(mockKernel);

            // 应该没有创建新的 PerformanceObserver
            expect(mockPerformanceObserver).toHaveBeenCalledTimes(1); // 只有之前的调用
        });
    });

    describe('日志记录', () => {
        beforeEach(async () => {
            await plugin.install(mockKernel);
        });

        it('应该记录信息级别日志', () => {
            (plugin as any).log('info', 'Test info message');

            expect(consoleSpy.log).toHaveBeenCalledWith(
                '[MicroCore DevTools]',
                'Test info message'
            );
        });

        it('应该记录警告级别日志', () => {
            (plugin as any).log('warn', 'Test warning message');

            expect(consoleSpy.warn).toHaveBeenCalledWith(
                '[MicroCore DevTools]',
                'Test warning message'
            );
        });

        it('应该记录错误级别日志', () => {
            (plugin as any).log('error', 'Test error message');

            expect(consoleSpy.error).toHaveBeenCalledWith(
                '[MicroCore DevTools]',
                'Test error message'
            );
        });

        it('应该根据日志级别过滤日志', async () => {
            const errorOnlyPlugin = new DevToolsPlugin({
                enabled: true,
                logLevel: 'error'
            });

            await errorOnlyPlugin.install(mockKernel);

            (errorOnlyPlugin as any).log('info', 'Info message');
            (errorOnlyPlugin as any).log('warn', 'Warning message');
            (errorOnlyPlugin as any).log('error', 'Error message');

            expect(consoleSpy.log).not.toHaveBeenCalled();
            expect(consoleSpy.warn).not.toHaveBeenCalled();
            expect(consoleSpy.error).toHaveBeenCalledWith(
                '[MicroCore DevTools]',
                'Error message'
            );
        });

        it('应该存储日志历史', () => {
            (plugin as any).log('info', 'Message 1');
            (plugin as any).log('warn', 'Message 2');
            (plugin as any).log('error', 'Message 3');

            const logHistory = (plugin as any).getLogHistory();

            expect(logHistory).toHaveLength(3);
            expect(logHistory[0]).toMatchObject({
                level: 'info',
                message: 'Message 1',
                timestamp: expect.any(Number)
            });
            expect(logHistory[1]).toMatchObject({
                level: 'warn',
                message: 'Message 2',
                timestamp: expect.any(Number)
            });
            expect(logHistory[2]).toMatchObject({
                level: 'error',
                message: 'Message 3',
                timestamp: expect.any(Number)
            });
        });

        it('应该限制日志历史长度', () => {
            const maxLogs = 100;

            // 添加超过限制的日志
            for (let i = 0; i < maxLogs + 10; i++) {
                (plugin as any).log('info', `Message ${i}`);
            }

            const logHistory = (plugin as any).getLogHistory();
            expect(logHistory).toHaveLength(maxLogs);

            // 验证保留的是最新的日志
            expect(logHistory[logHistory.length - 1].message).toBe(`Message ${maxLogs + 9}`);
        });
    });

    describe('事件监听', () => {
        beforeEach(async () => {
            await plugin.install(mockKernel);
        });

        it('应该监听应用生命周期事件', () => {
            expect(mockKernel.on).toHaveBeenCalledWith('app:loading', expect.any(Function));
            expect(mockKernel.on).toHaveBeenCalledWith('app:loaded', expect.any(Function));
            expect(mockKernel.on).toHaveBeenCalledWith('app:mounting', expect.any(Function));
            expect(mockKernel.on).toHaveBeenCalledWith('app:mounted', expect.any(Function));
            expect(mockKernel.on).toHaveBeenCalledWith('app:unmounting', expect.any(Function));
            expect(mockKernel.on).toHaveBeenCalledWith('app:unmounted', expect.any(Function));
        });

        it('应该监听插件生命周期事件', () => {
            expect(mockKernel.on).toHaveBeenCalledWith('plugin:installed', expect.any(Function));
            expect(mockKernel.on).toHaveBeenCalledWith('plugin:uninstalled', expect.any(Function));
        });

        it('应该监听错误事件', () => {
            expect(mockKernel.on).toHaveBeenCalledWith('error', expect.any(Function));
        });

        it('应该记录应用加载事件', () => {
            const loadingCallback = mockKernel.on.mock.calls.find(
                call => call[0] === 'app:loading'
            )[1];

            const appInfo = { name: 'test-app', url: 'http://localhost:3000' };
            loadingCallback(appInfo);

            const events = (plugin as any).getEvents();
            expect(events).toContainEqual({
                type: 'app:loading',
                data: appInfo,
                timestamp: expect.any(Number)
            });
        });

        it('应该记录错误事件', () => {
            const errorCallback = mockKernel.on.mock.calls.find(
                call => call[0] === 'error'
            )[1];

            const error = new Error('Test error');
            errorCallback(error);

            const events = (plugin as any).getEvents();
            expect(events).toContainEqual({
                type: 'error',
                data: {
                    message: 'Test error',
                    stack: error.stack
                },
                timestamp: expect.any(Number)
            });
        });
    });

    describe('数据收集', () => {
        beforeEach(async () => {
            await plugin.install(mockKernel);
        });

        it('应该收集应用信息', () => {
            const appsInfo = (plugin as any).getAppsInfo();

            expect(appsInfo).toEqual([
                {
                    name: 'test-app-1',
                    status: 'mounted',
                    loadTime: 150,
                    mountTime: 50
                },
                {
                    name: 'test-app-2',
                    status: 'loading',
                    loadTime: 0,
                    mountTime: 0
                }
            ]);
        });

        it('应该收集插件信息', () => {
            const pluginsInfo = (plugin as any).getPluginsInfo();

            expect(pluginsInfo).toEqual([
                { name: 'plugin-1', version: '1.0.0' },
                { name: 'plugin-2', version: '2.0.0' }
            ]);
        });

        it('应该收集系统信息', () => {
            const systemInfo = (plugin as any).getSystemInfo();

            expect(systemInfo).toMatchObject({
                userAgent: expect.any(String),
                platform: expect.any(String),
                language: expect.any(String),
                cookieEnabled: expect.any(Boolean),
                onLine: expect.any(Boolean)
            });
        });

        it('应该收集性能指标', () => {
            // 添加一些性能数据
            (plugin as any).performanceData.push({
                name: 'test-measure',
                duration: 100,
                startTime: 50
            });

            const performanceMetrics = (plugin as any).getPerformanceMetrics();

            expect(performanceMetrics).toMatchObject({
                totalMeasures: 1,
                averageDuration: 100,
                entries: expect.any(Array)
            });
        });
    });

    describe('数据导出', () => {
        beforeEach(async () => {
            await plugin.install(mockKernel);

            // 添加一些测试数据
            (plugin as any).log('info', 'Test log');
            (plugin as any).performanceData.push({
                name: 'test-measure',
                duration: 100
            });
        });

        it('应该能够导出所有数据', () => {
            const exportedData = (plugin as any).exportData();

            expect(exportedData).toMatchObject({
                timestamp: expect.any(Number),
                apps: expect.any(Array),
                plugins: expect.any(Array),
                logs: expect.any(Array),
                events: expect.any(Array),
                performance: expect.any(Array),
                system: expect.any(Object)
            });
        });

        it('应该能够导出 JSON 格式数据', () => {
            const jsonData = (plugin as any).exportAsJSON();

            expect(() => JSON.parse(jsonData)).not.toThrow();

            const parsed = JSON.parse(jsonData);
            expect(parsed).toMatchObject({
                timestamp: expect.any(Number),
                apps: expect.any(Array),
                plugins: expect.any(Array)
            });
        });

        it('应该能够清除历史数据', () => {
            // 验证有数据
            expect((plugin as any).getLogHistory()).toHaveLength(1);
            expect((plugin as any).performanceData).toHaveLength(1);

            (plugin as any).clearData();

            // 验证数据被清除
            expect((plugin as any).getLogHistory()).toHaveLength(0);
            expect((plugin as any).performanceData).toHaveLength(0);
            expect((plugin as any).getEvents()).toHaveLength(0);
        });
    });
});