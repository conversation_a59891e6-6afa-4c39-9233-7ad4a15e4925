/**
 * @fileoverview DevTools 插件集成测试
 * @description 测试插件与内核的集成和完整工作流程
 */

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { DevToolsPlugin } from '../../../src/devtools-plugin';

// Mock DOM 环境
Object.defineProperty(window, 'PerformanceObserver', {
    writable: true,
    value: vi.fn().mockImplementation((callback) => ({
        observe: vi.fn(),
        disconnect: vi.fn(),
        callback
    }))
});

// Mock 内核
const createMockKernel = () => ({
    registerPlugin: vi.fn(),
    on: vi.fn(),
    off: vi.fn(),
    emit: vi.fn(),
    getApps: vi.fn(() => []),
    getPlugins: vi.fn(() => []),
    loadApp: vi.fn(),
    mountApp: vi.fn(),
    unmountApp: vi.fn()
});

describe('DevToolsPlugin 集成测试', () => {
    let plugin: DevToolsPlugin;
    let mockKernel: ReturnType<typeof createMockKernel>;

    beforeEach(() => {
        // 清理 DOM
        document.body.innerHTML = '';

        // 重置环境变量
        vi.stubEnv('NODE_ENV', 'development');

        // 创建模拟内核
        mockKernel = createMockKernel();

        // 创建插件实例
        plugin = new DevToolsPlugin({
            enabled: true,
            showPanel: false,
            enablePerformanceMonitor: true
        });
    });

    afterEach(() => {
        vi.clearAllMocks();
        vi.unstubAllEnvs();

        // 清理 DOM
        document.body.innerHTML = '';

        // 移除事件监听器
        document.removeEventListener('keydown', (plugin as any).handleKeydown);
    });

    describe('插件安装和卸载', () => {
        it('应该成功安装插件', async () => {
            await plugin.install(mockKernel);

            expect(mockKernel.registerPlugin).toHaveBeenCalledWith(plugin);
            expect(document.getElementById('micro-core-devtools')).toBeTruthy();
        });

        it('应该在安装时注册事件监听器', async () => {
            await plugin.install(mockKernel);

            expect(mockKernel.on).toHaveBeenCalledWith('app:loading', expect.any(Function));
            expect(mockKernel.on).toHaveBeenCalledWith('app:loaded', expect.any(Function));
            expect(mockKernel.on).toHaveBeenCalledWith('app:mounted', expect.any(Function));
            expect(mockKernel.on).toHaveBeenCalledWith('error', expect.any(Function));
        });

        it('应该成功卸载插件', async () => {
            await plugin.install(mockKernel);
            await plugin.uninstall();

            expect(mockKernel.off).toHaveBeenCalledWith('app:loading', expect.any(Function));
            expect(mockKernel.off).toHaveBeenCalledWith('app:loaded', expect.any(Function));
            expect(mockKernel.off).toHaveBeenCalledWith('app:mounted', expect.any(Function));
            expect(mockKernel.off).toHaveBeenCalledWith('error', expect.any(Function));

            expect(document.getElementById('micro-core-devtools')).toBeFalsy();
        });

        it('应该在卸载时清理资源', async () => {
            await plugin.install(mockKernel);

            // 添加一些数据
            (plugin as any).log('info', 'Test message');
            expect((plugin as any).getLogHistory()).toHaveLength(1);

            await plugin.uninstall();

            // 验证资源被清理
            expect((plugin as any).getLogHistory()).toHaveLength(0);
        });
    });

    describe('完整工作流程', () => {
        beforeEach(async () => {
            await plugin.install(mockKernel);
        });

        it('应该监控完整的应用生命周期', async () => {
            const appInfo = {
                name: 'test-app',
                url: 'http://localhost:3000',
                container: '#app'
            };

            // 模拟应用加载过程
            const loadingCallback = mockKernel.on.mock.calls.find(
                call => call[0] === 'app:loading'
            )[1];
            const loadedCallback = mockKernel.on.mock.calls.find(
                call => call[0] === 'app:loaded'
            )[1];
            const mountedCallback = mockKernel.on.mock.calls.find(
                call => call[0] === 'app:mounted'
            )[1];

            // 触发事件
            loadingCallback(appInfo);
            loadedCallback({ ...appInfo, loadTime: 150 });
            mountedCallback({ ...appInfo, loadTime: 150, mountTime: 50 });

            // 验证事件被记录
            const events = (plugin as any).getEvents();
            expect(events).toHaveLength(3);
            expect(events[0].type).toBe('app:loading');
            expect(events[1].type).toBe('app:loaded');
            expect(events[2].type).toBe('app:mounted');
        });

        it('应该处理错误事件', async () => {
            const error = new Error('Test application error');

            const errorCallback = mockKernel.on.mock.calls.find(
                call => call[0] === 'error'
            )[1];

            errorCallback(error);

            // 验证错误被记录
            const events = (plugin as any).getEvents();
            const errorEvent = events.find(event => event.type === 'error');

            expect(errorEvent).toBeTruthy();
            expect(errorEvent.data.message).toBe('Test application error');
            expect(errorEvent.data.stack).toBe(error.stack);
        });

        it('应该实时更新面板数据', async () => {
            // 显示面板
            (plugin as any).showPanel();

            // 模拟应用状态变化
            mockKernel.getApps.mockReturnValue([
                {
                    name: 'app-1',
                    status: 'mounted',
                    loadTime: 100,
                    mountTime: 50
                }
            ]);

            // 触发数据更新
            (plugin as any).updatePanelData();

            // 验证面板内容更新
            const appsContent = document.querySelector('.devtools-apps-content');
            expect(appsContent?.textContent).toContain('app-1');
            expect(appsContent?.textContent).toContain('mounted');
        });
    });

    describe('性能监控集成', () => {
        beforeEach(async () => {
            await plugin.install(mockKernel);
        });

        it('应该监控应用加载性能', async () => {
            const performanceEntry = {
                name: 'app-load-test-app',
                entryType: 'measure',
                startTime: 100,
                duration: 200
            };

            // 模拟性能观察器回调
            const PerformanceObserver = window.PerformanceObserver as any;
            const callback = PerformanceObserver.mock.calls[0][0];

            callback({
                getEntries: () => [performanceEntry]
            });

            // 验证性能数据被记录
            const performanceData = (plugin as any).performanceData;
            expect(performanceData).toContain(performanceEntry);

            // 验证性能指标计算
            const metrics = (plugin as any).getPerformanceMetrics();
            expect(metrics.totalMeasures).toBe(1);
            expect(metrics.averageDuration).toBe(200);
        });

        it('应该在面板中显示性能数据', async () => {
            // 添加性能数据
            (plugin as any).performanceData.push({
                name: 'app-load',
                duration: 150,
                startTime: 50
            });

            // 显示面板并切换到性能标签
            (plugin as any).showPanel();
            (plugin as any).switchTab('performance');

            // 验证性能数据显示
            const performanceContent = document.querySelector('.devtools-performance-content');
            expect(performanceContent?.textContent).toContain('150');
        });
    });

    describe('用户交互集成', () => {
        beforeEach(async () => {
            await plugin.install(mockKernel);
        });

        it('应该响应键盘快捷键', async () => {
            // 测试 F12 快捷键
            const f12Event = new KeyboardEvent('keydown', {
                key: 'F12',
                code: 'F12'
            });

            document.dispatchEvent(f12Event);
            expect((plugin as any).isVisible).toBe(true);

            document.dispatchEvent(f12Event);
            expect((plugin as any).isVisible).toBe(false);
        });

        it('应该支持面板拖拽调整大小', async () => {
            (plugin as any).showPanel();

            const resizeHandle = document.querySelector('.devtools-resize-handle') as HTMLElement;
            expect(resizeHandle).toBeTruthy();

            // 模拟拖拽开始
            const mouseDownEvent = new MouseEvent('mousedown', {
                clientY: 100
            });
            resizeHandle.dispatchEvent(mouseDownEvent);

            // 模拟拖拽移动
            const mouseMoveEvent = new MouseEvent('mousemove', {
                clientY: 150
            });
            document.dispatchEvent(mouseMoveEvent);

            // 模拟拖拽结束
            const mouseUpEvent = new MouseEvent('mouseup');
            document.dispatchEvent(mouseUpEvent);

            // 验证面板高度改变
            const panel = document.getElementById('micro-core-devtools');
            expect(panel?.style.height).toBeTruthy();
        });

        it('应该支持标签页切换', async () => {
            (plugin as any).showPanel();

            const tabs = document.querySelectorAll('.devtools-tab') as NodeListOf<HTMLElement>;
            expect(tabs.length).toBeGreaterThan(1);

            // 点击第二个标签页
            if (tabs[1]) {
                tabs[1].click();

                expect(tabs[1].classList.contains('active')).toBe(true);
                expect(tabs[0].classList.contains('active')).toBe(false);

                // 验证内容区域切换
                const contents = document.querySelectorAll('.devtools-tab-content');
                expect(contents[1].classList.contains('active')).toBe(true);
                expect(contents[0].classList.contains('active')).toBe(false);
            }
        });
    });

    describe('数据持久化', () => {
        beforeEach(async () => {
            await plugin.install(mockKernel);
        });

        it('应该保存面板状态到 localStorage', async () => {
            const mockLocalStorage = {
                getItem: vi.fn(),
                setItem: vi.fn(),
                removeItem: vi.fn()
            };

            Object.defineProperty(window, 'localStorage', {
                value: mockLocalStorage
            });

            // 显示面板
            (plugin as any).showPanel();

            // 验证状态被保存
            expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
                'micro-core-devtools-visible',
                'true'
            );
        });

        it('应该从 localStorage 恢复面板状态', async () => {
            const mockLocalStorage = {
                getItem: vi.fn().mockReturnValue('true'),
                setItem: vi.fn(),
                removeItem: vi.fn()
            };

            Object.defineProperty(window, 'localStorage', {
                value: mockLocalStorage
            });

            // 重新创建插件实例
            const newPlugin = new DevToolsPlugin({
                enabled: true
            });

            await newPlugin.install(mockKernel);

            // 验证面板状态被恢复
            expect((newPlugin as any).isVisible).toBe(true);
        });

        it('应该导出和导入配置', async () => {
            // 修改一些配置
            (plugin as any).config.logLevel = 'error';
            (plugin as any).config.panelPosition = 'top';

            // 导出配置
            const exportedConfig = (plugin as any).exportConfig();

            expect(exportedConfig).toMatchObject({
                logLevel: 'error',
                panelPosition: 'top'
            });

            // 创建新插件并导入配置
            const newPlugin = new DevToolsPlugin();
            (newPlugin as any).importConfig(exportedConfig);

            expect((newPlugin as any).config.logLevel).toBe('error');
            expect((newPlugin as any).config.panelPosition).toBe('top');
        });
    });

    describe('错误处理', () => {
        it('应该优雅处理安装失败', async () => {
            const faultyKernel = {
                ...mockKernel,
                registerPlugin: vi.fn().mockRejectedValue(new Error('Registration failed'))
            };

            await expect(plugin.install(faultyKernel)).rejects.toThrow('Registration failed');

            // 验证插件状态未被破坏
            expect((plugin as any).isInstalled).toBe(false);
        });

        it('应该处理 DOM 操作错误', async () => {
            // Mock document.createElement 抛出错误
            const originalCreateElement = document.createElement;
            document.createElement = vi.fn().mockImplementation(() => {
                throw new Error('DOM error');
            });

            try {
                await plugin.install(mockKernel);
            } catch (error) {
                expect(error).toBeInstanceOf(Error);
            }

            // 恢复原始方法
            document.createElement = originalCreateElement;
        });

        it('应该处理性能监控错误', async () => {
            // Mock PerformanceObserver 抛出错误
            Object.defineProperty(window, 'PerformanceObserver', {
                value: vi.fn().mockImplementation(() => {
                    throw new Error('Performance monitoring error');
                })
            });

            // 插件应该仍能正常安装，只是性能监控被禁用
            await expect(plugin.install(mockKernel)).resolves.not.toThrow();
        });
    });

    describe('内存管理', () => {
        beforeEach(async () => {
            await plugin.install(mockKernel);
        });

        it('应该正确清理事件监听器', async () => {
            const removeEventListenerSpy = vi.spyOn(document, 'removeEventListener');

            await plugin.uninstall();

            expect(removeEventListenerSpy).toHaveBeenCalledWith('keydown', expect.any(Function));
        });

        it('应该清理性能观察器', async () => {
            const PerformanceObserver = window.PerformanceObserver as any;
            const observerInstance = PerformanceObserver.mock.results[0].value;

            await plugin.uninstall();

            expect(observerInstance.disconnect).toHaveBeenCalled();
        });

        it('应该清理定时器', async () => {
            const clearIntervalSpy = vi.spyOn(global, 'clearInterval');

            // 启动一些定时器
            (plugin as any).startPeriodicUpdate();

            await plugin.uninstall();

            expect(clearIntervalSpy).toHaveBeenCalled();
        });
    });
});