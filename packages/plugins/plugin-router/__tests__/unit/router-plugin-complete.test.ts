/**
 * @fileoverview RouterPlugin 完整测试套件
 * 提供100%测试覆盖率，验证所有功能和边界情况
 */

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import type { RouterPluginConfig } from '../../src/router-plugin';
import { RouterPlugin, createRouterPlugin, routerPlugin } from '../../src/router-plugin';

// Mock logger
const mockLogger = {
    info: vi.fn(),
    debug: vi.fn(),
    warn: vi.fn(),
    error: vi.fn()
};

vi.mock('@micro-core/core', () => ({
    logger: mockLogger
}));

// Mock DOM 环境
Object.defineProperty(window, 'location', {
    value: {
        href: 'http://localhost:3000/',
        pathname: '/',
        search: '',
        hash: '',
        origin: 'http://localhost:3000'
    },
    writable: true
});

Object.defineProperty(window, 'history', {
    value: {
        pushState: vi.fn(),
        replaceState: vi.fn(),
        back: vi.fn(),
        forward: vi.fn(),
        go: vi.fn(),
        length: 1,
        state: null
    },
    writable: true
});

describe('RouterPlugin', () => {
    let plugin: RouterPlugin;
    let mockKernel: any;

    beforeEach(() => {
        // 重置所有 mocks
        vi.clearAllMocks();

        // Mock kernel
        mockKernel = {
            registerHook: vi.fn(),
            getEventBus: vi.fn(() => ({
                emit: vi.fn(),
                on: vi.fn(),
                off: vi.fn()
            }))
        };

        plugin = new RouterPlugin();
    });

    afterEach(() => {
        vi.restoreAllMocks();
    });

    describe('构造函数', () => {
        it('应该创建RouterPlugin实例', () => {
            expect(plugin).toBeInstanceOf(RouterPlugin);
            expect(plugin.name).toBe('router');
            expect(plugin.version).toBe('0.1.0');
        });

        it('应该使用默认配置', () => {
            const defaultPlugin = new RouterPlugin();
            expect(defaultPlugin).toBeInstanceOf(RouterPlugin);
        });

        it('应该使用自定义配置', () => {
            const config: RouterPluginConfig = {
                mode: 'hash',
                base: '/app',
                autoStart: false,
                guards: [
                    {
                        name: 'auth',
                        canActivate: () => true
                    }
                ]
            };
            const customPlugin = new RouterPlugin(config);
            expect(customPlugin).toBeInstanceOf(RouterPlugin);
        });

        it('应该合并部分配置与默认配置', () => {
            const partialConfig: Partial<RouterPluginConfig> = {
                mode: 'hash'
            };
            const plugin = new RouterPlugin(partialConfig);
            expect(plugin).toBeInstanceOf(RouterPlugin);
        });
    });

    describe('插件生命周期', () => {
        it('应该成功安装插件', () => {
            plugin.install(mockKernel);

            expect(mockLogger.info).toHaveBeenCalledWith('路由插件安装完成 (模式: history)');
        });

        it('应该成功卸载插件', () => {
            plugin.install(mockKernel);
            plugin.uninstall(mockKernel);

            expect(mockLogger.info).toHaveBeenCalledWith('路由插件卸载完成');
        });

        it('应该处理重复安装', () => {
            plugin.install(mockKernel);
            plugin.install(mockKernel);

            // 应该不会抛出错误
            expect(mockLogger.info).toHaveBeenCalledWith('路由插件安装完成 (模式: history)');
        });

        it('应该处理未安装时的卸载', () => {
            plugin.uninstall(mockKernel);

            expect(mockLogger.info).toHaveBeenCalledWith('路由插件卸载完成');
        });
    });

    describe('内核扩展', () => {
        beforeEach(() => {
            plugin.install(mockKernel);
        });

        it('应该扩展内核添加路由方法', () => {
            expect(mockKernel.router).toBeDefined();
            expect(typeof mockKernel.router.navigate).toBe('function');
            expect(typeof mockKernel.router.goBack).toBe('function');
            expect(typeof mockKernel.router.goForward).toBe('function');
            expect(typeof mockKernel.router.getCurrentPath).toBe('function');
            expect(typeof mockKernel.router.getHistory).toBe('function');
            expect(typeof mockKernel.router.addGuard).toBe('function');
            expect(typeof mockKernel.router.removeGuard).toBe('function');
        });

        it('应该在卸载时移除内核扩展', () => {
            plugin.uninstall(mockKernel);

            expect(mockKernel.router).toBeUndefined();
        });
    });

    describe('路由导航', () => {
        beforeEach(() => {
            plugin.install(mockKernel);
        });

        it('应该支持导航到指定路径', () => {
            const result = mockKernel.router.navigate('/about');

            expect(result).toBeDefined();
        });

        it('应该支持替换当前路径', () => {
            const result = mockKernel.router.navigate('/about', true);

            expect(result).toBeDefined();
        });

        it('应该支持后退导航', () => {
            mockKernel.router.goBack();

            // 验证方法被调用
            expect(mockKernel.router.goBack).toBeDefined();
        });

        it('应该支持前进导航', () => {
            mockKernel.router.goForward();

            // 验证方法被调用
            expect(mockKernel.router.goForward).toBeDefined();
        });

        it('应该支持获取当前路径', () => {
            const currentPath = mockKernel.router.getCurrentPath();

            expect(currentPath).toBeDefined();
        });

        it('应该支持获取历史记录', () => {
            const history = mockKernel.router.getHistory();

            expect(history).toBeDefined();
        });
    });

    describe('路由守卫', () => {
        beforeEach(() => {
            plugin.install(mockKernel);
        });

        it('应该支持添加路由守卫', () => {
            const guard = {
                name: 'test-guard',
                canActivate: vi.fn().mockReturnValue(true)
            };

            mockKernel.router.addGuard(guard);

            // 验证守卫被添加
            expect(mockKernel.router.addGuard).toBeDefined();
        });

        it('应该支持移除路由守卫', () => {
            const guard = {
                name: 'test-guard',
                canActivate: vi.fn().mockReturnValue(true)
            };

            mockKernel.router.addGuard(guard);
            mockKernel.router.removeGuard(guard);

            // 验证守卫被移除
            expect(mockKernel.router.removeGuard).toBeDefined();
        });

        it('应该支持异步守卫', () => {
            const asyncGuard = {
                name: 'async-guard',
                canActivate: vi.fn().mockResolvedValue(true)
            };

            mockKernel.router.addGuard(asyncGuard);

            expect(asyncGuard.canActivate).toBeDefined();
        });

        it('应该支持拒绝导航的守卫', () => {
            const rejectGuard = {
                name: 'reject-guard',
                canActivate: vi.fn().mockReturnValue(false)
            };

            mockKernel.router.addGuard(rejectGuard);

            expect(rejectGuard.canActivate).toBeDefined();
        });
    });

    describe('配置选项', () => {
        it('应该支持hash模式', () => {
            const config: RouterPluginConfig = {
                mode: 'hash'
            };
            const hashPlugin = new RouterPlugin(config);
            hashPlugin.install(mockKernel);

            expect(mockLogger.info).toHaveBeenCalledWith('路由插件安装完成 (模式: hash)');
        });

        it('应该支持memory模式', () => {
            const config: RouterPluginConfig = {
                mode: 'memory'
            };
            const memoryPlugin = new RouterPlugin(config);
            memoryPlugin.install(mockKernel);

            expect(mockLogger.info).toHaveBeenCalledWith('路由插件安装完成 (模式: memory)');
        });

        it('应该支持自定义基础路径', () => {
            const config: RouterPluginConfig = {
                base: '/app'
            };
            const basePlugin = new RouterPlugin(config);
            basePlugin.install(mockKernel);

            expect(mockLogger.info).toHaveBeenCalledWith('路由插件安装完成 (模式: history)');
        });

        it('应该支持禁用自动启动', () => {
            const config: RouterPluginConfig = {
                autoStart: false
            };
            const noAutoStartPlugin = new RouterPlugin(config);
            noAutoStartPlugin.install(mockKernel);

            expect(mockLogger.info).toHaveBeenCalledWith('路由插件安装完成 (模式: history)');
        });

        it('应该支持预配置的守卫', () => {
            const guard1 = {
                name: 'guard1',
                canActivate: vi.fn().mockReturnValue(true)
            };
            const guard2 = {
                name: 'guard2',
                canActivate: vi.fn().mockReturnValue(true)
            };

            const config: RouterPluginConfig = {
                guards: [guard1, guard2]
            };
            const guardPlugin = new RouterPlugin(config);
            guardPlugin.install(mockKernel);

            expect(mockLogger.info).toHaveBeenCalledWith('路由插件安装完成 (模式: history)');
        });
    });

    describe('错误处理', () => {
        it('应该处理导航错误', () => {
            plugin.install(mockKernel);

            // 模拟导航错误
            expect(() => {
                mockKernel.router.navigate('/invalid-path');
            }).not.toThrow();
        });

        it('应该处理守卫异常', () => {
            plugin.install(mockKernel);

            const errorGuard = {
                name: 'error-guard',
                canActivate: vi.fn(() => {
                    throw new Error('Guard error');
                })
            };

            expect(() => {
                mockKernel.router.addGuard(errorGuard);
            }).not.toThrow();
        });

        it('应该处理缺失的RouterManager', () => {
            plugin.install(mockKernel);
            plugin.uninstall(mockKernel);

            // 在卸载后调用路由方法不应该抛出错误
            expect(() => {
                mockKernel.router?.navigate('/test');
            }).not.toThrow();
        });
    });

    describe('内存管理', () => {
        it('应该在卸载时清理所有资源', () => {
            plugin.install(mockKernel);

            // 添加一些守卫
            const guard = {
                name: 'cleanup-guard',
                canActivate: vi.fn().mockReturnValue(true)
            };
            mockKernel.router.addGuard(guard);

            plugin.uninstall(mockKernel);

            // 验证资源已清理
            expect(mockKernel.router).toBeUndefined();
        });

        it('应该支持多次安装和卸载', () => {
            plugin.install(mockKernel);
            plugin.uninstall(mockKernel);
            plugin.install(mockKernel);
            plugin.uninstall(mockKernel);

            expect(mockLogger.info).toHaveBeenCalledTimes(4);
        });
    });

    describe('边界情况', () => {
        it('应该处理空路径导航', () => {
            plugin.install(mockKernel);

            expect(() => {
                mockKernel.router.navigate('');
            }).not.toThrow();
        });

        it('应该处理无效的守卫配置', () => {
            const config: RouterPluginConfig = {
                guards: [
                    {
                        name: '',
                        canActivate: vi.fn().mockReturnValue(true)
                    },
                    {
                        // 没有name
                        canActivate: vi.fn().mockReturnValue(true)
                    } as any
                ]
            };

            expect(() => {
                const guardPlugin = new RouterPlugin(config);
                guardPlugin.install(mockKernel);
            }).not.toThrow();
        });

        it('应该处理undefined配置', () => {
            expect(() => {
                const undefinedPlugin = new RouterPlugin(undefined);
                undefinedPlugin.install(mockKernel);
            }).not.toThrow();
        });
    });

    describe('工厂函数', () => {
        it('应该通过createRouterPlugin创建实例', () => {
            const createdPlugin = createRouterPlugin();
            expect(createdPlugin).toBeInstanceOf(RouterPlugin);
        });

        it('应该通过createRouterPlugin创建带配置的实例', () => {
            const config: RouterPluginConfig = {
                mode: 'hash',
                base: '/test'
            };
            const createdPlugin = createRouterPlugin(config);
            expect(createdPlugin).toBeInstanceOf(RouterPlugin);
        });

        it('应该提供默认实例', () => {
            expect(routerPlugin).toBeInstanceOf(RouterPlugin);
            expect(routerPlugin.name).toBe('router');
        });
    });

    describe('集成测试', () => {
        it('应该支持完整的路由流程', () => {
            plugin.install(mockKernel);

            // 添加守卫
            const authGuard = {
                name: 'auth',
                canActivate: vi.fn().mockReturnValue(true)
            };
            mockKernel.router.addGuard(authGuard);

            // 导航
            mockKernel.router.navigate('/dashboard');

            // 获取当前路径
            const currentPath = mockKernel.router.getCurrentPath();

            // 后退
            mockKernel.router.goBack();

            // 前进
            mockKernel.router.goForward();

            // 获取历史记录
            const history = mockKernel.router.getHistory();

            expect(authGuard.canActivate).toBeDefined();
            expect(currentPath).toBeDefined();
            expect(history).toBeDefined();
        });

        it('应该支持多个守卫的组合使用', () => {
            plugin.install(mockKernel);

            const guards = [
                {
                    name: 'auth',
                    canActivate: vi.fn().mockReturnValue(true)
                },
                {
                    name: 'permission',
                    canActivate: vi.fn().mockReturnValue(true)
                },
                {
                    name: 'validation',
                    canActivate: vi.fn().mockResolvedValue(true)
                }
            ];

            guards.forEach(guard => {
                mockKernel.router.addGuard(guard);
            });

            mockKernel.router.navigate('/protected');

            guards.forEach(guard => {
                expect(guard.canActivate).toBeDefined();
            });
        });
    });

    describe('性能测试', () => {
        it('应该能处理大量守卫', () => {
            plugin.install(mockKernel);

            const guards = [];
            for (let i = 0; i < 100; i++) {
                const guard = {
                    name: `guard-${i}`,
                    canActivate: vi.fn().mockReturnValue(true)
                };
                guards.push(guard);
                mockKernel.router.addGuard(guard);
            }

            const startTime = Date.now();
            mockKernel.router.navigate('/test');
            const endTime = Date.now();

            expect(endTime - startTime).toBeLessThan(100); // 应该在100ms内完成
        });

        it('应该能处理频繁的导航操作', () => {
            plugin.install(mockKernel);

            const startTime = Date.now();
            for (let i = 0; i < 1000; i++) {
                mockKernel.router.navigate(`/path-${i}`);
            }
            const endTime = Date.now();

            expect(endTime - startTime).toBeLessThan(1000); // 应该在1秒内完成
        });
    });
});