/**
 * @fileoverview 路由插件单元测试
 * <AUTHOR> <<EMAIL>>
 */

import { beforeEach, describe, expect, it, vi, afterEach } from 'vitest';
import type { MicroCoreKernel, AppInfo } from '@micro-core/core';
import { RouterPlugin } from '../../src/router-plugin';
import type { RouterConfig, RouteInfo } from '../../src/types';

// Mock browser APIs
const mockLocation = {
  pathname: '/',
  search: '',
  hash: '',
  href: 'http://localhost:3000/',
  origin: 'http://localhost:3000',
  protocol: 'http:',
  host: 'localhost:3000',
  hostname: 'localhost',
  port: '3000',
};

const mockHistory = {
  pushState: vi.fn(),
  replaceState: vi.fn(),
  back: vi.fn(),
  forward: vi.fn(),
  go: vi.fn(),
  length: 1,
  state: null,
};

Object.defineProperty(window, 'location', {
  value: mockLocation,
  writable: true,
});

Object.defineProperty(window, 'history', {
  value: mockHistory,
  writable: true,
});

// Mock MicroCore kernel
const mockKernel: Partial<MicroCoreKernel> = {
  on: vi.fn(),
  off: vi.fn(),
  emit: vi.fn(),
  getApp: vi.fn(),
  getAllApps: vi.fn(),
};

describe('RouterPlugin', () => {
  let routerPlugin: RouterPlugin;
  let mockConfig: RouterConfig;

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Reset location
    mockLocation.pathname = '/';
    mockLocation.search = '';
    mockLocation.hash = '';
    
    mockConfig = {
      mode: 'history',
      base: '/',
      enableHashFallback: true,
      enableGuards: true,
      enableTransition: true,
      transitionDuration: 300,
      maxHistoryLength: 50,
      enableDebug: false,
    };
    
    routerPlugin = new RouterPlugin(mockConfig);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Plugin Initialization', () => {
    it('should initialize with default configuration', () => {
      const defaultPlugin = new RouterPlugin();
      expect(defaultPlugin.name).toBe('router');
      expect(defaultPlugin.version).toBe('1.0.0');
    });

    it('should merge custom configuration with defaults', () => {
      const customConfig: Partial<RouterConfig> = {
        mode: 'hash',
        base: '/app',
        enableDebug: true,
      };
      const plugin = new RouterPlugin(customConfig);
      expect(plugin).toBeDefined();
    });

    it('should install plugin successfully', async () => {
      await expect(routerPlugin.install(mockKernel as MicroCoreKernel)).resolves.not.toThrow();
      expect(mockKernel.on).toHaveBeenCalledWith('app:beforeMount', expect.any(Function));
      expect(mockKernel.on).toHaveBeenCalledWith('app:mounted', expect.any(Function));
      expect(mockKernel.on).toHaveBeenCalledWith('app:beforeUnmount', expect.any(Function));
    });
  });

  describe('Route Registration and Management', () => {
    beforeEach(async () => {
      await routerPlugin.install(mockKernel as MicroCoreKernel);
    });

    it('should register routes correctly', () => {
      const routeInfo: RouteInfo = {
        path: '/users/:id',
        appName: 'user-app',
        component: 'UserDetail',
        meta: {
          requiresAuth: true,
          title: 'User Detail',
        },
      };

      routerPlugin.registerRoute(routeInfo);
      
      const registeredRoutes = routerPlugin.getRoutes();
      expect(registeredRoutes).toContainEqual(
        expect.objectContaining({
          path: '/users/:id',
          appName: 'user-app',
        })
      );
    });

    it('should handle route parameters correctly', () => {
      const routeInfo: RouteInfo = {
        path: '/users/:id/posts/:postId',
        appName: 'user-app',
        component: 'UserPost',
      };

      routerPlugin.registerRoute(routeInfo);
      
      const match = routerPlugin.matchRoute('/users/123/posts/456');
      expect(match).toEqual(
        expect.objectContaining({
          route: expect.objectContaining({ path: '/users/:id/posts/:postId' }),
          params: { id: '123', postId: '456' },
        })
      );
    });

    it('should handle query parameters correctly', () => {
      const routeInfo: RouteInfo = {
        path: '/search',
        appName: 'search-app',
        component: 'Search',
      };

      routerPlugin.registerRoute(routeInfo);
      
      mockLocation.pathname = '/search';
      mockLocation.search = '?q=test&category=books';
      
      const match = routerPlugin.matchRoute('/search?q=test&category=books');
      expect(match).toEqual(
        expect.objectContaining({
          route: expect.objectContaining({ path: '/search' }),
          query: { q: 'test', category: 'books' },
        })
      );
    });

    it('should unregister routes correctly', () => {
      const routeInfo: RouteInfo = {
        path: '/temp',
        appName: 'temp-app',
        component: 'Temp',
      };

      routerPlugin.registerRoute(routeInfo);
      expect(routerPlugin.getRoutes()).toHaveLength(1);
      
      routerPlugin.unregisterRoute('/temp');
      expect(routerPlugin.getRoutes()).toHaveLength(0);
    });

    it('should handle wildcard routes', () => {
      const wildcardRoute: RouteInfo = {
        path: '/admin/*',
        appName: 'admin-app',
        component: 'AdminLayout',
      };

      routerPlugin.registerRoute(wildcardRoute);
      
      const match1 = routerPlugin.matchRoute('/admin/users');
      const match2 = routerPlugin.matchRoute('/admin/settings/profile');
      
      expect(match1?.route.path).toBe('/admin/*');
      expect(match2?.route.path).toBe('/admin/*');
    });
  });

  describe('Navigation Functionality', () => {
    beforeEach(async () => {
      await routerPlugin.install(mockKernel as MicroCoreKernel);
      
      // Register test routes
      routerPlugin.registerRoute({
        path: '/home',
        appName: 'home-app',
        component: 'Home',
      });
      
      routerPlugin.registerRoute({
        path: '/about',
        appName: 'about-app',
        component: 'About',
      });
    });

    it('should navigate to routes correctly', async () => {
      await routerPlugin.push('/about');
      
      expect(mockHistory.pushState).toHaveBeenCalledWith(
        expect.any(Object),
        '',
        '/about'
      );
      
      expect(mockKernel.emit).toHaveBeenCalledWith('router:navigated', {
        from: '/',
        to: '/about',
        type: 'push',
      });
    });

    it('should replace routes correctly', async () => {
      await routerPlugin.replace('/home');
      
      expect(mockHistory.replaceState).toHaveBeenCalledWith(
        expect.any(Object),
        '',
        '/home'
      );
      
      expect(mockKernel.emit).toHaveBeenCalledWith('router:navigated', {
        from: '/',
        to: '/home',
        type: 'replace',
      });
    });

    it('should handle browser back/forward navigation', async () => {
      // Simulate popstate event
      const popstateEvent = new PopStateEvent('popstate', {
        state: { path: '/about' },
      });
      
      window.dispatchEvent(popstateEvent);
      
      // Should emit navigation event
      expect(mockKernel.emit).toHaveBeenCalledWith('router:navigated', {
        from: expect.any(String),
        to: expect.any(String),
        type: 'pop',
      });
    });

    it('should handle navigation with parameters', async () => {
      routerPlugin.registerRoute({
        path: '/users/:id',
        appName: 'user-app',
        component: 'UserDetail',
      });
      
      await routerPlugin.push('/users/123');
      
      expect(mockHistory.pushState).toHaveBeenCalledWith(
        expect.objectContaining({
          params: { id: '123' },
        }),
        '',
        '/users/123'
      );
    });

    it('should handle navigation with query parameters', async () => {
      await routerPlugin.push('/search', { q: 'test', page: '1' });
      
      expect(mockHistory.pushState).toHaveBeenCalledWith(
        expect.any(Object),
        '',
        '/search?q=test&page=1'
      );
    });
  });

  describe('Route Guards', () => {
    beforeEach(async () => {
      await routerPlugin.install(mockKernel as MicroCoreKernel);
    });

    it('should execute before guards correctly', async () => {
      const beforeGuard = vi.fn().mockResolvedValue(true);
      
      routerPlugin.addBeforeGuard(beforeGuard);
      
      await routerPlugin.push('/test');
      
      expect(beforeGuard).toHaveBeenCalledWith(
        expect.objectContaining({ path: '/test' }),
        expect.objectContaining({ path: '/' })
      );
    });

    it('should block navigation when guard returns false', async () => {
      const blockingGuard = vi.fn().mockResolvedValue(false);
      
      routerPlugin.addBeforeGuard(blockingGuard);
      
      await routerPlugin.push('/blocked');
      
      expect(blockingGuard).toHaveBeenCalled();
      expect(mockHistory.pushState).not.toHaveBeenCalled();
    });

    it('should redirect when guard returns string', async () => {
      const redirectGuard = vi.fn().mockResolvedValue('/login');
      
      routerPlugin.addBeforeGuard(redirectGuard);
      
      await routerPlugin.push('/protected');
      
      expect(redirectGuard).toHaveBeenCalled();
      expect(mockHistory.pushState).toHaveBeenCalledWith(
        expect.any(Object),
        '',
        '/login'
      );
    });

    it('should execute after guards correctly', async () => {
      const afterGuard = vi.fn();
      
      routerPlugin.addAfterGuard(afterGuard);
      
      await routerPlugin.push('/test');
      
      expect(afterGuard).toHaveBeenCalledWith(
        expect.objectContaining({ path: '/test' }),
        expect.objectContaining({ path: '/' })
      );
    });

    it('should remove guards correctly', () => {
      const guard = vi.fn().mockResolvedValue(true);
      
      const removeGuard = routerPlugin.addBeforeGuard(guard);
      removeGuard();
      
      // Guard should not be called after removal
      routerPlugin.push('/test');
      expect(guard).not.toHaveBeenCalled();
    });
  });

  describe('Hash Mode Support', () => {
    beforeEach(async () => {
      const hashConfig: RouterConfig = {
        ...mockConfig,
        mode: 'hash',
      };
      
      routerPlugin = new RouterPlugin(hashConfig);
      await routerPlugin.install(mockKernel as MicroCoreKernel);
    });

    it('should handle hash navigation correctly', async () => {
      await routerPlugin.push('/about');
      
      expect(mockLocation.hash).toBe('#/about');
    });

    it('should parse hash routes correctly', () => {
      mockLocation.hash = '#/users/123';
      
      routerPlugin.registerRoute({
        path: '/users/:id',
        appName: 'user-app',
        component: 'UserDetail',
      });
      
      const match = routerPlugin.matchRoute('#/users/123');
      expect(match?.params).toEqual({ id: '123' });
    });
  });

  describe('App Integration', () => {
    beforeEach(async () => {
      await routerPlugin.install(mockKernel as MicroCoreKernel);
    });

    it('should handle app mounting with route matching', () => {
      const mockAppInfo: AppInfo = {
        name: 'test-app',
        entry: 'http://localhost:3000',
        container: '#app-container',
        activeWhen: '/test',
      };

      // Register route for the app
      routerPlugin.registerRoute({
        path: '/test',
        appName: 'test-app',
        component: 'TestComponent',
      });

      // Simulate app mounting
      const mountHandler = (mockKernel.on as any).mock.calls.find(
        call => call[0] === 'app:beforeMount'
      )[1];
      
      mockLocation.pathname = '/test';
      const result = mountHandler(mockAppInfo);
      
      expect(result).toBe(true);
    });

    it('should prevent app mounting for non-matching routes', () => {
      const mockAppInfo: AppInfo = {
        name: 'test-app',
        entry: 'http://localhost:3000',
        container: '#app-container',
        activeWhen: '/test',
      };

      // Don't register route for the app
      
      // Simulate app mounting
      const mountHandler = (mockKernel.on as any).mock.calls.find(
        call => call[0] === 'app:beforeMount'
      )[1];
      
      mockLocation.pathname = '/other';
      const result = mountHandler(mockAppInfo);
      
      expect(result).toBe(false);
    });

    it('should clean up app routes on unmount', () => {
      const mockAppInfo: AppInfo = {
        name: 'test-app',
        entry: 'http://localhost:3000',
        container: '#app-container',
        activeWhen: '/test',
      };

      // Register routes for the app
      routerPlugin.registerRoute({
        path: '/test',
        appName: 'test-app',
        component: 'TestComponent',
      });
      
      routerPlugin.registerRoute({
        path: '/test/detail',
        appName: 'test-app',
        component: 'TestDetail',
      });

      expect(routerPlugin.getRoutes()).toHaveLength(2);

      // Simulate app unmounting
      const unmountHandler = (mockKernel.on as any).mock.calls.find(
        call => call[0] === 'app:beforeUnmount'
      )[1];
      
      unmountHandler(mockAppInfo);
      
      // App routes should be cleaned up
      expect(routerPlugin.getRoutes().filter(r => r.appName === 'test-app')).toHaveLength(0);
    });
  });

  describe('Error Handling and Edge Cases', () => {
    beforeEach(async () => {
      await routerPlugin.install(mockKernel as MicroCoreKernel);
    });

    it('should handle invalid route patterns gracefully', () => {
      const invalidRoute: RouteInfo = {
        path: '[invalid-pattern',
        appName: 'test-app',
        component: 'Test',
      };

      expect(() => routerPlugin.registerRoute(invalidRoute)).not.toThrow();
    });

    it('should handle navigation to non-existent routes', async () => {
      await routerPlugin.push('/non-existent');
      
      // Should still navigate but emit appropriate events
      expect(mockKernel.emit).toHaveBeenCalledWith('router:notFound', {
        path: '/non-existent',
      });
    });

    it('should handle guard errors gracefully', async () => {
      const errorGuard = vi.fn().mockRejectedValue(new Error('Guard error'));
      
      routerPlugin.addBeforeGuard(errorGuard);
      
      // Should not throw and should prevent navigation
      await expect(routerPlugin.push('/test')).resolves.not.toThrow();
      expect(mockHistory.pushState).not.toHaveBeenCalled();
    });

    it('should handle browser API unavailability', () => {
      // Mock missing history API
      const originalHistory = window.history;
      delete (window as any).history;

      expect(() => {
        const plugin = new RouterPlugin({ mode: 'history' });
        plugin.push('/test');
      }).not.toThrow();

      window.history = originalHistory;
    });

    it('should handle circular redirects', async () => {
      const redirectGuard1 = vi.fn().mockResolvedValue('/route2');
      const redirectGuard2 = vi.fn().mockResolvedValue('/route1');
      
      routerPlugin.addBeforeGuard((to, from) => {
        if (to.path === '/route1') return redirectGuard1();
        if (to.path === '/route2') return redirectGuard2();
        return true;
      });
      
      // Should detect and prevent circular redirects
      await routerPlugin.push('/route1');
      
      expect(mockKernel.emit).toHaveBeenCalledWith('router:error', {
        type: 'circular-redirect',
        path: '/route1',
      });
    });
  });

  describe('Performance and Memory Management', () => {
    beforeEach(async () => {
      await routerPlugin.install(mockKernel as MicroCoreKernel);
    });

    it('should handle large numbers of routes efficiently', () => {
      const startTime = performance.now();
      
      // Register many routes
      for (let i = 0; i < 1000; i++) {
        routerPlugin.registerRoute({
          path: `/route${i}`,
          appName: `app${i}`,
          component: `Component${i}`,
        });
      }
      
      // Test route matching performance
      const match = routerPlugin.matchRoute('/route500');
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      expect(duration).toBeLessThan(100);
      expect(match?.route.path).toBe('/route500');
    });

    it('should limit history length correctly', async () => {
      const limitedConfig: RouterConfig = {
        ...mockConfig,
        maxHistoryLength: 3,
      };
      
      const limitedPlugin = new RouterPlugin(limitedConfig);
      await limitedPlugin.install(mockKernel as MicroCoreKernel);
      
      // Navigate multiple times
      await limitedPlugin.push('/route1');
      await limitedPlugin.push('/route2');
      await limitedPlugin.push('/route3');
      await limitedPlugin.push('/route4');
      
      const history = limitedPlugin.getHistory();
      expect(history.length).toBeLessThanOrEqual(3);
    });

    it('should clean up event listeners on uninstall', async () => {
      const originalAddEventListener = window.addEventListener;
      const originalRemoveEventListener = window.removeEventListener;
      
      window.addEventListener = vi.fn();
      window.removeEventListener = vi.fn();
      
      await routerPlugin.install(mockKernel as MicroCoreKernel);
      await routerPlugin.uninstall();
      
      expect(window.removeEventListener).toHaveBeenCalledWith('popstate', expect.any(Function));
      
      window.addEventListener = originalAddEventListener;
      window.removeEventListener = originalRemoveEventListener;
    });
  });

  describe('Debug and Development Features', () => {
    it('should provide debug information when enabled', async () => {
      const debugConfig: RouterConfig = {
        ...mockConfig,
        enableDebug: true,
      };
      
      const debugPlugin = new RouterPlugin(debugConfig);
      
      const originalLog = console.log;
      console.log = vi.fn();
      
      await debugPlugin.install(mockKernel as MicroCoreKernel);
      await debugPlugin.push('/test');
      
      expect(console.log).toHaveBeenCalled();
      
      console.log = originalLog;
    });

    it('should provide router statistics', () => {
      // Register routes and perform navigation
      routerPlugin.registerRoute({
        path: '/route1',
        appName: 'app1',
        component: 'Component1',
      });
      
      routerPlugin.push('/route1');
      
      const stats = routerPlugin.getStatistics();
      
      expect(stats).toEqual(
        expect.objectContaining({
          totalRoutes: expect.any(Number),
          activeRoute: expect.any(String),
          navigationCount: expect.any(Number),
          guardCount: expect.any(Number),
        })
      );
    });
  });
});
