/**
 * @fileoverview 增强路由管理器类型定义
 * @description 定义路由相关的接口和类型
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

/**
 * 路由配置接口
 */
export interface EnhancedRouteConfig {
    /** 路由路径 */
    path: string;
    /** 应用名称 */
    app: string;
    /** 路由名称 */
    name?: string;
    /** 路由参数 */
    params?: Record<string, any>;
    /** 路由元数据 */
    meta?: Record<string, any>;
    /** 路由优先级 */
    priority?: number;
    /** 是否精确匹配 */
    exact?: boolean;
    /** 路由守卫 */
    guards?: RouteGuard[];
    /** 路由缓存设置 */
    cache?: {
        enabled: boolean;
        ttl?: number;
    };
}

/**
 * 路由匹配结果
 */
export interface RouteMatch {
    /** 匹配的路由配置 */
    route: EnhancedRouteConfig;
    /** 提取的参数 */
    params: Record<string, string>;
    /** 查询参数 */
    query: Record<string, string>;
    /** 匹配分数（越高越好） */
    score: number;
    /** 匹配路径 */
    path: string;
}

/**
 * 路由守卫函数
 */
export type RouteGuard = (
    to: RouteMatch,
    from: RouteMatch | null
) => boolean | Promise<boolean> | string | Promise<string>;

/**
 * 路由树节点（用于高效匹配）
 */
export interface RouteTreeNode {
    /** 路径段 */
    segment: string;
    /** 是否为参数节点 */
    isParam: boolean;
    /** 参数名称 */
    paramName?: string;
    /** 路由配置（叶子节点） */
    route?: EnhancedRouteConfig;
    /** 子节点 */
    children: Map<string, RouteTreeNode>;
    /** 通配符子节点 */
    wildcardChild?: RouteTreeNode;
}

/**
 * 路由缓存项
 */
export interface RouteCacheItem {
    /** 匹配结果 */
    match: RouteMatch | null;
    /** 缓存时间戳 */
    timestamp: number;
    /** 访问次数 */
    accessCount: number;
}

/**
 * 路由器性能统计
 */
export interface RouterPerformanceStats {
    /** 总匹配次数 */
    totalMatches: number;
    /** 缓存命中次数 */
    cacheHits: number;
    /** 平均匹配时间（毫秒） */
    avgMatchTime: number;
    /** 最慢匹配时间（毫秒） */
    slowestMatchTime: number;
    /** 总导航次数 */
    totalNavigations: number;
    /** 失败导航次数 */
    failedNavigations: number;
}

/**
 * 增强路由器选项
 */
export interface EnhancedRouterOptions {
    /** 路由器模式 */
    mode?: 'hash' | 'history' | 'memory';
    /** 基础路径 */
    base?: string;
    /** 启用路由守卫 */
    enableGuards?: boolean;
    /** 启用路由缓存 */
    enableCache?: boolean;
    /** 缓存TTL（毫秒） */
    cacheTTL?: number;
    /** 最大缓存大小 */
    maxCacheSize?: number;
    /** 启用性能跟踪 */
    enablePerformanceTracking?: boolean;
}

/**
 * 导航事件数据
 */
export interface NavigationEventData {
    /** 目标路径 */
    path: string;
    /** 匹配结果 */
    match?: RouteMatch;
    /** 前一个路由 */
    previous?: RouteMatch | null;
    /** 失败原因 */
    reason?: string;
    /** 错误信息 */
    error?: Error;
}

/**
 * 路由变更事件数据
 */
export interface RouteChangeEventData {
    /** 来源路由 */
    from: RouteMatch | null;
    /** 目标路由 */
    to: RouteMatch;
}

/**
 * 段解析结果
 */
export interface SegmentParseResult {
    /** 键值 */
    key: string;
    /** 是否为参数 */
    isParam: boolean;
    /** 参数名称 */
    paramName?: string;
}