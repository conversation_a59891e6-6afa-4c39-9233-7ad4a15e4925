/**
 * @fileoverview 路由缓存管理器
 * @description 提供路由匹配结果的缓存功能，提高路由匹配性能
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

import type { RouteCacheItem, RouteMatch } from './types';

/**
 * 路由缓存管理器
 * 负责缓存路由匹配结果，提高重复路由匹配的性能
 */
export class RouteCacheManager {
    private cache = new Map<string, RouteCacheItem>();
    private cacheTTL: number;
    private maxCacheSize: number;
    private cleanupInterval: NodeJS.Timeout | null = null;

    constructor(cacheTTL: number = 300000, maxCacheSize: number = 1000) {
        this.cacheTTL = cacheTTL; // 5分钟默认TTL
        this.maxCacheSize = maxCacheSize;
        this.startCleanup();
    }

    /**
     * 从缓存获取匹配结果
     */
    get(path: string): RouteMatch | null {
        const item = this.cache.get(path);
        if (!item) return null;

        // 检查缓存项是否过期
        if (Date.now() - item.timestamp > this.cacheTTL) {
            this.cache.delete(path);
            return null;
        }

        item.accessCount++;
        return item.match;
    }

    /**
     * 添加匹配结果到缓存
     */
    set(path: string, match: RouteMatch | null): void {
        // 检查缓存大小限制
        if (this.cache.size >= this.maxCacheSize) {
            this.evictLeastRecentlyUsed();
        }

        this.cache.set(path, {
            match,
            timestamp: Date.now(),
            accessCount: 1
        });
    }

    /**
     * 清空缓存
     */
    clear(): void {
        this.cache.clear();
    }

    /**
     * 获取缓存统计信息
     */
    getStats(): {
        size: number;
        maxSize: number;
        hitRate: number;
        totalAccess: number;
    } {
        let totalAccess = 0;
        for (const item of this.cache.values()) {
            totalAccess += item.accessCount;
        }

        return {
            size: this.cache.size,
            maxSize: this.maxCacheSize,
            hitRate: totalAccess > 0 ? (this.cache.size / totalAccess) * 100 : 0,
            totalAccess
        };
    }

    /**
     * 移除最少使用的缓存项
     */
    private evictLeastRecentlyUsed(): void {
        let oldestKey = '';
        let oldestTime = Date.now();
        let leastAccessCount = Infinity;

        for (const [key, item] of this.cache) {
            // 优先移除访问次数最少的项
            if (item.accessCount < leastAccessCount) {
                leastAccessCount = item.accessCount;
                oldestKey = key;
                oldestTime = item.timestamp;
            } else if (item.accessCount === leastAccessCount && item.timestamp < oldestTime) {
                // 如果访问次数相同，移除最旧的项
                oldestTime = item.timestamp;
                oldestKey = key;
            }
        }

        if (oldestKey) {
            this.cache.delete(oldestKey);
        }
    }

    /**
     * 启动缓存清理
     */
    private startCleanup(): void {
        this.cleanupInterval = setInterval(() => {
            this.cleanupExpired();
        }, 60000); // 每分钟清理一次
    }

    /**
     * 清理过期的缓存项
     */
    private cleanupExpired(): void {
        const now = Date.now();
        const expiredKeys: string[] = [];

        for (const [path, item] of this.cache) {
            if (now - item.timestamp > this.cacheTTL) {
                expiredKeys.push(path);
            }
        }

        for (const key of expiredKeys) {
            this.cache.delete(key);
        }

        if (expiredKeys.length > 0) {
            console.log(`[RouteCacheManager] 清理了 ${expiredKeys.length} 个过期缓存项`);
        }
    }

    /**
     * 停止缓存清理
     */
    stopCleanup(): void {
        if (this.cleanupInterval) {
            clearInterval(this.cleanupInterval);
            this.cleanupInterval = null;
        }
    }

    /**
     * 销毁缓存管理器
     */
    destroy(): void {
        this.stopCleanup();
        this.clear();
    }

    /**
     * 获取缓存中的所有路径
     */
    getCachedPaths(): string[] {
        return Array.from(this.cache.keys());
    }

    /**
     * 检查路径是否被缓存
     */
    has(path: string): boolean {
        const item = this.cache.get(path);
        if (!item) return false;

        // 检查是否过期
        if (Date.now() - item.timestamp > this.cacheTTL) {
            this.cache.delete(path);
            return false;
        }

        return true;
    }

    /**
     * 更新缓存配置
     */
    updateConfig(cacheTTL?: number, maxCacheSize?: number): void {
        if (cacheTTL !== undefined) {
            this.cacheTTL = cacheTTL;
        }

        if (maxCacheSize !== undefined) {
            this.maxCacheSize = maxCacheSize;
            // 如果新的最大大小小于当前大小，需要清理
            while (this.cache.size > this.maxCacheSize) {
                this.evictLeastRecentlyUsed();
            }
        }
    }

    /**
     * 预热缓存（批量添加路径）
     */
    warmup(paths: string[], matchFunction: (path: string) => RouteMatch | null): void {
        for (const path of paths) {
            if (!this.has(path)) {
                const match = matchFunction(path);
                this.set(path, match);
            }
        }
    }
}