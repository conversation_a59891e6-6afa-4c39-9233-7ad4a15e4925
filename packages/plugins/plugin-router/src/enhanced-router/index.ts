/**
 * @fileoverview 增强路由管理器主文件
 * @description 整合所有路由功能模块，提供统一的路由管理接口
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

import { EventEmitter } from 'events';
import { PerformanceTracker } from './performance-tracker';
import { RouteCacheManager } from './route-cache';
import { RouteGuardManager } from './route-guards';
import { RouteTreeManager } from './route-tree';
import type {
    EnhancedRouteConfig,
    EnhancedRouterOptions,
    NavigationEventData,
    RouteChangeEventData,
    RouteMatch,
    RouterPerformanceStats
} from './types';

/**
 * 增强路由管理器
 * 提供高级路由功能，包括基于树的匹配、缓存和守卫
 */
export class EnhancedRouterManager extends EventEmitter {
    private routes = new Map<string, EnhancedRouteConfig>();
    private routeTree: RouteTreeManager;
    private routeCache: RouteCacheManager;
    private guardManager: RouteGuardManager;
    private performanceTracker: PerformanceTracker;
    private currentRoute: RouteMatch | null = null;
    private options: Required<EnhancedRouterOptions>;
    private isStarted = false;

    constructor(options: EnhancedRouterOptions = {}) {
        super();

        this.options = {
            mode: 'history',
            base: '',
            enableGuards: true,
            enableCache: true,
            cacheTTL: 300000, // 5分钟
            maxCacheSize: 1000,
            enablePerformanceTracking: true,
            ...options
        };

        this.routeTree = new RouteTreeManager();
        this.routeCache = new RouteCacheManager(this.options.cacheTTL, this.options.maxCacheSize);
        this.guardManager = new RouteGuardManager();
        this.performanceTracker = new PerformanceTracker();
    }

    /**
     * 启动路由器
     */
    start(): void {
        if (this.isStarted) {
            return;
        }

        this.isStarted = true;

        // 监听路由变化
        if (this.options.mode === 'hash') {
            window.addEventListener('hashchange', this.handleRouteChange.bind(this));
        } else if (this.options.mode === 'history') {
            window.addEventListener('popstate', this.handleRouteChange.bind(this));
        }

        // 处理初始路由
        this.handleRouteChange();

        this.emit('router:started');
        console.log('[EnhancedRouterManager] 路由器已启动');
    }

    /**
     * 停止路由器
     */
    stop(): void {
        if (!this.isStarted) {
            return;
        }

        this.isStarted = false;

        // 移除事件监听器
        if (this.options.mode === 'hash') {
            window.removeEventListener('hashchange', this.handleRouteChange.bind(this));
        } else if (this.options.mode === 'history') {
            window.removeEventListener('popstate', this.handleRouteChange.bind(this));
        }

        this.emit('router:stopped');
        console.log('[EnhancedRouterManager] 路由器已停止');
    }

    /**
     * 注册路由
     */
    registerRoute(config: EnhancedRouteConfig): void {
        // 验证路由配置
        if (!config.path || !config.app) {
            throw new Error('路由必须包含 path 和 app 属性');
        }

        // 设置默认值
        const route: EnhancedRouteConfig = {
            priority: 0,
            exact: false,
            guards: [],
            cache: { enabled: true },
            ...config
        };

        this.routes.set(config.path, route);
        this.routeTree.addRoute(route);

        // 路由变化时清空缓存
        if (this.options.enableCache) {
            this.routeCache.clear();
        }

        this.emit('route:registered', route);
        console.log(`[EnhancedRouterManager] 路由已注册: ${route.path} -> ${route.app}`);
    }

    /**
     * 取消注册路由
     */
    unregisterRoute(path: string): void {
        const route = this.routes.get(path);
        if (route) {
            this.routes.delete(path);
            this.routeTree.removeRoute(route);

            // 路由变化时清空缓存
            if (this.options.enableCache) {
                this.routeCache.clear();
            }

            this.emit('route:unregistered', route);
            console.log(`[EnhancedRouterManager] 路由已取消注册: ${path}`);
        }
    }

    /**
     * 导航到指定路径
     */
    async navigate(path: string, replace = false): Promise<boolean> {
        const startTime = Date.now();

        try {
            // 匹配路由
            const match = await this.matchRoute(path);

            if (!match) {
                this.performanceTracker.recordNavigationFailure();
                const eventData: NavigationEventData = {
                    path,
                    reason: '没有匹配的路由'
                };
                this.emit('navigation:failed', eventData);
                return false;
            }

            // 运行路由守卫
            if (this.options.enableGuards) {
                const guardResult = await this.guardManager.runGuards(match, this.currentRoute);
                if (guardResult !== true) {
                    this.performanceTracker.recordNavigationFailure();
                    const eventData: NavigationEventData = {
                        path,
                        match,
                        reason: guardResult
                    };
                    this.emit('navigation:blocked', eventData);
                    return false;
                }
            }

            // 更新浏览器历史
            const fullPath = this.getFullPath(path);
            if (this.options.mode === 'hash') {
                if (replace) {
                    window.location.replace(`#${fullPath}`);
                } else {
                    window.location.hash = fullPath;
                }
            } else if (this.options.mode === 'history') {
                if (replace) {
                    window.history.replaceState(null, '', fullPath);
                } else {
                    window.history.pushState(null, '', fullPath);
                }
            }

            // 更新当前路由
            const previousRoute = this.currentRoute;
            this.currentRoute = match;

            // 发出导航事件
            const successEventData: NavigationEventData = {
                path,
                match,
                previous: previousRoute
            };
            this.emit('navigation:success', successEventData);

            const changeEventData: RouteChangeEventData = {
                from: previousRoute,
                to: match
            };
            this.emit('route:changed', changeEventData);

            if (this.options.enablePerformanceTracking) {
                const navigationTime = Date.now() - startTime;
                this.performanceTracker.recordNavigationTime(navigationTime);
                this.performanceTracker.recordNavigationSuccess();
                console.log(`[EnhancedRouterManager] 导航完成，耗时 ${navigationTime}ms`);
            }

            return true;
        } catch (error) {
            this.performanceTracker.recordNavigationFailure();
            const eventData: NavigationEventData = {
                path,
                error: error as Error
            };
            this.emit('navigation:error', eventData);
            console.error('[EnhancedRouterManager] 导航错误:', error);
            return false;
        }
    }

    /**
     * 匹配路由
     */
    async matchRoute(path: string): Promise<RouteMatch | null> {
        const startTime = Date.now();

        // 首先检查缓存
        if (this.options.enableCache) {
            const cached = this.routeCache.get(path);
            if (cached) {
                this.performanceTracker.recordCacheHit();
                return cached;
            }
        }

        // 执行基于树的匹配
        const match = this.routeTree.matchRoute(path);

        // 更新性能统计
        if (this.options.enablePerformanceTracking) {
            const matchTime = Date.now() - startTime;
            this.performanceTracker.recordMatchTime(matchTime);
        }

        // 缓存结果
        if (this.options.enableCache && match) {
            this.routeCache.set(path, match);
        }

        return match;
    }

    /**
     * 添加全局路由守卫
     */
    addGuard(guard: (to: RouteMatch, from: RouteMatch | null) => boolean | Promise<boolean> | string | Promise<string>): void {
        this.guardManager.addGlobalGuard(guard);
    }

    /**
     * 移除全局路由守卫
     */
    removeGuard(guard: (to: RouteMatch, from: RouteMatch | null) => boolean | Promise<boolean> | string | Promise<string>): void {
        this.guardManager.removeGlobalGuard(guard);
    }

    /**
     * 获取当前路由
     */
    getCurrentRoute(): RouteMatch | null {
        return this.currentRoute;
    }

    /**
     * 获取当前路径
     */
    getCurrentPath(): string {
        if (this.options.mode === 'hash') {
            return window.location.hash.slice(1) || '/';
        } else if (this.options.mode === 'history') {
            return window.location.pathname + window.location.search;
        } else {
            return this.currentRoute?.path || '/';
        }
    }

    /**
     * 获取所有注册的路由
     */
    getRoutes(): EnhancedRouteConfig[] {
        return Array.from(this.routes.values());
    }

    /**
     * 获取路由器性能统计
     */
    getStats(): RouterPerformanceStats {
        return this.performanceTracker.getStats();
    }

    /**
     * 获取详细的性能报告
     */
    getPerformanceReport(): string {
        return this.performanceTracker.generateReport();
    }

    /**
     * 清空所有路由
     */
    clear(): void {
        this.routes.clear();
        this.routeTree.clear();
        this.currentRoute = null;
        this.routeCache.clear();
        this.guardManager.clearGlobalGuards();
        this.performanceTracker.reset();

        this.emit('router:cleared');
        console.log('[EnhancedRouterManager] 路由器已清空');
    }

    /**
     * 处理路由变化
     */
    private async handleRouteChange(): Promise<void> {
        const currentPath = this.getCurrentPath();
        const match = await this.matchRoute(currentPath);

        if (match && match !== this.currentRoute) {
            const previousRoute = this.currentRoute;

            // 运行守卫
            if (this.options.enableGuards) {
                const guardResult = await this.guardManager.runGuards(match, previousRoute);
                if (guardResult !== true) {
                    const eventData: NavigationEventData = {
                        path: currentPath,
                        match,
                        reason: guardResult
                    };
                    this.emit('navigation:blocked', eventData);
                    return;
                }
            }

            this.currentRoute = match;
            const changeEventData: RouteChangeEventData = {
                from: previousRoute,
                to: match
            };
            this.emit('route:changed', changeEventData);
        }
    }

    /**
     * 获取完整路径（包含基础路径）
     */
    private getFullPath(path: string): string {
        if (path.startsWith('/')) {
            return this.options.base + path;
        }
        return this.options.base + '/' + path;
    }

    /**
     * 销毁路由器
     */
    destroy(): void {
        this.stop();
        this.clear();
        this.routeCache.destroy();
        this.removeAllListeners();
        console.log('[EnhancedRouterManager] 路由器已销毁');
    }
}

// 导出所有类型和类
export { PerformanceTracker } from './performance-tracker';
export { RouteCacheManager } from './route-cache';
export { RouteGuardManager } from './route-guards';
export { RouteTreeManager } from './route-tree';
export * from './types';
