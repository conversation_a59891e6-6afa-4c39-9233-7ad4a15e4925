/**
 * @fileoverview 认证插件集成测试
 * <AUTHOR> <<EMAIL>>
 */

import { beforeEach, describe, expect, it, vi, afterEach } from 'vitest';
import type { MicroCoreKernel, AppInfo } from '@micro-core/core';
import { AuthPlugin } from '../../src/auth-plugin';
import type { AuthConfig, AuthUser } from '../../src/types';

// Mock MicroCore kernel with more realistic behavior
const createMockKernel = (): Partial<MicroCoreKernel> => {
  const eventHandlers = new Map<string, Function[]>();
  
  return {
    on: vi.fn((event: string, handler: Function) => {
      if (!eventHandlers.has(event)) {
        eventHandlers.set(event, []);
      }
      eventHandlers.get(event)!.push(handler);
    }),
    off: vi.fn((event: string, handler: Function) => {
      const handlers = eventHandlers.get(event);
      if (handlers) {
        const index = handlers.indexOf(handler);
        if (index > -1) {
          handlers.splice(index, 1);
        }
      }
    }),
    emit: vi.fn((event: string, ...args: any[]) => {
      const handlers = eventHandlers.get(event);
      if (handlers) {
        handlers.forEach(handler => handler(...args));
      }
    }),
    getApp: vi.fn((name: string) => ({
      name,
      status: 'mounted',
      container: document.createElement('div'),
    })),
    getAllApps: vi.fn(() => []),
    _eventHandlers: eventHandlers,
  };
};

// Mock fetch for API calls
global.fetch = vi.fn();

describe('AuthPlugin Integration Tests', () => {
  let authPlugin: AuthPlugin;
  let mockKernel: Partial<MicroCoreKernel>;
  let mockConfig: AuthConfig;

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Reset localStorage
    Object.defineProperty(window, 'localStorage', {
      value: {
        getItem: vi.fn(),
        setItem: vi.fn(),
        removeItem: vi.fn(),
        clear: vi.fn(),
      },
      writable: true,
    });

    mockKernel = createMockKernel();
    
    mockConfig = {
      tokenKey: 'integration-token',
      refreshTokenKey: 'integration-refresh-token',
      tokenExpiry: 3600000,
      refreshTokenExpiry: 86400000,
      autoRefresh: true,
      enablePermissionCheck: true,
      enableRoleCheck: true,
      loginUrl: '/login',
      unauthorizedUrl: '/unauthorized',
      storage: 'localStorage',
      apiEndpoints: {
        login: '/api/auth/login',
        refresh: '/api/auth/refresh',
        logout: '/api/auth/logout',
        profile: '/api/auth/profile',
      },
    };
    
    authPlugin = new AuthPlugin(mockConfig);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Full Authentication Flow', () => {
    it('should complete full login-to-logout flow', async () => {
      await authPlugin.install(mockKernel as MicroCoreKernel);

      // Mock successful login API response
      const mockUser: AuthUser = {
        id: '1',
        username: 'testuser',
        email: '<EMAIL>',
        roles: ['user', 'admin'],
        permissions: ['read', 'write'],
      };

      const mockLoginResponse = {
        user: mockUser,
        token: 'jwt-token-123',
        refreshToken: 'refresh-token-456',
      };

      (global.fetch as any).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockLoginResponse),
      });

      // Set up login handler
      authPlugin.setLoginHandler(async (username, password) => {
        const response = await fetch('/api/auth/login', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ username, password }),
        });
        return response.json();
      });

      // Perform login
      const loginResult = await authPlugin.login('testuser', 'password123');

      // Verify login success
      expect(loginResult.user).toEqual(mockUser);
      expect(authPlugin.isAuthenticated()).toBe(true);
      expect(authPlugin.getCurrentUser()).toEqual(mockUser);
      expect(authPlugin.hasRole('admin')).toBe(true);
      expect(authPlugin.hasPermission('write')).toBe(true);

      // Verify token storage
      expect(window.localStorage.setItem).toHaveBeenCalledWith(
        'integration-token',
        expect.stringContaining('jwt-token-123')
      );

      // Perform logout
      await authPlugin.logout();

      // Verify logout
      expect(authPlugin.isAuthenticated()).toBe(false);
      expect(authPlugin.getCurrentUser()).toBeNull();
      expect(window.localStorage.removeItem).toHaveBeenCalledWith('integration-token');
    });

    it('should handle token refresh flow', async () => {
      await authPlugin.install(mockKernel as MicroCoreKernel);

      // Mock refresh token response
      const mockRefreshResponse = {
        token: 'new-jwt-token-789',
        refreshToken: 'new-refresh-token-012',
      };

      (global.fetch as any).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockRefreshResponse),
      });

      // Set up refresh handler
      authPlugin.setRefreshTokenHandler(async (refreshToken) => {
        const response = await fetch('/api/auth/refresh', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ refreshToken }),
        });
        return response.json();
      });

      // Mock existing refresh token
      (window.localStorage.getItem as any).mockReturnValue('old-refresh-token');

      // Perform token refresh
      await authPlugin.refreshToken();

      // Verify new token storage
      expect(window.localStorage.setItem).toHaveBeenCalledWith(
        'integration-token',
        expect.stringContaining('new-jwt-token-789')
      );
    });
  });

  describe('App Lifecycle Integration', () => {
    it('should integrate with app mounting lifecycle', async () => {
      await authPlugin.install(mockKernel as MicroCoreKernel);

      // Mock authenticated user
      const mockUser: AuthUser = {
        id: '1',
        username: 'testuser',
        email: '<EMAIL>',
        roles: ['user'],
        permissions: ['read'],
      };

      await authPlugin.setCurrentUser(mockUser);

      // Mock app info
      const mockAppInfo: AppInfo = {
        name: 'test-app',
        entry: 'http://localhost:3000',
        container: '#app-container',
        activeWhen: '/test',
        meta: {
          requiresAuth: true,
          requiredRoles: ['user'],
        },
      };

      // Trigger app beforeMount event
      const eventHandlers = (mockKernel as any)._eventHandlers;
      const beforeMountHandlers = eventHandlers.get('app:beforeMount');
      
      expect(beforeMountHandlers).toBeDefined();
      expect(beforeMountHandlers.length).toBeGreaterThan(0);

      // Execute the handler
      const canMount = await beforeMountHandlers[0](mockAppInfo);
      expect(canMount).toBe(true);
    });

    it('should block unauthorized app mounting', async () => {
      await authPlugin.install(mockKernel as MicroCoreKernel);

      // No authenticated user

      const mockAppInfo: AppInfo = {
        name: 'admin-app',
        entry: 'http://localhost:3001',
        container: '#admin-container',
        activeWhen: '/admin',
        meta: {
          requiresAuth: true,
          requiredRoles: ['admin'],
        },
      };

      // Trigger app beforeMount event
      const eventHandlers = (mockKernel as any)._eventHandlers;
      const beforeMountHandlers = eventHandlers.get('app:beforeMount');
      
      // Execute the handler
      const canMount = await beforeMountHandlers[0](mockAppInfo);
      expect(canMount).toBe(false);
    });
  });

  describe('Cross-Plugin Communication', () => {
    it('should emit authentication events for other plugins', async () => {
      await authPlugin.install(mockKernel as MicroCoreKernel);

      const mockUser: AuthUser = {
        id: '1',
        username: 'testuser',
        email: '<EMAIL>',
        roles: ['user'],
        permissions: ['read'],
      };

      // Mock login handler
      authPlugin.setLoginHandler(async () => ({
        user: mockUser,
        token: 'test-token',
        refreshToken: 'test-refresh',
      }));

      // Perform login
      await authPlugin.login('testuser', 'password');

      // Verify events were emitted
      expect(mockKernel.emit).toHaveBeenCalledWith('auth:login', mockUser);
      expect(mockKernel.emit).toHaveBeenCalledWith('auth:userChanged', mockUser);

      // Perform logout
      await authPlugin.logout();

      // Verify logout event
      expect(mockKernel.emit).toHaveBeenCalledWith('auth:logout');
      expect(mockKernel.emit).toHaveBeenCalledWith('auth:userChanged', null);
    });

    it('should handle permission changes dynamically', async () => {
      await authPlugin.install(mockKernel as MicroCoreKernel);

      const initialUser: AuthUser = {
        id: '1',
        username: 'testuser',
        email: '<EMAIL>',
        roles: ['user'],
        permissions: ['read'],
      };

      await authPlugin.setCurrentUser(initialUser);
      expect(authPlugin.hasPermission('write')).toBe(false);

      // Update user permissions
      const updatedUser: AuthUser = {
        ...initialUser,
        permissions: ['read', 'write'],
      };

      await authPlugin.setCurrentUser(updatedUser);
      expect(authPlugin.hasPermission('write')).toBe(true);

      // Verify permission change event
      expect(mockKernel.emit).toHaveBeenCalledWith('auth:permissionsChanged', updatedUser);
    });
  });

  describe('Error Recovery and Resilience', () => {
    it('should recover from network failures during login', async () => {
      await authPlugin.install(mockKernel as MicroCoreKernel);

      // Mock network failure followed by success
      (global.fetch as any)
        .mockRejectedValueOnce(new Error('Network error'))
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            user: { id: '1', username: 'testuser' },
            token: 'recovery-token',
            refreshToken: 'recovery-refresh',
          }),
        });

      authPlugin.setLoginHandler(async (username, password) => {
        const response = await fetch('/api/auth/login', {
          method: 'POST',
          body: JSON.stringify({ username, password }),
        });
        return response.json();
      });

      // First attempt should fail
      await expect(authPlugin.login('testuser', 'password')).rejects.toThrow('Network error');

      // Second attempt should succeed
      const result = await authPlugin.login('testuser', 'password');
      expect(result.user.username).toBe('testuser');
    });

    it('should handle corrupted token data gracefully', async () => {
      await authPlugin.install(mockKernel as MicroCoreKernel);

      // Mock corrupted token data
      (window.localStorage.getItem as any).mockReturnValue('corrupted-json-data');

      // Should not throw and should return null
      expect(() => authPlugin.getToken()).not.toThrow();
      expect(authPlugin.getToken()).toBeNull();
      expect(authPlugin.isAuthenticated()).toBe(false);
    });

    it('should handle storage quota exceeded errors', async () => {
      await authPlugin.install(mockKernel as MicroCoreKernel);

      // Mock storage quota exceeded error
      (window.localStorage.setItem as any).mockImplementation(() => {
        throw new DOMException('QuotaExceededError');
      });

      // Should handle the error gracefully
      await expect(authPlugin.setToken('test-token')).resolves.not.toThrow();
    });
  });

  describe('Performance and Memory Management', () => {
    it('should clean up event listeners on uninstall', async () => {
      await authPlugin.install(mockKernel as MicroCoreKernel);

      const eventHandlers = (mockKernel as any)._eventHandlers;
      const initialHandlerCount = Array.from(eventHandlers.values())
        .reduce((sum, handlers) => sum + handlers.length, 0);

      await authPlugin.uninstall();

      const finalHandlerCount = Array.from(eventHandlers.values())
        .reduce((sum, handlers) => sum + handlers.length, 0);

      expect(finalHandlerCount).toBeLessThan(initialHandlerCount);
    });

    it('should handle rapid login/logout cycles without memory leaks', async () => {
      await authPlugin.install(mockKernel as MicroCoreKernel);

      const mockUser: AuthUser = {
        id: '1',
        username: 'testuser',
        email: '<EMAIL>',
        roles: ['user'],
        permissions: ['read'],
      };

      authPlugin.setLoginHandler(async () => ({
        user: mockUser,
        token: 'test-token',
        refreshToken: 'test-refresh',
      }));

      // Perform rapid login/logout cycles
      for (let i = 0; i < 10; i++) {
        await authPlugin.login('testuser', 'password');
        expect(authPlugin.isAuthenticated()).toBe(true);
        
        await authPlugin.logout();
        expect(authPlugin.isAuthenticated()).toBe(false);
      }

      // Should complete without errors
      expect(true).toBe(true);
    });
  });

  describe('Security Considerations', () => {
    it('should not expose sensitive data in error messages', async () => {
      await authPlugin.install(mockKernel as MicroCoreKernel);

      authPlugin.setLoginHandler(async () => {
        throw new Error('Authentication failed');
      });

      try {
        await authPlugin.login('testuser', 'sensitive-password');
      } catch (error) {
        expect(error.message).not.toContain('sensitive-password');
        expect(error.message).toBe('Authentication failed');
      }
    });

    it('should validate token format before storage', async () => {
      await authPlugin.install(mockKernel as MicroCoreKernel);

      // Attempt to set invalid token
      await authPlugin.setToken(''); // Empty token
      expect(authPlugin.getToken()).toBeNull();

      await authPlugin.setToken('invalid-token-format');
      // Should handle gracefully without throwing
      expect(() => authPlugin.getToken()).not.toThrow();
    });
  });
});
