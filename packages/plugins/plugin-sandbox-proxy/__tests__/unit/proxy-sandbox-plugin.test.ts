/**
 * @fileoverview 代理沙箱插件单元测试
 * <AUTHOR> <<EMAIL>>
 */

import { beforeEach, describe, expect, it, vi, afterEach } from 'vitest';
import type { MicroCoreKernel, AppInfo } from '@micro-core/core';
import { ProxySandboxPlugin } from '../../src/proxy-sandbox-plugin';
import type { ProxySandboxPluginOptions } from '../../src/proxy-sandbox-plugin';

// Mock MicroCore kernel
const mockKernel: Partial<MicroCoreKernel> = {
  on: vi.fn(),
  off: vi.fn(),
  emit: vi.fn(),
  getApp: vi.fn(),
  getAllApps: vi.fn(),
};

// Mock global objects for testing
const originalWindow = global.window;
const originalDocument = global.document;

describe('ProxySandboxPlugin', () => {
  let proxySandboxPlugin: ProxySandboxPlugin;
  let mockOptions: ProxySandboxPluginOptions;

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Reset global objects
    global.window = originalWindow;
    global.document = originalDocument;
    
    mockOptions = {
      enableStyleIsolation: true,
      enableScriptIsolation: true,
      enableEventIsolation: true,
      enableStorageIsolation: false,
      enableHistoryIsolation: false,
      strictMode: false,
      whiteList: ['console', 'setTimeout', 'setInterval'],
      blackList: ['eval', 'Function'],
    };
    
    proxySandboxPlugin = new ProxySandboxPlugin(mockOptions);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Plugin Initialization', () => {
    it('should initialize with default configuration', () => {
      const defaultPlugin = new ProxySandboxPlugin();
      expect(defaultPlugin.name).toBe('proxy-sandbox');
      expect(defaultPlugin.version).toBe('1.0.0');
    });

    it('should merge custom configuration with defaults', () => {
      const customOptions: Partial<ProxySandboxPluginOptions> = {
        strictMode: true,
        enableStorageIsolation: true,
      };
      const plugin = new ProxySandboxPlugin(customOptions);
      expect(plugin).toBeDefined();
    });

    it('should install plugin successfully', async () => {
      await expect(proxySandboxPlugin.install(mockKernel as MicroCoreKernel)).resolves.not.toThrow();
      expect(mockKernel.on).toHaveBeenCalledWith('app:beforeMount', expect.any(Function));
      expect(mockKernel.on).toHaveBeenCalledWith('app:beforeUnmount', expect.any(Function));
    });
  });

  describe('Sandbox Creation and Management', () => {
    beforeEach(async () => {
      await proxySandboxPlugin.install(mockKernel as MicroCoreKernel);
    });

    it('should create sandbox for app', async () => {
      const appInfo: AppInfo = {
        name: 'test-app',
        entry: 'http://localhost:3000',
        container: '#app-container',
        activeWhen: '/test',
      };

      const sandbox = await proxySandboxPlugin.createSandbox(appInfo.name, appInfo);
      expect(sandbox).toBeDefined();
      expect(sandbox.appName).toBe('test-app');
    });

    it('should reuse existing sandbox for same app', async () => {
      const appInfo: AppInfo = {
        name: 'test-app',
        entry: 'http://localhost:3000',
        container: '#app-container',
        activeWhen: '/test',
      };

      const sandbox1 = await proxySandboxPlugin.createSandbox(appInfo.name, appInfo);
      const sandbox2 = await proxySandboxPlugin.createSandbox(appInfo.name, appInfo);
      
      expect(sandbox1).toBe(sandbox2);
    });

    it('should destroy sandbox correctly', async () => {
      const appInfo: AppInfo = {
        name: 'test-app',
        entry: 'http://localhost:3000',
        container: '#app-container',
        activeWhen: '/test',
      };

      await proxySandboxPlugin.createSandbox(appInfo.name, appInfo);
      await proxySandboxPlugin.destroySandbox(appInfo.name);
      
      // Should be able to create new sandbox after destruction
      const newSandbox = await proxySandboxPlugin.createSandbox(appInfo.name, appInfo);
      expect(newSandbox).toBeDefined();
    });
  });

  describe('Proxy Handler Functionality', () => {
    beforeEach(async () => {
      await proxySandboxPlugin.install(mockKernel as MicroCoreKernel);
    });

    it('should isolate global variables', async () => {
      const appInfo: AppInfo = {
        name: 'test-app',
        entry: 'http://localhost:3000',
        container: '#app-container',
        activeWhen: '/test',
      };

      const sandbox = await proxySandboxPlugin.createSandbox(appInfo.name, appInfo);
      await sandbox.activate();

      // Set a global variable in sandbox
      sandbox.getProxy().testGlobal = 'sandbox-value';
      
      // Should not affect real window
      expect((global.window as any).testGlobal).toBeUndefined();
      
      // Should be accessible within sandbox
      expect(sandbox.getProxy().testGlobal).toBe('sandbox-value');
    });

    it('should handle whitelisted globals correctly', async () => {
      const appInfo: AppInfo = {
        name: 'test-app',
        entry: 'http://localhost:3000',
        container: '#app-container',
        activeWhen: '/test',
      };

      const sandbox = await proxySandboxPlugin.createSandbox(appInfo.name, appInfo);
      await sandbox.activate();

      // Whitelisted globals should be accessible
      expect(sandbox.getProxy().console).toBeDefined();
      expect(sandbox.getProxy().setTimeout).toBeDefined();
    });

    it('should block blacklisted globals', async () => {
      const appInfo: AppInfo = {
        name: 'test-app',
        entry: 'http://localhost:3000',
        container: '#app-container',
        activeWhen: '/test',
      };

      const sandbox = await proxySandboxPlugin.createSandbox(appInfo.name, appInfo);
      await sandbox.activate();

      // Blacklisted globals should be blocked
      expect(() => sandbox.getProxy().eval).toThrow();
      expect(() => sandbox.getProxy().Function).toThrow();
    });
  });

  describe('Style Isolation', () => {
    beforeEach(async () => {
      await proxySandboxPlugin.install(mockKernel as MicroCoreKernel);
    });

    it('should isolate styles when enabled', async () => {
      const appInfo: AppInfo = {
        name: 'test-app',
        entry: 'http://localhost:3000',
        container: '#app-container',
        activeWhen: '/test',
      };

      const sandbox = await proxySandboxPlugin.createSandbox(appInfo.name, appInfo);
      await sandbox.activate();

      // Mock document.createElement for style elements
      const mockStyleElement = {
        setAttribute: vi.fn(),
        textContent: '',
        sheet: {
          insertRule: vi.fn(),
          deleteRule: vi.fn(),
          cssRules: [],
        },
      };

      const originalCreateElement = document.createElement;
      document.createElement = vi.fn((tagName: string) => {
        if (tagName === 'style') {
          return mockStyleElement as any;
        }
        return originalCreateElement.call(document, tagName);
      });

      // Create style element in sandbox
      const styleEl = sandbox.getProxy().document.createElement('style');
      styleEl.textContent = '.test { color: red; }';

      // Should have sandbox-specific attributes
      expect(mockStyleElement.setAttribute).toHaveBeenCalledWith(
        'data-micro-core-app',
        'test-app'
      );

      document.createElement = originalCreateElement;
    });

    it('should clean up styles on sandbox destruction', async () => {
      const appInfo: AppInfo = {
        name: 'test-app',
        entry: 'http://localhost:3000',
        container: '#app-container',
        activeWhen: '/test',
      };

      const sandbox = await proxySandboxPlugin.createSandbox(appInfo.name, appInfo);
      await sandbox.activate();

      // Mock style elements
      const mockStyleElements = [
        { remove: vi.fn(), getAttribute: vi.fn(() => 'test-app') },
        { remove: vi.fn(), getAttribute: vi.fn(() => 'test-app') },
      ];

      document.querySelectorAll = vi.fn(() => mockStyleElements as any);

      await proxySandboxPlugin.destroySandbox(appInfo.name);

      // Should remove all app-specific styles
      mockStyleElements.forEach(el => {
        expect(el.remove).toHaveBeenCalled();
      });
    });
  });

  describe('Event Isolation', () => {
    beforeEach(async () => {
      await proxySandboxPlugin.install(mockKernel as MicroCoreKernel);
    });

    it('should isolate event listeners', async () => {
      const appInfo: AppInfo = {
        name: 'test-app',
        entry: 'http://localhost:3000',
        container: '#app-container',
        activeWhen: '/test',
      };

      const sandbox = await proxySandboxPlugin.createSandbox(appInfo.name, appInfo);
      await sandbox.activate();

      const mockEventListener = vi.fn();
      const originalAddEventListener = window.addEventListener;
      window.addEventListener = vi.fn();

      // Add event listener in sandbox
      sandbox.getProxy().addEventListener('click', mockEventListener);

      // Should track the event listener
      expect(window.addEventListener).toHaveBeenCalledWith('click', expect.any(Function));

      window.addEventListener = originalAddEventListener;
    });

    it('should clean up event listeners on sandbox destruction', async () => {
      const appInfo: AppInfo = {
        name: 'test-app',
        entry: 'http://localhost:3000',
        container: '#app-container',
        activeWhen: '/test',
      };

      const sandbox = await proxySandboxPlugin.createSandbox(appInfo.name, appInfo);
      await sandbox.activate();

      const mockEventListener = vi.fn();
      const originalRemoveEventListener = window.removeEventListener;
      window.removeEventListener = vi.fn();

      // Add and then destroy sandbox
      sandbox.getProxy().addEventListener('click', mockEventListener);
      await proxySandboxPlugin.destroySandbox(appInfo.name);

      // Should remove event listeners
      expect(window.removeEventListener).toHaveBeenCalled();

      window.removeEventListener = originalRemoveEventListener;
    });
  });

  describe('Script Isolation', () => {
    beforeEach(async () => {
      await proxySandboxPlugin.install(mockKernel as MicroCoreKernel);
    });

    it('should isolate script execution context', async () => {
      const appInfo: AppInfo = {
        name: 'test-app',
        entry: 'http://localhost:3000',
        container: '#app-container',
        activeWhen: '/test',
      };

      const sandbox = await proxySandboxPlugin.createSandbox(appInfo.name, appInfo);
      await sandbox.activate();

      // Execute script in sandbox context
      const result = sandbox.executeScript('var testVar = "sandbox"; testVar;');
      expect(result).toBe('sandbox');

      // Should not affect global scope
      expect((global as any).testVar).toBeUndefined();
    });

    it('should handle script execution errors gracefully', async () => {
      const appInfo: AppInfo = {
        name: 'test-app',
        entry: 'http://localhost:3000',
        container: '#app-container',
        activeWhen: '/test',
      };

      const sandbox = await proxySandboxPlugin.createSandbox(appInfo.name, appInfo);
      await sandbox.activate();

      // Execute invalid script
      expect(() => {
        sandbox.executeScript('invalid javascript syntax {');
      }).toThrow();
    });
  });

  describe('Storage Isolation', () => {
    beforeEach(async () => {
      await proxySandboxPlugin.install(mockKernel as MicroCoreKernel);
    });

    it('should isolate localStorage when enabled', async () => {
      const isolatedOptions: ProxySandboxPluginOptions = {
        ...mockOptions,
        enableStorageIsolation: true,
      };
      
      const isolatedPlugin = new ProxySandboxPlugin(isolatedOptions);
      await isolatedPlugin.install(mockKernel as MicroCoreKernel);

      const appInfo: AppInfo = {
        name: 'test-app',
        entry: 'http://localhost:3000',
        container: '#app-container',
        activeWhen: '/test',
      };

      const sandbox = await isolatedPlugin.createSandbox(appInfo.name, appInfo);
      await sandbox.activate();

      // Set item in sandbox localStorage
      sandbox.getProxy().localStorage.setItem('test-key', 'sandbox-value');

      // Should not affect real localStorage
      expect(localStorage.getItem('test-key')).toBeNull();

      // Should be accessible within sandbox
      expect(sandbox.getProxy().localStorage.getItem('test-key')).toBe('sandbox-value');
    });

    it('should isolate sessionStorage when enabled', async () => {
      const isolatedOptions: ProxySandboxPluginOptions = {
        ...mockOptions,
        enableStorageIsolation: true,
      };
      
      const isolatedPlugin = new ProxySandboxPlugin(isolatedOptions);
      await isolatedPlugin.install(mockKernel as MicroCoreKernel);

      const appInfo: AppInfo = {
        name: 'test-app',
        entry: 'http://localhost:3000',
        container: '#app-container',
        activeWhen: '/test',
      };

      const sandbox = await isolatedPlugin.createSandbox(appInfo.name, appInfo);
      await sandbox.activate();

      // Set item in sandbox sessionStorage
      sandbox.getProxy().sessionStorage.setItem('test-key', 'sandbox-value');

      // Should not affect real sessionStorage
      expect(sessionStorage.getItem('test-key')).toBeNull();

      // Should be accessible within sandbox
      expect(sandbox.getProxy().sessionStorage.getItem('test-key')).toBe('sandbox-value');
    });
  });

  describe('History Isolation', () => {
    beforeEach(async () => {
      await proxySandboxPlugin.install(mockKernel as MicroCoreKernel);
    });

    it('should isolate history when enabled', async () => {
      const isolatedOptions: ProxySandboxPluginOptions = {
        ...mockOptions,
        enableHistoryIsolation: true,
      };
      
      const isolatedPlugin = new ProxySandboxPlugin(isolatedOptions);
      await isolatedPlugin.install(mockKernel as MicroCoreKernel);

      const appInfo: AppInfo = {
        name: 'test-app',
        entry: 'http://localhost:3000',
        container: '#app-container',
        activeWhen: '/test',
      };

      const sandbox = await isolatedPlugin.createSandbox(appInfo.name, appInfo);
      await sandbox.activate();

      const originalPushState = history.pushState;
      history.pushState = vi.fn();

      // Push state in sandbox
      sandbox.getProxy().history.pushState({}, 'Test', '/test-path');

      // Should not directly affect real history
      expect(history.pushState).not.toHaveBeenCalled();

      history.pushState = originalPushState;
    });
  });

  describe('Error Handling and Edge Cases', () => {
    beforeEach(async () => {
      await proxySandboxPlugin.install(mockKernel as MicroCoreKernel);
    });

    it('should handle proxy creation errors gracefully', async () => {
      const appInfo: AppInfo = {
        name: 'test-app',
        entry: 'http://localhost:3000',
        container: '#app-container',
        activeWhen: '/test',
      };

      // Mock Proxy constructor to throw error
      const originalProxy = global.Proxy;
      global.Proxy = vi.fn(() => {
        throw new Error('Proxy not supported');
      });

      await expect(proxySandboxPlugin.createSandbox(appInfo.name, appInfo)).rejects.toThrow();

      global.Proxy = originalProxy;
    });

    it('should handle sandbox activation errors', async () => {
      const appInfo: AppInfo = {
        name: 'test-app',
        entry: 'http://localhost:3000',
        container: '#app-container',
        activeWhen: '/test',
      };

      const sandbox = await proxySandboxPlugin.createSandbox(appInfo.name, appInfo);
      
      // Mock activation to throw error
      const originalActivate = sandbox.activate;
      sandbox.activate = vi.fn().mockRejectedValue(new Error('Activation failed'));

      await expect(sandbox.activate()).rejects.toThrow('Activation failed');

      sandbox.activate = originalActivate;
    });

    it('should handle multiple sandbox activations gracefully', async () => {
      const appInfo: AppInfo = {
        name: 'test-app',
        entry: 'http://localhost:3000',
        container: '#app-container',
        activeWhen: '/test',
      };

      const sandbox = await proxySandboxPlugin.createSandbox(appInfo.name, appInfo);
      
      // Multiple activations should not cause errors
      await sandbox.activate();
      await sandbox.activate();
      await sandbox.activate();

      expect(sandbox.isActive()).toBe(true);
    });

    it('should handle deactivation of inactive sandbox', async () => {
      const appInfo: AppInfo = {
        name: 'test-app',
        entry: 'http://localhost:3000',
        container: '#app-container',
        activeWhen: '/test',
      };

      const sandbox = await proxySandboxPlugin.createSandbox(appInfo.name, appInfo);
      
      // Deactivate without activation should not throw
      await expect(sandbox.deactivate()).resolves.not.toThrow();
    });
  });

  describe('Performance Considerations', () => {
    beforeEach(async () => {
      await proxySandboxPlugin.install(mockKernel as MicroCoreKernel);
    });

    it('should handle large number of property accesses efficiently', async () => {
      const appInfo: AppInfo = {
        name: 'test-app',
        entry: 'http://localhost:3000',
        container: '#app-container',
        activeWhen: '/test',
      };

      const sandbox = await proxySandboxPlugin.createSandbox(appInfo.name, appInfo);
      await sandbox.activate();

      const proxy = sandbox.getProxy();
      const startTime = performance.now();

      // Perform many property accesses
      for (let i = 0; i < 1000; i++) {
        proxy[`prop${i}`] = i;
        const value = proxy[`prop${i}`];
        expect(value).toBe(i);
      }

      const endTime = performance.now();
      const duration = endTime - startTime;

      // Should complete within reasonable time (less than 100ms)
      expect(duration).toBeLessThan(100);
    });

    it('should clean up resources efficiently on destruction', async () => {
      const appInfo: AppInfo = {
        name: 'test-app',
        entry: 'http://localhost:3000',
        container: '#app-container',
        activeWhen: '/test',
      };

      const sandbox = await proxySandboxPlugin.createSandbox(appInfo.name, appInfo);
      await sandbox.activate();

      // Add many properties and event listeners
      const proxy = sandbox.getProxy();
      for (let i = 0; i < 100; i++) {
        proxy[`prop${i}`] = i;
        proxy.addEventListener(`event${i}`, () => {});
      }

      const startTime = performance.now();
      await proxySandboxPlugin.destroySandbox(appInfo.name);
      const endTime = performance.now();
      const duration = endTime - startTime;

      // Cleanup should be efficient (less than 50ms)
      expect(duration).toBeLessThan(50);
    });
  });

  describe('Plugin Lifecycle', () => {
    it('should uninstall plugin cleanly', async () => {
      await proxySandboxPlugin.install(mockKernel as MicroCoreKernel);
      
      // Create some sandboxes
      const appInfo1: AppInfo = {
        name: 'app1',
        entry: 'http://localhost:3000',
        container: '#app1',
        activeWhen: '/app1',
      };
      
      const appInfo2: AppInfo = {
        name: 'app2',
        entry: 'http://localhost:3001',
        container: '#app2',
        activeWhen: '/app2',
      };

      await proxySandboxPlugin.createSandbox(appInfo1.name, appInfo1);
      await proxySandboxPlugin.createSandbox(appInfo2.name, appInfo2);

      // Uninstall should clean up all sandboxes
      await proxySandboxPlugin.uninstall();

      expect(mockKernel.off).toHaveBeenCalledWith('app:beforeMount', expect.any(Function));
      expect(mockKernel.off).toHaveBeenCalledWith('app:beforeUnmount', expect.any(Function));
    });
  });
});
