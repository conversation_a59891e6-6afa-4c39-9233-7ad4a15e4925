/**
 * @fileoverview WASM 工具函数
 * @description 提供 WASM 相关的工具函数和辅助方法
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

/**
 * WASM 工具类
 */
export class WasmUtils {
    /**
     * 生成模块 ID
     */
    static generateModuleId(url: string): string {
        return `wasm-${Date.now()}-${url.split('/').pop()?.replace(/\W/g, '') || 'module'}`;
    }

    /**
     * 格式化字节数
     */
    static formatBytes(bytes: number): string {
        if (bytes === 0) return '0 B';

        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));

        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * 检查浏览器支持
     */
    static isSupported(): boolean {
        return typeof WebAssembly !== 'undefined' &&
            typeof WebAssembly.compile === 'function' &&
            typeof WebAssembly.instantiate === 'function';
    }

    /**
     * 检查流式编译支持
     */
    static isStreamingSupported(): boolean {
        return 'compileStreaming' in WebAssembly && 'instantiateStreaming' in WebAssembly;
    }

    /**
     * 检查 SIMD 支持
     */
    static async isSIMDSupported(): Promise<boolean> {
        try {
            // 简单的 SIMD 测试模块
            const simdTestModule = new Uint8Array([
                0x00, 0x61, 0x73, 0x6d, // WASM 魔数
                0x01, 0x00, 0x00, 0x00, // 版本
                0x01, 0x05, 0x01, 0x60, // 类型段
                0x00, 0x01, 0x7b,       // 函数类型：() -> v128
                0x03, 0x02, 0x01, 0x00, // 函数段
                0x0a, 0x09, 0x01, 0x07, // 代码段
                0x00, 0xfd, 0x0c, 0x00, // v128.const
                0x00, 0x00, 0x00, 0x0b  // end
            ]);

            await WebAssembly.compile(simdTestModule);
            return true;
        } catch {
            return false;
        }
    }

    /**
     * 检查多线程支持
     */
    static isThreadsSupported(): boolean {
        return typeof SharedArrayBuffer !== 'undefined' && typeof Atomics !== 'undefined';
    }

    /**
     * 获取 WASM 功能支持信息
     */
    static async getFeatureSupport(): Promise<{
        basic: boolean;
        streaming: boolean;
        simd: boolean;
        threads: boolean;
        bulkMemory: boolean;
        referenceTypes: boolean;
    }> {
        return {
            basic: this.isSupported(),
            streaming: this.isStreamingSupported(),
            simd: await this.isSIMDSupported(),
            threads: this.isThreadsSupported(),
            bulkMemory: await this.isBulkMemorySupported(),
            referenceTypes: await this.isReferenceTypesSupported()
        };
    }

    /**
     * 检查批量内存操作支持
     */
    static async isBulkMemorySupported(): Promise<boolean> {
        try {
            // 简单的批量内存测试模块
            const bulkMemoryTestModule = new Uint8Array([
                0x00, 0x61, 0x73, 0x6d, // WASM 魔数
                0x01, 0x00, 0x00, 0x00, // 版本
                0x05, 0x03, 0x01, 0x00, // 内存段
                0x01,                   // 1 页
                0x0a, 0x09, 0x01, 0x07, // 代码段
                0x00, 0x41, 0x00, 0x41, // i32.const 0, i32.const 0
                0x41, 0x00, 0xfc, 0x08, // i32.const 0, memory.fill
                0x00, 0x0b              // end
            ]);

            await WebAssembly.compile(bulkMemoryTestModule);
            return true;
        } catch {
            return false;
        }
    }

    /**
     * 检查引用类型支持
     */
    static async isReferenceTypesSupported(): Promise<boolean> {
        try {
            // 简单的引用类型测试模块
            const refTypesTestModule = new Uint8Array([
                0x00, 0x61, 0x73, 0x6d, // WASM 魔数
                0x01, 0x00, 0x00, 0x00, // 版本
                0x01, 0x04, 0x01, 0x60, // 类型段
                0x00, 0x00,             // 函数类型：() -> ()
                0x03, 0x02, 0x01, 0x00, // 函数段
                0x0a, 0x06, 0x01, 0x04, // 代码段
                0x00, 0xd0, 0x70, 0x0b  // ref.null func, end
            ]);

            await WebAssembly.compile(refTypesTestModule);
            return true;
        } catch {
            return false;
        }
    }

    /**
     * 验证 WASM 模块
     */
    static async validateModule(bytes: ArrayBuffer): Promise<boolean> {
        try {
            await WebAssembly.compile(bytes);
            return true;
        } catch {
            return false;
        }
    }

    /**
     * 获取模块导入信息
     */
    static getModuleImports(module: WebAssembly.Module): WebAssembly.ModuleImportDescriptor[] {
        try {
            return WebAssembly.Module.imports(module);
        } catch {
            return [];
        }
    }

    /**
     * 获取模块导出信息
     */
    static getModuleExports(module: WebAssembly.Module): WebAssembly.ModuleExportDescriptor[] {
        try {
            return WebAssembly.Module.exports(module);
        } catch {
            return [];
        }
    }

    /**
     * 计算模块大小
     */
    static calculateModuleSize(module: WebAssembly.Module): number {
        try {
            // 这是一个粗略的估算，实际大小可能不同
            const exports = this.getModuleExports(module);
            const imports = this.getModuleImports(module);

            // 基础大小估算
            let size = 1024; // 基础开销
            size += exports.length * 64; // 每个导出约 64 字节
            size += imports.length * 64; // 每个导入约 64 字节

            return size;
        } catch {
            return 0;
        }
    }

    /**
     * 创建默认导入对象
     */
    static createDefaultImportObject(): WebAssembly.Imports {
        return {
            env: {
                memory: new WebAssembly.Memory({ initial: 1 }),
                table: new WebAssembly.Table({ initial: 0, element: 'anyfunc' }),
                __memory_base: 0,
                __table_base: 0,
                abort: () => {
                    throw new Error('WASM 模块调用了 abort');
                },
                assert: (condition: number) => {
                    if (!condition) {
                        throw new Error('WASM 断言失败');
                    }
                }
            },
            wasi_snapshot_preview1: {
                proc_exit: (code: number) => {
                    throw new Error(`WASM 进程退出，代码: ${code}`);
                },
                fd_write: () => 0,
                fd_read: () => 0,
                fd_close: () => 0,
                fd_seek: () => 0
            }
        };
    }

    /**
     * 安全地调用 WASM 函数
     */
    static safeCall<T extends any[], R>(
        func: (...args: T) => R,
        args: T,
        timeout: number = 5000
    ): Promise<R> {
        return new Promise((resolve, reject) => {
            const timeoutId = setTimeout(() => {
                reject(new Error('WASM 函数调用超时'));
            }, timeout);

            try {
                const result = func(...args);
                clearTimeout(timeoutId);
                resolve(result);
            } catch (error) {
                clearTimeout(timeoutId);
                reject(error);
            }
        });
    }

    /**
     * 监控内存使用
     */
    static monitorMemoryUsage(memory: WebAssembly.Memory): {
        current: number;
        max: number;
        pages: number;
    } {
        const buffer = memory.buffer;
        const pages = buffer.byteLength / 65536; // 64KB per page

        return {
            current: buffer.byteLength,
            max: memory.buffer.byteLength,
            pages: Math.floor(pages)
        };
    }

    /**
     * 优化内存使用
     */
    static optimizeMemory(memory: WebAssembly.Memory, targetSize?: number): boolean {
        try {
            if (targetSize && memory.buffer.byteLength > targetSize) {
                // WASM 内存只能增长，不能缩小
                // 这里只是一个占位符，实际上无法缩小内存
                console.warn('WASM 内存无法缩小，只能增长');
                return false;
            }
            return true;
        } catch {
            return false;
        }
    }
}