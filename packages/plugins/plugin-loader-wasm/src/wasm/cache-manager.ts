/**
 * @fileoverview WASM 缓存管理器
 * @description 管理 WASM 模块的缓存存储和检索
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

import type { WasmCacheItem, WasmLoaderConfig, WasmModuleInfo } from '../types';

/**
 * WASM 缓存管理器
 */
export class WasmCacheManager {
    private config: Required<WasmLoaderConfig>;
    private memoryCache = new Map<string, WasmCacheItem>();
    private db: IDBDatabase | null = null;

    constructor(config: Required<WasmLoaderConfig>) {
        this.config = config;

        if (this.config.enableCache && this.config.cacheStrategy === 'indexeddb') {
            this.initIndexedDB();
        }
    }

    /**
     * 初始化 IndexedDB
     */
    private async initIndexedDB(): Promise<void> {
        if (!('indexedDB' in window)) {
            console.warn('浏览器不支持 IndexedDB，将使用内存缓存');
            return;
        }

        return new Promise((resolve, reject) => {
            const request = indexedDB.open('micro-core-wasm-cache', 1);

            request.onerror = () => {
                console.error('打开 IndexedDB 失败:', request.error);
                reject(request.error);
            };

            request.onsuccess = () => {
                this.db = request.result;
                resolve();
            };

            request.onupgradeneeded = (event) => {
                const db = (event.target as IDBOpenDBRequest).result;

                if (!db.objectStoreNames.contains('wasm-modules')) {
                    const store = db.createObjectStore('wasm-modules', { keyPath: 'key' });
                    store.createIndex('timestamp', 'timestamp', { unique: false });
                    store.createIndex('expireTime', 'expireTime', { unique: false });
                }
            };
        });
    }

    /**
     * 从缓存获取模块
     */
    async getFromCache(moduleId: string): Promise<WasmCacheItem | null> {
        switch (this.config.cacheStrategy) {
            case 'memory':
                return this.getFromMemoryCache(moduleId);
            case 'indexeddb':
                return await this.getFromIndexedDBCache(moduleId);
            default:
                return null;
        }
    }

    /**
     * 从内存缓存获取
     */
    private getFromMemoryCache(moduleId: string): WasmCacheItem | null {
        const item = this.memoryCache.get(moduleId);
        if (!item) {
            return null;
        }

        // 检查是否过期
        if (item.expireTime && Date.now() > item.expireTime) {
            this.memoryCache.delete(moduleId);
            return null;
        }

        // 更新访问信息
        item.accessCount++;
        item.lastAccessed = Date.now();

        return item;
    }

    /**
     * 从 IndexedDB 缓存获取
     */
    private async getFromIndexedDBCache(moduleId: string): Promise<WasmCacheItem | null> {
        if (!this.db) {
            return null;
        }

        return new Promise((resolve, reject) => {
            const transaction = this.db!.transaction(['wasm-modules'], 'readonly');
            const store = transaction.objectStore('wasm-modules');
            const request = store.get(moduleId);

            request.onsuccess = () => {
                const item = request.result as WasmCacheItem | undefined;
                if (!item) {
                    resolve(null);
                    return;
                }

                // 检查是否过期
                if (item.expireTime && Date.now() > item.expireTime) {
                    this.deleteFromIndexedDBCache(moduleId);
                    resolve(null);
                    return;
                }

                // 更新访问信息
                item.accessCount++;
                item.lastAccessed = Date.now();

                resolve(item);
            };

            request.onerror = () => {
                reject(request.error);
            };
        });
    }

    /**
     * 保存到缓存
     */
    async saveToCache(
        moduleId: string,
        module: WebAssembly.Module,
        moduleInfo: WasmModuleInfo
    ): Promise<void> {
        const cacheItem: WasmCacheItem = {
            key: moduleId,
            module,
            moduleInfo,
            timestamp: Date.now(),
            accessCount: 0,
            lastAccessed: Date.now()
        };

        switch (this.config.cacheStrategy) {
            case 'memory':
                await this.saveToMemoryCache(moduleId, cacheItem);
                break;
            case 'indexeddb':
                await this.saveToIndexedDBCache(moduleId, cacheItem);
                break;
        }
    }

    /**
     * 保存到内存缓存
     */
    private async saveToMemoryCache(moduleId: string, item: WasmCacheItem): Promise<void> {
        // 检查缓存大小限制
        await this.ensureCacheSize();
        this.memoryCache.set(moduleId, item);
    }

    /**
     * 保存到 IndexedDB 缓存
     */
    private async saveToIndexedDBCache(moduleId: string, item: WasmCacheItem): Promise<void> {
        if (!this.db) {
            return;
        }

        return new Promise((resolve, reject) => {
            const transaction = this.db!.transaction(['wasm-modules'], 'readwrite');
            const store = transaction.objectStore('wasm-modules');
            const request = store.put(item);

            request.onsuccess = () => {
                resolve();
            };

            request.onerror = () => {
                reject(request.error);
            };
        });
    }

    /**
     * 从 IndexedDB 删除缓存项
     */
    private async deleteFromIndexedDBCache(moduleId: string): Promise<void> {
        if (!this.db) {
            return;
        }

        return new Promise((resolve, reject) => {
            const transaction = this.db!.transaction(['wasm-modules'], 'readwrite');
            const store = transaction.objectStore('wasm-modules');
            const request = store.delete(moduleId);

            request.onsuccess = () => {
                resolve();
            };

            request.onerror = () => {
                reject(request.error);
            };
        });
    }

    /**
     * 确保缓存大小不超过限制
     */
    private async ensureCacheSize(): Promise<void> {
        if (this.memoryCache.size < this.config.cacheSize) {
            return;
        }

        // 使用 LRU 策略清理缓存
        const items = Array.from(this.memoryCache.entries());
        items.sort(([, a], [, b]) => a.lastAccessed - b.lastAccessed);

        const removeCount = Math.ceil(this.memoryCache.size * 0.2); // 清理 20%
        for (let i = 0; i < removeCount && i < items.length; i++) {
            this.memoryCache.delete(items[i][0]);
        }
    }

    /**
     * 删除缓存项
     */
    async deleteFromCache(moduleId: string): Promise<void> {
        this.memoryCache.delete(moduleId);

        if (this.config.cacheStrategy === 'indexeddb') {
            await this.deleteFromIndexedDBCache(moduleId);
        }
    }

    /**
     * 清空所有缓存
     */
    async clearCache(): Promise<void> {
        this.memoryCache.clear();

        if (this.config.cacheStrategy === 'indexeddb' && this.db) {
            return new Promise((resolve, reject) => {
                const transaction = this.db!.transaction(['wasm-modules'], 'readwrite');
                const store = transaction.objectStore('wasm-modules');
                const request = store.clear();

                request.onsuccess = () => {
                    resolve();
                };

                request.onerror = () => {
                    reject(request.error);
                };
            });
        }
    }

    /**
     * 获取缓存大小
     */
    getCacheSize(): number {
        return this.memoryCache.size;
    }

    /**
     * 销毁缓存管理器
     */
    destroy(): void {
        this.clearCache();

        if (this.db) {
            this.db.close();
            this.db = null;
        }
    }
}