/**
 * Logger 插件测试
 */

import { EventBus } from '@micro-core/core';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { LoggerPlugin } from '../plugin-logger/src';

// 模拟依赖
vi.mock('@micro-core/core', () => ({
    EventBus: class MockEventBus {
        on = vi.fn();
        off = vi.fn();
        emit = vi.fn();
        once = vi.fn();
    }
}));

describe('LoggerPlugin', () => {
    let loggerPlugin: LoggerPlugin;
    let mockEventBus: EventBus;
    let mockConfig: any;
    let originalConsole: any;

    beforeEach(() => {
        // 保存原始控制台方法
        originalConsole = { ...console };

        // 模拟控制台方法
        console.log = vi.fn();
        console.info = vi.fn();
        console.warn = vi.fn();
        console.error = vi.fn();
        console.debug = vi.fn();

        // 重置所有模拟
        vi.resetAllMocks();

        // 创建模拟事件总线
        mockEventBus = new EventBus();

        // 创建模拟配置
        mockConfig = {
            level: 'info',
            prefix: '[微前端]',
            enableTimestamp: true,
            enableSourceApp: true,
            enableConsole: true,
            enableDevTools: true,
            enableRemoteLogging: false,
            remoteLoggingEndpoint: 'https://api.example.com/logs',
            customFormatters: {},
            filters: []
        };

        // 创建插件实例
        loggerPlugin = new LoggerPlugin();
    });

    afterEach(() => {
        // 恢复原始控制台方法
        Object.keys(originalConsole).forEach(key => {
            console[key] = originalConsole[key];
        });

        vi.clearAllMocks();
    });

    describe('插件初始化', () => {
        it('应该能够正确初始化插件', async () => {
            await loggerPlugin.install({ eventBus: mockEventBus }, mockConfig);
            expect(mockEventBus.on).toHaveBeenCalled();
        });

        it('应该在没有配置时使用默认配置', async () => {
            await loggerPlugin.install({ eventBus: mockEventBus }, {});
            expect(mockEventBus.on).toHaveBeenCalled();
        });

        it('应该在初始化时设置事件监听器', async () => {
            await loggerPlugin.install({ eventBus: mockEventBus }, mockConfig);
            expect(mockEventBus.on).toHaveBeenCalledWith('logger:log', expect.any(Function));
            expect(mockEventBus.on).toHaveBeenCalledWith('logger:info', expect.any(Function));
            expect(mockEventBus.on).toHaveBeenCalledWith('logger:warn', expect.any(Function));
            expect(mockEventBus.on).toHaveBeenCalledWith('logger:error', expect.any(Function));
            expect(mockEventBus.on).toHaveBeenCalledWith('logger:debug', expect.any(Function));
        });
    });

    describe('日志记录', () => {
        it('应该能够记录不同级别的日志', async () => {
            await loggerPlugin.install({ eventBus: mockEventBus }, mockConfig);

            // 获取各级别日志处理函数
            const logHandlers = {
                log: (mockEventBus.on as any).mock.calls.find(call => call[0] === 'logger:log')[1],
                info: (mockEventBus.on as any).mock.calls.find(call => call[0] === 'logger:info')[1],
                warn: (mockEventBus.on as any).mock.calls.find(call => call[0] === 'logger:warn')[1],
                error: (mockEventBus.on as any).mock.calls.find(call => call[0] === 'logger:error')[1],
                debug: (mockEventBus.on as any).mock.calls.find(call => call[0] === 'logger:debug')[1]
            };

            // 测试各级别日志
            const testMessage = '测试日志消息';
            const testData = { key: 'value' };

            logHandlers.log(testMessage, testData);
            expect(console.log).toHaveBeenCalled();

            logHandlers.info(testMessage, testData);
            expect(console.info).toHaveBeenCalled();

            logHandlers.warn(testMessage, testData);
            expect(console.warn).toHaveBeenCalled();

            logHandlers.error(testMessage, testData);
            expect(console.error).toHaveBeenCalled();

            logHandlers.debug(testMessage, testData);
            expect(console.debug).toHaveBeenCalled();
        });

        it('应该根据配置的日志级别过滤日志', async () => {
            // 设置日志级别为 warn
            mockConfig.level = 'warn';
            await loggerPlugin.install({ eventBus: mockEventBus }, mockConfig);

            // 获取各级别日志处理函数
            const logHandlers = {
                log: (mockEventBus.on as any).mock.calls.find(call => call[0] === 'logger:log')[1],
                info: (mockEventBus.on as any).mock.calls.find(call => call[0] === 'logger:info')[1],
                warn: (mockEventBus.on as any).mock.calls.find(call => call[0] === 'logger:warn')[1],
                error: (mockEventBus.on as any).mock.calls.find(call => call[0] === 'logger:error')[1],
                debug: (mockEventBus.on as any).mock.calls.find(call => call[0] === 'logger:debug')[1]
            };

            // 测试各级别日志
            const testMessage = '测试日志消息';

            logHandlers.log(testMessage);
            expect(console.log).not.toHaveBeenCalled();

            logHandlers.info(testMessage);
            expect(console.info).not.toHaveBeenCalled();

            logHandlers.warn(testMessage);
            expect(console.warn).toHaveBeenCalled();

            logHandlers.error(testMessage);
            expect(console.error).toHaveBeenCalled();

            logHandlers.debug(testMessage);
            expect(console.debug).not.toHaveBeenCalled();
        });

        it('应该在禁用控制台输出时不记录到控制台', async () => {
            // 禁用控制台输出
            mockConfig.enableConsole = false;
            await loggerPlugin.install({ eventBus: mockEventBus }, mockConfig);

            // 获取日志处理函数
            const logHandler = (mockEventBus.on as any).mock.calls.find(
                call => call[0] === 'logger:log'
            )[1];

            // 测试日志
            logHandler('测试日志消息');
            expect(console.log).not.toHaveBeenCalled();
        });

        it('应该在启用时间戳时包含时间戳', async () => {
            // 启用时间戳
            mockConfig.enableTimestamp = true;
            await loggerPlugin.install({ eventBus: mockEventBus }, mockConfig);

            // 获取日志处理函数
            const logHandler = (mockEventBus.on as any).mock.calls.find(
                call => call[0] === 'logger:log'
            )[1];

            // 测试日志
            logHandler('测试日志消息');
            expect(console.log).toHaveBeenCalledWith(
                expect.stringContaining(mockConfig.prefix),
                expect.stringMatching(/\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/),
                '测试日志消息'
            );
        });

        it('应该在启用源应用时包含源应用信息', async () => {
            // 启用源应用
            mockConfig.enableSourceApp = true;
            await loggerPlugin.install({ eventBus: mockEventBus }, mockConfig);

            // 获取日志处理函数
            const logHandler = (mockEventBus.on as any).mock.calls.find(
                call => call[0] === 'logger:log'
            )[1];

            // 测试日志（带源应用）
            const sourceApp = 'test-app';
            logHandler('测试日志消息', null, sourceApp);
            expect(console.log).toHaveBeenCalledWith(
                expect.stringContaining(mockConfig.prefix),
                expect.any(String), // 时间戳
                expect.stringContaining(sourceApp),
                '测试日志消息'
            );
        });
    });

    describe('远程日志', () => {
        it('应该在启用远程日志时发送日志到远程端点', async () => {
            // 启用远程日志
            mockConfig.enableRemoteLogging = true;
            mockConfig.remoteLoggingEndpoint = 'https://api.example.com/logs';

            // 模拟 fetch API
            global.fetch = vi.fn().mockResolvedValue({
                ok: true,
                json: () => Promise.resolve({ success: true })
            });

            await loggerPlugin.install({ eventBus: mockEventBus }, mockConfig);

            // 获取日志处理函数
            const errorHandler = (mockEventBus.on as any).mock.calls.find(
                call => call[0] === 'logger:error'
            )[1];

            // 测试错误日志（通常会发送到远程）
            const errorMessage = '严重错误';
            const errorData = new Error('测试错误');
            errorHandler(errorMessage, errorData);

            // 验证远程日志发送
            expect(global.fetch).toHaveBeenCalledWith(
                mockConfig.remoteLoggingEndpoint,
                expect.objectContaining({
                    method: 'POST',
                    headers: expect.objectContaining({
                        'Content-Type': 'application/json'
                    }),
                    body: expect.any(String)
                })
            );

            // 验证发送的数据
            const callData = JSON.parse((global.fetch as any).mock.calls[0][1].body);
            expect(callData).toEqual(
                expect.objectContaining({
                    level: 'error',
                    message: errorMessage,
                    timestamp: expect.any(String)
                })
            );
        });

        it('应该在远程日志发送失败时处理错误', async () => {
            // 启用远程日志
            mockConfig.enableRemoteLogging = true;
            mockConfig.remoteLoggingEndpoint = 'https://api.example.com/logs';

            // 模拟 fetch API 失败
            global.fetch = vi.fn().mockRejectedValue(new Error('网络错误'));

            // 模拟控制台错误方法以捕获内部错误
            const consoleErrorSpy = vi.spyOn(console, 'error');

            await loggerPlugin.install({ eventBus: mockEventBus }, mockConfig);

            // 获取日志处理函数
            const errorHandler = (mockEventBus.on as any).mock.calls.find(
                call => call[0] === 'logger:error'
            )[1];

            // 测试错误日志
            errorHandler('严重错误');

            // 验证远程日志发送尝试
            expect(global.fetch).toHaveBeenCalled();

            // 验证错误处理
            expect(consoleErrorSpy).toHaveBeenCalledWith(
                expect.stringContaining('远程日志发送失败'),
                expect.any(Error)
            );
        });
    });

    describe('自定义格式化器', () => {
        it('应该能够使用自定义格式化器', async () => {
            // 添加自定义格式化器
            mockConfig.customFormatters = {
                json: (message: any) => JSON.stringify(message, null, 2)
            };

            await loggerPlugin.install({ eventBus: mockEventBus }, mockConfig);

            // 获取日志处理函数
            const logHandler = (mockEventBus.on as any).mock.calls.find(
                call => call[0] === 'logger:log'
            )[1];

            // 测试带自定义格式化器的日志
            const testData = { name: '测试', value: 123 };
            logHandler(testData, { formatter: 'json' });

            // 验证格式化输出
            expect(console.log).toHaveBeenCalledWith(
                expect.stringContaining(mockConfig.prefix),
                expect.any(String), // 时间戳
                JSON.stringify(testData, null, 2)
            );
        });
    });

    describe('日志过滤器', () => {
        it('应该能够根据过滤器过滤日志', async () => {
            // 添加日志过滤器
            mockConfig.filters = [
                (message: string) => !message.includes('过滤掉')
            ];

            await loggerPlugin.install({ eventBus: mockEventBus }, mockConfig);

            // 获取日志处理函数
            const logHandler = (mockEventBus.on as any).mock.calls.find(
                call => call[0] === 'logger:log'
            )[1];

            // 测试不应被过滤的日志
            logHandler('正常日志');
            expect(console.log).toHaveBeenCalledTimes(1);

            // 测试应被过滤的日志
            logHandler('这条日志应该被过滤掉');
            expect(console.log).toHaveBeenCalledTimes(1); // 仍然是1，表示第二条日志被过滤了
        });
    });

    describe('DevTools 集成', () => {
        it('应该在启用 DevTools 时发送日志到 DevTools', async () => {
            // 启用 DevTools
            mockConfig.enableDevTools = true;
            await loggerPlugin.install({ eventBus: mockEventBus }, mockConfig);

            // 获取日志处理函数
            const logHandler = (mockEventBus.on as any).mock.calls.find(
                call => call[0] === 'logger:log'
            )[1];

            // 测试日志
            logHandler('测试日志消息');

            // 验证发送到 DevTools
            expect(mockEventBus.emit).toHaveBeenCalledWith(
                'devtools:log',
                expect.objectContaining({
                    level: 'log',
                    message: '测试日志消息',
                    timestamp: expect.any(Number)
                })
            );
        });
    });

    describe('插件卸载', () => {
        it('应该能够正确卸载插件', async () => {
            await loggerPlugin.install({ eventBus: mockEventBus }, mockConfig);
            await loggerPlugin.uninstall({ eventBus: mockEventBus });

            expect(mockEventBus.off).toHaveBeenCalled();
        });

        it('应该在卸载时移除事件监听器', async () => {
            await loggerPlugin.install({ eventBus: mockEventBus }, mockConfig);
            await loggerPlugin.uninstall({ eventBus: mockEventBus });

            expect(mockEventBus.off).toHaveBeenCalledWith('logger:log', expect.any(Function));
            expect(mockEventBus.off).toHaveBeenCalledWith('logger:info', expect.any(Function));
            expect(mockEventBus.off).toHaveBeenCalledWith('logger:warn', expect.any(Function));
            expect(mockEventBus.off).toHaveBeenCalledWith('logger:error', expect.any(Function));
            expect(mockEventBus.off).toHaveBeenCalledWith('logger:debug', expect.any(Function));
        });
    });
});