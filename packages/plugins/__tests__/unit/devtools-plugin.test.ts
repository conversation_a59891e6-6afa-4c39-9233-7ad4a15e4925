/**
 * DevTools 插件测试
 */

import { EventBus } from '@micro-core/core';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { DevToolsPlugin } from '../plugin-devtools/src';

// 模拟依赖
vi.mock('@micro-core/core', () => ({
    EventBus: class MockEventBus {
        on = vi.fn();
        off = vi.fn();
        emit = vi.fn();
        once = vi.fn();
    }
}));

describe('DevToolsPlugin', () => {
    let devToolsPlugin: DevToolsPlugin;
    let mockEventBus: EventBus;
    let mockConfig: any;

    beforeEach(() => {
        // 重置所有模拟
        vi.resetAllMocks();

        // 创建模拟事件总线
        mockEventBus = new EventBus();

        // 创建模拟配置
        mockConfig = {
            enabled: true,
            defaultPanel: 'apps',
            position: 'bottom-right',
            theme: 'dark',
            features: {
                appInspector: true,
                eventMonitor: true,
                performanceMonitor: true,
                networkMonitor: true,
                stateInspector: true
            },
            customPanels: []
        };

        // 创建插件实例
        devToolsPlugin = new DevToolsPlugin();

        // 模拟 DOM 方法
        document.createElement = vi.fn().mockImplementation((tag) => {
            return {
                tagName: tag.toUpperCase(),
                style: {},
                classList: {
                    add: vi.fn(),
                    remove: vi.fn(),
                    toggle: vi.fn()
                },
                appendChild: vi.fn(),
                addEventListener: vi.fn(),
                removeEventListener: vi.fn(),
                setAttribute: vi.fn(),
                getAttribute: vi.fn(),
                querySelector: vi.fn(),
                querySelectorAll: vi.fn().mockReturnValue([]),
                innerHTML: ''
            };
        });

        document.body.appendChild = vi.fn();
        document.body.removeChild = vi.fn();
    });

    afterEach(() => {
        vi.clearAllMocks();
    });

    describe('插件初始化', () => {
        it('应该能够正确初始化插件', async () => {
            await devToolsPlugin.install({ eventBus: mockEventBus }, mockConfig);
            expect(mockEventBus.on).toHaveBeenCalled();
            expect(document.createElement).toHaveBeenCalled();
            expect(document.body.appendChild).toHaveBeenCalled();
        });

        it('应该在没有配置时使用默认配置', async () => {
            await devToolsPlugin.install({ eventBus: mockEventBus }, {});
            expect(mockEventBus.on).toHaveBeenCalled();
            expect(document.createElement).toHaveBeenCalled();
        });

        it('应该在禁用时不创建 DevTools 面板', async () => {
            mockConfig.enabled = false;
            await devToolsPlugin.install({ eventBus: mockEventBus }, mockConfig);
            expect(document.createElement).not.toHaveBeenCalled();
        });

        it('应该在初始化时设置事件监听器', async () => {
            await devToolsPlugin.install({ eventBus: mockEventBus }, mockConfig);
            expect(mockEventBus.on).toHaveBeenCalledWith('devtools:toggle', expect.any(Function));
            expect(mockEventBus.on).toHaveBeenCalledWith('devtools:show', expect.any(Function));
            expect(mockEventBus.on).toHaveBeenCalledWith('devtools:hide', expect.any(Function));
            expect(mockEventBus.on).toHaveBeenCalledWith('devtools:log', expect.any(Function));
        });
    });

    describe('DevTools 面板操作', () => {
        it('应该能够切换 DevTools 面板显示', async () => {
            await devToolsPlugin.install({ eventBus: mockEventBus }, mockConfig);

            // 模拟切换事件处理函数
            const toggleHandler = (mockEventBus.on as any).mock.calls.find(
                call => call[0] === 'devtools:toggle'
            )[1];

            // 调用切换处理函数
            toggleHandler();

            // 验证面板切换
            expect(mockEventBus.emit).toHaveBeenCalledWith('devtools:toggled', expect.any(Boolean));
        });

        it('应该能够显示 DevTools 面板', async () => {
            await devToolsPlugin.install({ eventBus: mockEventBus }, mockConfig);

            // 模拟显示事件处理函数
            const showHandler = (mockEventBus.on as any).mock.calls.find(
                call => call[0] === 'devtools:show'
            )[1];

            // 调用显示处理函数
            showHandler();

            // 验证面板显示
            expect(mockEventBus.emit).toHaveBeenCalledWith('devtools:shown');
        });

        it('应该能够隐藏 DevTools 面板', async () => {
            await devToolsPlugin.install({ eventBus: mockEventBus }, mockConfig);

            // 模拟隐藏事件处理函数
            const hideHandler = (mockEventBus.on as any).mock.calls.find(
                call => call[0] === 'devtools:hide'
            )[1];

            // 调用隐藏处理函数
            hideHandler();

            // 验证面板隐藏
            expect(mockEventBus.emit).toHaveBeenCalledWith('devtools:hidden');
        });
    });

    describe('日志记录', () => {
        it('应该能够记录日志', async () => {
            await devToolsPlugin.install({ eventBus: mockEventBus }, mockConfig);

            // 模拟日志事件处理函数
            const logHandler = (mockEventBus.on as any).mock.calls.find(
                call => call[0] === 'devtools:log'
            )[1];

            // 模拟日志数据
            const logData = {
                level: 'info',
                message: '测试日志',
                source: 'app1',
                timestamp: Date.now()
            };

            // 调用日志处理函数
            logHandler(logData);

            // 验证日志记录
            expect(mockEventBus.emit).toHaveBeenCalledWith('devtools:logged', logData);
        });

        it('应该能够处理不同级别的日志', async () => {
            await devToolsPlugin.install({ eventBus: mockEventBus }, mockConfig);

            // 模拟日志事件处理函数
            const logHandler = (mockEventBus.on as any).mock.calls.find(
                call => call[0] === 'devtools:log'
            )[1];

            // 模拟不同级别的日志数据
            const logLevels = ['info', 'warn', 'error', 'debug'];

            for (const level of logLevels) {
                const logData = {
                    level,
                    message: `${level} 测试日志`,
                    source: 'app1',
                    timestamp: Date.now()
                };

                // 调用日志处理函数
                logHandler(logData);

                // 验证日志记录
                expect(mockEventBus.emit).toHaveBeenCalledWith('devtools:logged', logData);
            }
        });
    });

    describe('自定义面板', () => {
        it('应该能够添加自定义面板', async () => {
            // 添加自定义面板配置
            mockConfig.customPanels = [
                {
                    id: 'custom-panel',
                    name: '自定义面板',
                    icon: 'custom-icon',
                    render: vi.fn()
                }
            ];

            await devToolsPlugin.install({ eventBus: mockEventBus }, mockConfig);

            // 验证自定义面板创建
            expect(document.createElement).toHaveBeenCalledTimes(expect.any(Number));
        });
    });

    describe('事件监控', () => {
        it('应该能够监控事件', async () => {
            // 启用事件监控
            mockConfig.features.eventMonitor = true;

            await devToolsPlugin.install({ eventBus: mockEventBus }, mockConfig);

            // 验证事件监听器设置
            expect(mockEventBus.on).toHaveBeenCalledWith(expect.any(String), expect.any(Function));
        });
    });

    describe('性能监控', () => {
        it('应该能够监控性能', async () => {
            // 启用性能监控
            mockConfig.features.performanceMonitor = true;

            // 模拟性能 API
            global.performance = {
                now: vi.fn().mockReturnValue(100),
                mark: vi.fn(),
                measure: vi.fn(),
                getEntriesByType: vi.fn().mockReturnValue([]),
                getEntriesByName: vi.fn().mockReturnValue([]),
                clearMarks: vi.fn(),
                clearMeasures: vi.fn()
            } as any;

            await devToolsPlugin.install({ eventBus: mockEventBus }, mockConfig);

            // 验证性能监控初始化
            expect(mockEventBus.on).toHaveBeenCalledWith('app:mounted', expect.any(Function));
            expect(mockEventBus.on).toHaveBeenCalledWith('app:unmounted', expect.any(Function));
        });
    });

    describe('插件卸载', () => {
        it('应该能够正确卸载插件', async () => {
            await devToolsPlugin.install({ eventBus: mockEventBus }, mockConfig);
            await devToolsPlugin.uninstall({ eventBus: mockEventBus });

            expect(mockEventBus.off).toHaveBeenCalled();
            expect(document.body.removeChild).toHaveBeenCalled();
        });

        it('应该在卸载时移除事件监听器', async () => {
            await devToolsPlugin.install({ eventBus: mockEventBus }, mockConfig);
            await devToolsPlugin.uninstall({ eventBus: mockEventBus });

            expect(mockEventBus.off).toHaveBeenCalledWith('devtools:toggle', expect.any(Function));
            expect(mockEventBus.off).toHaveBeenCalledWith('devtools:show', expect.any(Function));
            expect(mockEventBus.off).toHaveBeenCalledWith('devtools:hide', expect.any(Function));
            expect(mockEventBus.off).toHaveBeenCalledWith('devtools:log', expect.any(Function));
        });
    });
});