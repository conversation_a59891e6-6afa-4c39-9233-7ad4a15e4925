/**
 * Sandbox Proxy 插件测试
 */

// 模拟 EventBus 类型
interface EventBus {
    on: (event: string, handler: Function) => void;
    off: (event: string, handler: Function) => void;
    emit: (event: string, ...args: any[]) => void;
    once: (event: string, handler: Function) => void;
}

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import SandboxProxyPlugin from '../plugin-sandbox-proxy/src';

// 模拟依赖
const MockEventBus = vi.fn().mockImplementation(() => ({
    on: vi.fn(),
    off: vi.fn(),
    emit: vi.fn(),
    once: vi.fn()
}));

describe('SandboxProxyPlugin', () => {
    let sandboxPlugin: SandboxProxyPlugin;
    let mockEventBus: EventBus;
    let mockConfig: any;

    beforeEach(() => {
        // 重置所有模拟
        vi.resetAllMocks();

        // 创建模拟事件总线
        mockEventBus = MockEventBus();

        // 创建模拟配置
        mockConfig = {
            strictMode: true,
            whiteList: ['console', 'setTimeout', 'clearTimeout'],
            autoRestore: true,
            disableDevtools: false,
            isolateGlobalState: true,
            scopeProperties: ['__MICRO_APP_ENVIRONMENT__', '__MICRO_APP_PUBLIC_PATH__']
        };

        // 创建插件实例
        sandboxPlugin = new SandboxProxyPlugin();

        // 模拟全局对象
        global.window = {
            ...global,
            addEventListener: vi.fn(),
            removeEventListener: vi.fn(),
            dispatchEvent: vi.fn()
        } as any;
    });

    afterEach(() => {
        vi.clearAllMocks();
    });

    describe('插件初始化', () => {
        it('应该能够正确初始化插件', async () => {
            await sandboxPlugin.install({ eventBus: mockEventBus }, mockConfig);
            expect(mockEventBus.on).toHaveBeenCalled();
        });

        it('应该在没有配置时使用默认配置', async () => {
            await sandboxPlugin.install({ eventBus: mockEventBus }, {});
            expect(mockEventBus.on).toHaveBeenCalled();
        });

        it('应该在初始化时设置事件监听器', async () => {
            await sandboxPlugin.install({ eventBus: mockEventBus }, mockConfig);
            expect(mockEventBus.on).toHaveBeenCalledWith('sandbox:create', expect.any(Function));
            expect(mockEventBus.on).toHaveBeenCalledWith('sandbox:destroy', expect.any(Function));
            expect(mockEventBus.on).toHaveBeenCalledWith('sandbox:activate', expect.any(Function));
            expect(mockEventBus.on).toHaveBeenCalledWith('sandbox:deactivate', expect.any(Function));
        });
    });

    describe('沙箱创建', () => {
        it('应该能够创建沙箱环境', async () => {
            await sandboxPlugin.install({ eventBus: mockEventBus }, mockConfig);

            // 模拟沙箱创建事件处理函数
            const createHandler = (mockEventBus.on as any).mock.calls.find(
                call => call[0] === 'sandbox:create'
            )[1];

            // 模拟应用数据
            const appData = {
                name: 'test-app',
                id: 'app-1'
            };

            // 调用创建处理函数
            const sandbox = createHandler(appData);

            // 验证沙箱创建
            expect(sandbox).toBeDefined();
            expect(sandbox.proxy).toBeDefined();
            expect(mockEventBus.emit).toHaveBeenCalledWith('sandbox:created', expect.objectContaining({
                name: appData.name,
                id: appData.id
            }));
        });

        it('应该在沙箱中隔离全局状态', async () => {
            await sandboxPlugin.install({ eventBus: mockEventBus }, mockConfig);

            // 模拟沙箱创建事件处理函数
            const createHandler = (mockEventBus.on as any).mock.calls.find(
                call => call[0] === 'sandbox:create'
            )[1];

            // 创建两个沙箱
            const app1Data = { name: 'app-1', id: 'app-1' };
            const app2Data = { name: 'app-2', id: 'app-2' };

            const sandbox1 = createHandler(app1Data);
            const sandbox2 = createHandler(app2Data);

            // 在沙箱1中设置全局变量
            sandbox1.proxy.testVar = 'app1-value';

            // 验证沙箱2中没有该变量
            expect(sandbox2.proxy.testVar).toBeUndefined();

            // 验证全局环境中没有该变量
            expect((global as any).testVar).toBeUndefined();
        });

        it('应该在严格模式下阻止修改白名单外的全局属性', async () => {
            mockConfig.strictMode = true;
            await sandboxPlugin.install({ eventBus: mockEventBus }, mockConfig);

            // 模拟沙箱创建事件处理函数
            const createHandler = (mockEventBus.on as any).mock.calls.find(
                call => call[0] === 'sandbox:create'
            )[1];

            // 创建沙箱
            const appData = { name: 'test-app', id: 'app-1' };
            const sandbox = createHandler(appData);

            // 尝试修改白名单外的全局属性
            const setRestrictedProp = () => {
                sandbox.proxy.document = 'modified';
            };

            // 验证操作被阻止
            expect(setRestrictedProp).toThrow();
        });

        it('应该允许访问白名单内的全局属性', async () => {
            await sandboxPlugin.install({ eventBus: mockEventBus }, mockConfig);

            // 模拟沙箱创建事件处理函数
            const createHandler = (mockEventBus.on as any).mock.calls.find(
                call => call[0] === 'sandbox:create'
            )[1];

            // 创建沙箱
            const appData = { name: 'test-app', id: 'app-1' };
            const sandbox = createHandler(appData);

            // 设置白名单内的全局属性
            (global as any).console = { log: vi.fn() };

            // 验证可以访问白名单内的属性
            expect(sandbox.proxy.console).toBeDefined();
        });
    });

    describe('沙箱激活和停用', () => {
        it('应该能够激活沙箱', async () => {
            await sandboxPlugin.install({ eventBus: mockEventBus }, mockConfig);

            // 模拟沙箱创建和激活事件处理函数
            const createHandler = (mockEventBus.on as any).mock.calls.find(
                call => call[0] === 'sandbox:create'
            )[1];
            const activateHandler = (mockEventBus.on as any).mock.calls.find(
                call => call[0] === 'sandbox:activate'
            )[1];

            // 创建沙箱
            const appData = { name: 'test-app', id: 'app-1' };
            const sandbox = createHandler(appData);

            // 激活沙箱
            activateHandler({ id: appData.id });

            // 验证沙箱激活
            expect(mockEventBus.emit).toHaveBeenCalledWith('sandbox:activated', expect.objectContaining({
                id: appData.id
            }));
        });

        it('应该能够停用沙箱', async () => {
            await sandboxPlugin.install({ eventBus: mockEventBus }, mockConfig);

            // 模拟沙箱创建和停用事件处理函数
            const createHandler = (mockEventBus.on as any).mock.calls.find(
                call => call[0] === 'sandbox:create'
            )[1];
            const deactivateHandler = (mockEventBus.on as any).mock.calls.find(
                call => call[0] === 'sandbox:deactivate'
            )[1];

            // 创建沙箱
            const appData = { name: 'test-app', id: 'app-1' };
            const sandbox = createHandler(appData);

            // 停用沙箱
            deactivateHandler({ id: appData.id });

            // 验证沙箱停用
            expect(mockEventBus.emit).toHaveBeenCalledWith('sandbox:deactivated', expect.objectContaining({
                id: appData.id
            }));
        });

        it('应该在停用沙箱时恢复全局状态', async () => {
            mockConfig.autoRestore = true;
            await sandboxPlugin.install({ eventBus: mockEventBus }, mockConfig);

            // 模拟沙箱创建、激活和停用事件处理函数
            const createHandler = (mockEventBus.on as any).mock.calls.find(
                call => call[0] === 'sandbox:create'
            )[1];
            const activateHandler = (mockEventBus.on as any).mock.calls.find(
                call => call[0] === 'sandbox:activate'
            )[1];
            const deactivateHandler = (mockEventBus.on as any).mock.calls.find(
                call => call[0] === 'sandbox:deactivate'
            )[1];

            // 创建沙箱
            const appData = { name: 'test-app', id: 'app-1' };
            const sandbox = createHandler(appData);

            // 激活沙箱
            activateHandler({ id: appData.id });

            // 在沙箱中修改全局变量
            sandbox.proxy.testVar = 'test-value';

            // 停用沙箱
            deactivateHandler({ id: appData.id });

            // 验证全局变量被恢复
            expect((global as any).testVar).toBeUndefined();
        });
    });

    describe('沙箱销毁', () => {
        it('应该能够销毁沙箱', async () => {
            await sandboxPlugin.install({ eventBus: mockEventBus }, mockConfig);

            // 模拟沙箱创建和销毁事件处理函数
            const createHandler = (mockEventBus.on as any).mock.calls.find(
                call => call[0] === 'sandbox:create'
            )[1];
            const destroyHandler = (mockEventBus.on as any).mock.calls.find(
                call => call[0] === 'sandbox:destroy'
            )[1];

            // 创建沙箱
            const appData = { name: 'test-app', id: 'app-1' };
            const sandbox = createHandler(appData);

            // 销毁沙箱
            destroyHandler({ id: appData.id });

            // 验证沙箱销毁
            expect(mockEventBus.emit).toHaveBeenCalledWith('sandbox:destroyed', expect.objectContaining({
                id: appData.id
            }));
        });

        it('应该在销毁沙箱时清理所有资源', async () => {
            await sandboxPlugin.install({ eventBus: mockEventBus }, mockConfig);

            // 模拟沙箱创建和销毁事件处理函数
            const createHandler = (mockEventBus.on as any).mock.calls.find(
                call => call[0] === 'sandbox:create'
            )[1];
            const destroyHandler = (mockEventBus.on as any).mock.calls.find(
                call => call[0] === 'sandbox:destroy'
            )[1];

            // 创建沙箱
            const appData = { name: 'test-app', id: 'app-1' };
            const sandbox = createHandler(appData);

            // 在沙箱中添加事件监听器
            sandbox.proxy.addEventListener = vi.fn();
            sandbox.proxy.addEventListener('click', () => { });

            // 销毁沙箱
            destroyHandler({ id: appData.id });

            // 验证事件监听器被清理
            expect(mockEventBus.emit).toHaveBeenCalledWith('sandbox:destroyed', expect.any(Object));
        });
    });

    describe('插件卸载', () => {
        it('应该能够正确卸载插件', async () => {
            await sandboxPlugin.install({ eventBus: mockEventBus }, mockConfig);
            await sandboxPlugin.uninstall({ eventBus: mockEventBus });

            expect(mockEventBus.off).toHaveBeenCalled();
        });

        it('应该在卸载时移除事件监听器', async () => {
            await sandboxPlugin.install({ eventBus: mockEventBus }, mockConfig);
            await sandboxPlugin.uninstall({ eventBus: mockEventBus });

            expect(mockEventBus.off).toHaveBeenCalledWith('sandbox:create', expect.any(Function));
            expect(mockEventBus.off).toHaveBeenCalledWith('sandbox:destroy', expect.any(Function));
            expect(mockEventBus.off).toHaveBeenCalledWith('sandbox:activate', expect.any(Function));
            expect(mockEventBus.off).toHaveBeenCalledWith('sandbox:deactivate', expect.any(Function));
        });

        it('应该在卸载时销毁所有沙箱', async () => {
            await sandboxPlugin.install({ eventBus: mockEventBus }, mockConfig);

            // 模拟沙箱创建事件处理函数
            const createHandler = (mockEventBus.on as any).mock.calls.find(
                call => call[0] === 'sandbox:create'
            )[1];

            // 创建多个沙箱
            createHandler({ name: 'app-1', id: 'app-1' });
            createHandler({ name: 'app-2', id: 'app-2' });

            // 重置 emit 调用记录
            (mockEventBus.emit as any).mockClear();

            // 卸载插件
            await sandboxPlugin.uninstall({ eventBus: mockEventBus });

            // 验证所有沙箱被销毁
            expect(mockEventBus.emit).toHaveBeenCalledWith('sandbox:destroyed', expect.any(Object));
        });
    });
});