/**
 * Communication 插件测试
 */

import { EventBus } from '@micro-core/core';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { CommunicationPlugin } from '../plugin-communication/src';

// 模拟依赖
vi.mock('@micro-core/core', () => ({
    EventBus: class MockEventBus {
        on = vi.fn();
        off = vi.fn();
        emit = vi.fn();
        once = vi.fn();
    }
}));

describe('CommunicationPlugin', () => {
    let communicationPlugin: CommunicationPlugin;
    let mockEventBus: EventBus;
    let mockConfig: any;

    beforeEach(() => {
        // 重置所有模拟
        vi.resetAllMocks();

        // 创建模拟事件总线
        mockEventBus = new EventBus();

        // 创建模拟配置
        mockConfig = {
            channels: ['app1', 'app2', 'global'],
            defaultChannel: 'global',
            messageTimeout: 5000,
            enableBroadcast: true,
            enableP2P: true,
            onMessageReceived: vi.fn(),
            onMessageSent: vi.fn(),
            onError: vi.fn()
        };

        // 创建插件实例
        communicationPlugin = new CommunicationPlugin();
    });

    afterEach(() => {
        vi.clearAllMocks();
    });

    describe('插件初始化', () => {
        it('应该能够正确初始化插件', async () => {
            await communicationPlugin.install({ eventBus: mockEventBus }, mockConfig);
            expect(mockEventBus.on).toHaveBeenCalled();
        });

        it('应该在没有配置时使用默认配置', async () => {
            await communicationPlugin.install({ eventBus: mockEventBus }, {});
            expect(mockEventBus.on).toHaveBeenCalled();
        });

        it('应该在初始化时设置事件监听器', async () => {
            await communicationPlugin.install({ eventBus: mockEventBus }, mockConfig);
            expect(mockEventBus.on).toHaveBeenCalledWith('communication:send', expect.any(Function));
            expect(mockEventBus.on).toHaveBeenCalledWith('communication:broadcast', expect.any(Function));
            expect(mockEventBus.on).toHaveBeenCalledWith('communication:subscribe', expect.any(Function));
            expect(mockEventBus.on).toHaveBeenCalledWith('communication:unsubscribe', expect.any(Function));
        });
    });

    describe('消息发送', () => {
        it('应该能够发送点对点消息', async () => {
            await communicationPlugin.install({ eventBus: mockEventBus }, mockConfig);

            // 模拟发送消息事件处理函数
            const sendHandler = (mockEventBus.on as any).mock.calls.find(
                call => call[0] === 'communication:send'
            )[1];

            // 模拟消息数据
            const message = {
                to: 'app2',
                data: { type: 'action', payload: { id: 123 } },
                channel: 'global'
            };

            // 调用发送处理函数
            await sendHandler(message);

            // 验证事件发出
            expect(mockEventBus.emit).toHaveBeenCalledWith(
                'communication:message',
                expect.objectContaining({
                    from: expect.any(String),
                    to: 'app2',
                    data: message.data,
                    channel: 'global',
                    timestamp: expect.any(Number)
                })
            );
            expect(mockConfig.onMessageSent).toHaveBeenCalled();
        });

        it('应该能够广播消息', async () => {
            await communicationPlugin.install({ eventBus: mockEventBus }, mockConfig);

            // 模拟广播消息事件处理函数
            const broadcastHandler = (mockEventBus.on as any).mock.calls.find(
                call => call[0] === 'communication:broadcast'
            )[1];

            // 模拟消息数据
            const message = {
                data: { type: 'notification', payload: { message: '系统通知' } },
                channel: 'global'
            };

            // 调用广播处理函数
            await broadcastHandler(message);

            // 验证事件发出
            expect(mockEventBus.emit).toHaveBeenCalledWith(
                'communication:broadcast',
                expect.objectContaining({
                    from: expect.any(String),
                    data: message.data,
                    channel: 'global',
                    timestamp: expect.any(Number),
                    broadcast: true
                })
            );
            expect(mockConfig.onMessageSent).toHaveBeenCalled();
        });

        it('应该在消息发送失败时处理错误', async () => {
            await communicationPlugin.install({ eventBus: mockEventBus }, mockConfig);

            // 模拟发送消息事件处理函数
            const sendHandler = (mockEventBus.on as any).mock.calls.find(
                call => call[0] === 'communication:send'
            )[1];

            // 模拟消息数据（无效目标）
            const message = {
                to: undefined,
                data: { type: 'action', payload: { id: 123 } },
                channel: 'global'
            };

            // 模拟 emit 抛出错误
            (mockEventBus.emit as any).mockImplementation(() => {
                throw new Error('发送失败');
            });

            // 调用发送处理函数
            await sendHandler(message);

            // 验证错误处理
            expect(mockConfig.onError).toHaveBeenCalled();
        });
    });

    describe('频道订阅', () => {
        it('应该能够订阅频道', async () => {
            await communicationPlugin.install({ eventBus: mockEventBus }, mockConfig);

            // 模拟订阅频道事件处理函数
            const subscribeHandler = (mockEventBus.on as any).mock.calls.find(
                call => call[0] === 'communication:subscribe'
            )[1];

            // 模拟订阅数据
            const subscription = {
                channel: 'app1',
                callback: vi.fn()
            };

            // 调用订阅处理函数
            await subscribeHandler(subscription);

            // 验证事件监听器添加
            expect(mockEventBus.on).toHaveBeenCalledWith(
                'communication:message',
                expect.any(Function)
            );
        });

        it('应该能够取消订阅频道', async () => {
            await communicationPlugin.install({ eventBus: mockEventBus }, mockConfig);

            // 模拟取消订阅频道事件处理函数
            const unsubscribeHandler = (mockEventBus.on as any).mock.calls.find(
                call => call[0] === 'communication:unsubscribe'
            )[1];

            // 模拟订阅数据
            const subscription = {
                channel: 'app1',
                callback: vi.fn()
            };

            // 调用取消订阅处理函数
            await unsubscribeHandler(subscription);

            // 验证事件监听器移除
            expect(mockEventBus.off).toHaveBeenCalledWith(
                'communication:message',
                expect.any(Function)
            );
        });
    });

    describe('消息接收', () => {
        it('应该能够接收消息并触发回调', async () => {
            await communicationPlugin.install({ eventBus: mockEventBus }, mockConfig);

            // 模拟订阅频道
            const subscribeHandler = (mockEventBus.on as any).mock.calls.find(
                call => call[0] === 'communication:subscribe'
            )[1];

            const callback = vi.fn();
            await subscribeHandler({
                channel: 'app1',
                callback
            });

            // 获取消息监听器
            const messageListener = (mockEventBus.on as any).mock.calls.find(
                call => call[0] === 'communication:message'
            )[1];

            // 模拟接收消息
            const message = {
                from: 'app2',
                to: 'app1',
                data: { type: 'action', payload: { id: 123 } },
                channel: 'app1',
                timestamp: Date.now()
            };

            // 调用消息监听器
            messageListener(message);

            // 验证回调调用
            expect(callback).toHaveBeenCalledWith(message);
            expect(mockConfig.onMessageReceived).toHaveBeenCalledWith(message);
        });

        it('应该能够接收广播消息', async () => {
            await communicationPlugin.install({ eventBus: mockEventBus }, mockConfig);

            // 模拟订阅频道
            const subscribeHandler = (mockEventBus.on as any).mock.calls.find(
                call => call[0] === 'communication:subscribe'
            )[1];

            const callback = vi.fn();
            await subscribeHandler({
                channel: 'global',
                callback
            });

            // 获取广播监听器
            const broadcastListener = (mockEventBus.on as any).mock.calls.find(
                call => call[0] === 'communication:broadcast'
            )[1];

            // 模拟接收广播消息
            const message = {
                from: 'app2',
                data: { type: 'notification', payload: { message: '系统通知' } },
                channel: 'global',
                timestamp: Date.now(),
                broadcast: true
            };

            // 调用广播监听器
            broadcastListener(message);

            // 验证回调调用
            expect(callback).toHaveBeenCalledWith(message);
            expect(mockConfig.onMessageReceived).toHaveBeenCalledWith(message);
        });
    });

    describe('插件卸载', () => {
        it('应该能够正确卸载插件', async () => {
            await communicationPlugin.install({ eventBus: mockEventBus }, mockConfig);
            await communicationPlugin.uninstall({ eventBus: mockEventBus });

            expect(mockEventBus.off).toHaveBeenCalled();
        });

        it('应该在卸载时移除事件监听器', async () => {
            await communicationPlugin.install({ eventBus: mockEventBus }, mockConfig);
            await communicationPlugin.uninstall({ eventBus: mockEventBus });

            expect(mockEventBus.off).toHaveBeenCalledWith('communication:send', expect.any(Function));
            expect(mockEventBus.off).toHaveBeenCalledWith('communication:broadcast', expect.any(Function));
            expect(mockEventBus.off).toHaveBeenCalledWith('communication:subscribe', expect.any(Function));
            expect(mockEventBus.off).toHaveBeenCalledWith('communication:unsubscribe', expect.any(Function));
        });
    });
});