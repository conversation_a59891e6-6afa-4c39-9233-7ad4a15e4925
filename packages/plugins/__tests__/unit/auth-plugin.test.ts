/**
 * Auth 插件测试
 */

import { EventBus } from '@micro-core/core';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { AuthPlugin } from '../plugin-auth/src';

// 模拟依赖
vi.mock('@micro-core/core', () => ({
    EventBus: class MockEventBus {
        on = vi.fn();
        off = vi.fn();
        emit = vi.fn();
        once = vi.fn();
    }
}));

describe('AuthPlugin', () => {
    let authPlugin: AuthPlugin;
    let mockEventBus: EventBus;
    let mockConfig: any;

    beforeEach(() => {
        // 重置所有模拟
        vi.resetAllMocks();

        // 创建模拟事件总线
        mockEventBus = new EventBus();

        // 创建模拟配置
        mockConfig = {
            loginUrl: '/login',
            logoutUrl: '/logout',
            tokenStorage: 'localStorage',
            tokenKey: 'auth_token',
            autoRefresh: true,
            refreshInterval: 300000, // 5分钟
            onAuthSuccess: vi.fn(),
            onAuthFailure: vi.fn(),
            onAuthExpired: vi.fn()
        };

        // 创建插件实例
        authPlugin = new AuthPlugin();
    });

    afterEach(() => {
        vi.clearAllMocks();
        localStorage.clear();
        sessionStorage.clear();
    });

    describe('插件初始化', () => {
        it('应该能够正确初始化插件', async () => {
            await authPlugin.install({ eventBus: mockEventBus }, mockConfig);
            expect(mockEventBus.on).toHaveBeenCalled();
        });

        it('应该在没有配置时使用默认配置', async () => {
            await authPlugin.install({ eventBus: mockEventBus }, {});
            expect(mockEventBus.on).toHaveBeenCalled();
        });

        it('应该在初始化时设置事件监听器', async () => {
            await authPlugin.install({ eventBus: mockEventBus }, mockConfig);
            expect(mockEventBus.on).toHaveBeenCalledWith('auth:login', expect.any(Function));
            expect(mockEventBus.on).toHaveBeenCalledWith('auth:logout', expect.any(Function));
            expect(mockEventBus.on).toHaveBeenCalledWith('auth:check', expect.any(Function));
        });
    });

    describe('身份验证操作', () => {
        it('应该能够处理登录请求', async () => {
            await authPlugin.install({ eventBus: mockEventBus }, mockConfig);

            // 模拟登录事件处理函数
            const loginHandler = (mockEventBus.on as any).mock.calls.find(
                call => call[0] === 'auth:login'
            )[1];

            // 模拟登录凭据
            const credentials = { username: 'testuser', password: 'password' };
            const token = 'test_token';

            // 模拟 fetch API
            global.fetch = vi.fn().mockResolvedValue({
                ok: true,
                json: () => Promise.resolve({ token })
            });

            // 调用登录处理函数
            await loginHandler(credentials);

            // 验证 fetch 调用
            expect(global.fetch).toHaveBeenCalledWith(mockConfig.loginUrl, expect.any(Object));

            // 验证 token 存储
            expect(localStorage.getItem(mockConfig.tokenKey)).toBe(token);

            // 验证事件发出
            expect(mockEventBus.emit).toHaveBeenCalledWith('auth:login:success', expect.any(Object));
            expect(mockConfig.onAuthSuccess).toHaveBeenCalled();
        });

        it('应该能够处理登录失败', async () => {
            await authPlugin.install({ eventBus: mockEventBus }, mockConfig);

            // 模拟登录事件处理函数
            const loginHandler = (mockEventBus.on as any).mock.calls.find(
                call => call[0] === 'auth:login'
            )[1];

            // 模拟登录凭据
            const credentials = { username: 'testuser', password: 'wrong_password' };

            // 模拟 fetch API 失败
            global.fetch = vi.fn().mockResolvedValue({
                ok: false,
                status: 401,
                json: () => Promise.resolve({ error: '认证失败' })
            });

            // 调用登录处理函数
            await loginHandler(credentials);

            // 验证 fetch 调用
            expect(global.fetch).toHaveBeenCalledWith(mockConfig.loginUrl, expect.any(Object));

            // 验证事件发出
            expect(mockEventBus.emit).toHaveBeenCalledWith('auth:login:failure', expect.any(Object));
            expect(mockConfig.onAuthFailure).toHaveBeenCalled();
        });

        it('应该能够处理登出请求', async () => {
            await authPlugin.install({ eventBus: mockEventBus }, mockConfig);

            // 模拟登出事件处理函数
            const logoutHandler = (mockEventBus.on as any).mock.calls.find(
                call => call[0] === 'auth:logout'
            )[1];

            // 设置 token
            localStorage.setItem(mockConfig.tokenKey, 'test_token');

            // 模拟 fetch API
            global.fetch = vi.fn().mockResolvedValue({
                ok: true,
                json: () => Promise.resolve({ success: true })
            });

            // 调用登出处理函数
            await logoutHandler();

            // 验证 fetch 调用
            expect(global.fetch).toHaveBeenCalledWith(mockConfig.logoutUrl, expect.any(Object));

            // 验证 token 移除
            expect(localStorage.getItem(mockConfig.tokenKey)).toBeNull();

            // 验证事件发出
            expect(mockEventBus.emit).toHaveBeenCalledWith('auth:logout:success');
        });

        it('应该能够检查认证状态', async () => {
            await authPlugin.install({ eventBus: mockEventBus }, mockConfig);

            // 模拟检查认证事件处理函数
            const checkHandler = (mockEventBus.on as any).mock.calls.find(
                call => call[0] === 'auth:check'
            )[1];

            // 设置 token
            localStorage.setItem(mockConfig.tokenKey, 'test_token');

            // 调用检查处理函数
            const callback = vi.fn();
            await checkHandler(callback);

            // 验证回调调用
            expect(callback).toHaveBeenCalledWith(true, 'test_token');
        });
    });

    describe('令牌管理', () => {
        it('应该能够在 localStorage 中存储令牌', async () => {
            mockConfig.tokenStorage = 'localStorage';
            await authPlugin.install({ eventBus: mockEventBus }, mockConfig);

            // 模拟登录事件处理函数
            const loginHandler = (mockEventBus.on as any).mock.calls.find(
                call => call[0] === 'auth:login'
            )[1];

            // 模拟登录凭据和响应
            const credentials = { username: 'testuser', password: 'password' };
            const token = 'test_token';

            global.fetch = vi.fn().mockResolvedValue({
                ok: true,
                json: () => Promise.resolve({ token })
            });

            // 调用登录处理函数
            await loginHandler(credentials);

            // 验证 token 存储在 localStorage
            expect(localStorage.getItem(mockConfig.tokenKey)).toBe(token);
        });

        it('应该能够在 sessionStorage 中存储令牌', async () => {
            mockConfig.tokenStorage = 'sessionStorage';
            await authPlugin.install({ eventBus: mockEventBus }, mockConfig);

            // 模拟登录事件处理函数
            const loginHandler = (mockEventBus.on as any).mock.calls.find(
                call => call[0] === 'auth:login'
            )[1];

            // 模拟登录凭据和响应
            const credentials = { username: 'testuser', password: 'password' };
            const token = 'test_token';

            global.fetch = vi.fn().mockResolvedValue({
                ok: true,
                json: () => Promise.resolve({ token })
            });

            // 调用登录处理函数
            await loginHandler(credentials);

            // 验证 token 存储在 sessionStorage
            expect(sessionStorage.getItem(mockConfig.tokenKey)).toBe(token);
        });

        it('应该能够处理令牌刷新', async () => {
            mockConfig.autoRefresh = true;
            mockConfig.refreshInterval = 100; // 100ms，便于测试
            await authPlugin.install({ eventBus: mockEventBus }, mockConfig);

            // 设置 token
            localStorage.setItem(mockConfig.tokenKey, 'old_token');

            // 模拟 fetch API 用于刷新
            global.fetch = vi.fn().mockResolvedValue({
                ok: true,
                json: () => Promise.resolve({ token: 'new_token' })
            });

            // 等待刷新
            await new Promise(resolve => setTimeout(resolve, 150));

            // 验证 fetch 调用
            expect(global.fetch).toHaveBeenCalled();

            // 验证 token 更新
            expect(localStorage.getItem(mockConfig.tokenKey)).toBe('new_token');
        });
    });

    describe('插件卸载', () => {
        it('应该能够正确卸载插件', async () => {
            await authPlugin.install({ eventBus: mockEventBus }, mockConfig);
            await authPlugin.uninstall({ eventBus: mockEventBus });

            expect(mockEventBus.off).toHaveBeenCalled();
        });

        it('应该在卸载时移除事件监听器', async () => {
            await authPlugin.install({ eventBus: mockEventBus }, mockConfig);
            await authPlugin.uninstall({ eventBus: mockEventBus });

            expect(mockEventBus.off).toHaveBeenCalledWith('auth:login', expect.any(Function));
            expect(mockEventBus.off).toHaveBeenCalledWith('auth:logout', expect.any(Function));
            expect(mockEventBus.off).toHaveBeenCalledWith('auth:check', expect.any(Function));
        });

        it('应该在卸载时清除定时器', async () => {
            mockConfig.autoRefresh = true;
            await authPlugin.install({ eventBus: mockEventBus }, mockConfig);

            const clearIntervalSpy = vi.spyOn(global, 'clearInterval');
            await authPlugin.uninstall({ eventBus: mockEventBus });

            expect(clearIntervalSpy).toHaveBeenCalled();
        });
    });
});